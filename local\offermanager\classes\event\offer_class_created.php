<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mood<PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\event;

use local_offermanager\persistent\offer_class_model;
use context_system;

/**
 * Event offer_class_created
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class offer_class_created extends \core\event\base {

    /**
     * Set basic properties for the event.
     */
    protected function init() {
        $this->data['objecttable'] = 'local_offermanager_class';
        $this->data['crud'] = 'c';
        $this->data['edulevel'] = self::LEVEL_OTHER;
    }

    /**
     * Método estático para criar uma instância do evento.
     *
     * @param offer_class_model $offercourse.
     * @return self
     */
    public static function instance(offer_class_model $offerclass)
    {
        $data = [
            'objectid' => $offerclass->get('id'),
            'context' => context_system::instance(),
            'other' => [
                'offercourseid' => $offerclass->get('offercourseid'),
                'enrolid' => $offerclass->get('enrolid'),
            ],
        ];

        $event = self::create($data);
        return $event;
    }
    
    public static function get_name()
    {
        return get_string('event:offerclasscreated', 'local_offermanager');
    }

    public function get_description()
    {
        return "O usuário com id '{$this->userid}' criou a turma com id '{$this->objectid}'.";
    }
}
