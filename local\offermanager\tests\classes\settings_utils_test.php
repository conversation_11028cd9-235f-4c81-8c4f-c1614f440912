<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\classes;

use local_offermanager\settings_utils;

/**
 * Tests for Offer Manager
 *
 * @package    local_offermanager
 * @category   test
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
final class settings_utils_test extends \advanced_testcase {
    public function test_get_type_options() {
        $this->resetAfterTest(true);

        set_config('typeoptions', "online\npresencial\nhibrido", 'local_offermanager');
        $expected = ['online', 'presencial', 'hibrido'];
        $this->assertEquals($expected, settings_utils::get_type_options());

        set_config('typeoptions', '', 'local_offermanager');
        $this->assertEquals([], settings_utils::get_type_options());

        set_config('typeoptions', " online \n presencial \n ", 'local_offermanager');
        $expected = ['online', 'presencial'];
        $this->assertEquals($expected, settings_utils::get_type_options());
    }

    public function test_get_type_default() {
        $this->resetAfterTest(true);
    
        set_config('defaulttypeoption', 'online', 'local_offermanager');
        $this->assertEquals('online', settings_utils::get_type_default());
    
        unset_config('defaulttypeoption', 'local_offermanager');
        $this->assertFalse(settings_utils::get_type_default());
    }
}
