<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\classes\persistent;

use local_offermanager\persistent\offer_user_enrol_model;
use advanced_testcase;
use stdClass;

/**
 * Tests for Offer Manager User Enrolment Model
 *
 * @package    local_offermanager
 * @category   test
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
final class offer_user_enrol_model_test extends \advanced_testcase {

    /**
     * Test creating a valid enrolment record.
     */
    public function test_create_valid_enrolment(): void {
        $this->resetAfterTest();

        $user = $this->getDataGenerator()->create_user();
        $course = $this->getDataGenerator()->create_course();

        // Create an enrolment record.
        $enrolmentData = [
            'offerclassid' => 1, // Example offer class ID.
            'ueid' => 1, // Example user enrolment ID.
            'userid' => $user->id,
            'courseid' => $course->id,
            'situation' => 1, // Example situation.
            'usercreated' => $user->id,
        ];

        $enrolment = new offer_user_enrol_model(0, (object)$enrolmentData);
        $enrolment->save();

        // Assert that the record was saved successfully.
        $this->assertGreaterThan(0, $enrolment->get('id'));
        $this->assertEquals($user->id, $enrolment->get('userid'));
        $this->assertEquals($course->id, $enrolment->get('courseid'));
    }

    /**
     * Test cancelling an enrolment.
     */
    public function test_set_canceled(): void {
        $this->resetAfterTest();

        // Create a user and course for testing.
        $user = $this->getDataGenerator()->create_user();
        $course = $this->getDataGenerator()->create_course();

        // Create an enrolment record.
        $enrolmentData = [
            'offerclassid' => 1,
            'ueid' => 1,
            'userid' => $user->id,
            'courseid' => $course->id,
            'situation' => 1,
            'usercreated' => $user->id,
        ];

        $enrolment = new offer_user_enrol_model(0, (object)$enrolmentData);
        $enrolment->save();

        // Cancel the enrolment.
        $cancelDescription = "Motivo pessoal";
        $enrolment->set_canceled(1, $cancelDescription, $user->id);

        // Reload the enrolment to verify changes.
        $updatedEnrolment = new offer_user_enrol_model($enrolment->get('id'));

        // Assert cancellation details.
        $this->assertEquals(1, $updatedEnrolment->get('self_canceled'));
        $this->assertEquals($cancelDescription, $updatedEnrolment->get_cancellation_reason());
        $this->assertEquals($user->id, $updatedEnrolment->get('usercanceled'));
        $this->assertGreaterThan(0, $updatedEnrolment->get('timecanceled'));
    }

    /**
     * Test checking if an enrolment is canceled.
     */
    public function test_is_canceled(): void {
        $this->resetAfterTest();

        // Create a user and course for testing.
        $user = $this->getDataGenerator()->create_user();
        $course = $this->getDataGenerator()->create_course();

        // Create an enrolment record.
        $enrolmentData = [
            'offerclassid' => 1,
            'ueid' => 1,
            'userid' => $user->id,
            'courseid' => $course->id,
            'situation' => 1,
            'usercreated' => $user->id,
        ];

        $enrolment = new offer_user_enrol_model(0, (object)$enrolmentData);
        $enrolment->save();

        // Initially, the enrolment should not be canceled.
        $this->assertFalse($enrolment->is_canceled());

        // Cancel the enrolment.
        $enrolment->set_canceled(1, "Motivo pessoal", $user->id);

        // Reload the enrolment to verify changes.
        $updatedEnrolment = new offer_user_enrol_model($enrolment->get('id'));

        // After cancellation, the enrolment should be marked as canceled.
        $this->assertTrue($updatedEnrolment->is_canceled());
    }

    /**
     * Test retrieving cancellation details.
     */
    public function test_get_cancellation_details(): void {
        $this->resetAfterTest();

        // Create a user and course for testing.
        $user = $this->getDataGenerator()->create_user();
        $course = $this->getDataGenerator()->create_course();

        // Create an enrolment record.
        $enrolmentData = [
            'offerclassid' => 1,
            'ueid' => 1,
            'userid' => $user->id,
            'courseid' => $course->id,
            'situation' => 1,
            'usercreated' => $user->id,
        ];

        $enrolment = new offer_user_enrol_model(0, (object)$enrolmentData);
        $enrolment->save();

        // Cancel the enrolment.
        $cancelDescription = "Motivo pessoal";
        $enrolment->set_canceled(1, $cancelDescription, $user->id);

        // Retrieve cancellation details.
        $details = $enrolment->get_cancellation_details();

        // Assert cancellation details.
        $this->assertEquals(1, $details->self_canceled);
        $this->assertEquals($cancelDescription, $details->cancel_description);
        $this->assertEquals($user->id, $details->usercanceled);
        $this->assertGreaterThan(0, $details->timecanceled);
    }
}