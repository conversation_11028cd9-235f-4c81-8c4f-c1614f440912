<template>
  <div v-if="show" class="modal-backdrop" @click="$emit('close')">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h3 class="modal-title">Duplicar Turma "{{ turma?.nome }}"</h3>
        <button class="close-button" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <h3 class="section-title">SELECIONAR CURSO</h3>

        <div class="search-section">
          <div class="search-group">
            <Autocomplete
              v-model="selectedCategory"
              :items="categoryOptions"
              label="Categoria"
              placeholder="Pesquisar..."
              :input-max-width="null"
              :loading="loadingCategories"
              :show-filter-tags="false"
              :show-selected-in-input="true"
              :auto-open="true"
              :has-search-icon="true"
              :max-label-length="25"
              :no-results-text="
                categoryOptions.length === 0
                  ? 'Nenhuma categoria disponível'
                  : 'Nenhuma categoria encontrada'
              "
              @select="handleCategorySelect"
            />
          </div>

          <div class="search-group">
            <Autocomplete
              v-model="selectedCourse"
              :items="targetCourseOptions"
              label="Curso"
              placeholder="Pesquisar..."
              :input-max-width="null"
              :disabled="!selectedCategory"
              :loading="loadingCourses || loadingMoreCourses"
              :auto-open="true"
              :has-search-icon="true"
              :max-label-length="25"
              :no-results-text="courseNoResultsText"
              @select="handleCourseSelect"
              @load-more="loadMoreCourses"
              @search="handleCourseSearch"
              ref="courseAutocomplete"
            />
          </div>
        </div>

        <div class="table-container">
          <div
            v-if="selectedCoursesPreview.length === 0"
            class="empty-preview-message"
          >
            <p>Selecione cursos acima para duplicar a turma</p>
          </div>
          <CustomTable
            v-else
            :headers="tableHeaders"
            :items="filteredCourses"
            :sort-by="sortBy"
            :sort-desc="sortDesc"
            @sort="handleTableSort"
          >
            <template #item-actions="{ item }">
              <div class="action-buttons">
                <button
                  class="btn-action btn-delete"
                  @click="removeCourse(item)"
                  title="Remover da lista"
                >
                  <i class="fa fa-trash fa-fw"></i>
                </button>
              </div>
            </template>
          </CustomTable>
        </div>

        <Pagination
          v-if="selectedCoursesPreview.length > 0"
          v-model:current-page="currentPage"
          v-model:per-page="perPage"
          :total="selectedCoursesPreview.length"
        />
      </div>
      <div class="modal-footer">
        <button
          class="btn-primary"
          @click="handleConfirm"
          :disabled="selectedCoursesPreview.length === 0"
        >
          Duplicar
        </button>
        <button class="btn-secondary" @click="$emit('close')">Cancelar</button>
      </div>
    </div>
  </div>
</template>

<script>
import Autocomplete from "./Autocomplete.vue";
import CustomTable from "./CustomTable.vue";
import Pagination from "./Pagination.vue";
import {
  getPotentialDuplicationCourses,
  duplicateClass,
  getCategories,
  getCoursesByCategory,
} from "@/services/offer";

export default {
  name: "DuplicateClassModal",
  components: {
    Autocomplete,
    CustomTable,
    Pagination,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    turma: {
      type: Object,
      default: null,
    },
    parentCourse: {
      type: Object,
      default: null,
    },
    offerId: {
      type: [Number, String],
      default: null,
    },
  },
  emits: ["close", "success", "loading", "error"],
  data() {
    return {
      // Seleção de categoria
      selectedCategory: null,
      selectedCategoryObject: null,
      categoryOptions: [],
      loadingCategories: false,

      // Seleção de curso
      selectedCourse: null,
      targetCourseOptions: [],
      selectedCoursesPreview: [],
      loadingCourses: false,
      loadingMoreCourses: false,

      // Paginação de cursos potenciais
      coursesPage: 1,
      coursesPerPage: 20,
      coursesTotalPages: 1,
      hasMoreCourses: false,

      // Paginação da tabela de pré-visualização
      currentPage: 1,
      perPage: 5,
      sortBy: "label",
      sortDesc: false,
      tableHeaders: [
        { text: "CURSO", value: "label", sortable: true },
        { text: "CATEGORIA", value: "category_name", sortable: true },
        { text: "AÇÕES", value: "actions", sortable: false, align: "right" },
      ],

      // Duplicação
      duplicatingCourses: false,
      duplicatedCount: 0,
      totalToDuplicate: 0,

      // Cursos existentes na oferta
      existingCourses: [],
    };
  },

  computed: {
    // Texto a ser exibido quando não há resultados no autocomplete de cursos
    courseNoResultsText() {
      if (!this.selectedCategory) {
        return "Selecione uma categoria primeiro";
      }
      if (this.loadingCourses || this.loadingMoreCourses) {
        return "Carregando cursos...";
      }
      return this.targetCourseOptions.length === 0
        ? "Nenhum curso disponível"
        : "Nenhum curso encontrado";
    },

    filteredCourses() {
      // Aplicar paginação
      const startIndex = (this.currentPage - 1) * this.perPage;
      const endIndex = startIndex + this.perPage;

      // Aplicar ordenação
      const sortedCourses = [...this.selectedCoursesPreview].sort((a, b) => {
        const aValue = a[this.sortBy];
        const bValue = b[this.sortBy];

        if (aValue < bValue) return this.sortDesc ? 1 : -1;
        if (aValue > bValue) return this.sortDesc ? -1 : 1;
        return 0;
      });

      return sortedCourses.slice(startIndex, endIndex);
    },
  },
  watch: {
    show(newVal) {
      if (newVal && this.turma && this.parentCourse) {
        // Resetar o formulário antes de carregar os cursos
        this.resetForm();
        // Carregar as categorias disponíveis
        this.$nextTick(() => {
          this.loadAllCategories();
        });
      } else {
        this.resetForm();
      }
    },
    // Observar mudanças na turma ou no curso pai
    turma() {
      if (this.show) {
        this.resetForm();
        this.loadAllCategories();
      }
    },
    parentCourse() {
      if (this.show) {
        this.resetForm();
        this.loadAllCategories();
      }
    },
    // Observar mudanças na categoria selecionada
    selectedCategory(newValue) {
      // Se o usuário limpar a seleção, limpar as opções de cursos
      if (!newValue) {
        this.targetCourseOptions = [];
        this.selectedCourse = null;
        this.selectedCategoryObject = null;
      } else {
        // Carregar cursos para a categoria selecionada
        this.loadCoursesForCategory(newValue);
      }
    },
  },
  methods: {
    resetForm() {
      // Resetar seleção de categoria
      this.selectedCategory = null;
      this.selectedCategoryObject = null;
      this.categoryOptions = [];
      this.loadingCategories = false;

      // Resetar seleção de curso
      this.selectedCourse = null;
      this.targetCourseOptions = [];
      this.selectedCoursesPreview = [];
      this.loadingCourses = false;
      this.loadingMoreCourses = false;

      // Resetar paginação de cursos
      this.coursesPage = 1;
      this.coursesPerPage = 20;
      this.coursesTotalPages = 1;
      this.hasMoreCourses = false;

      // Resetar paginação da tabela
      this.currentPage = 1;

      // Resetar estado de duplicação
      this.duplicatingCourses = false;
      this.duplicatedCount = 0;
      this.totalToDuplicate = 0;

      // Limpar cursos existentes
      this.existingCourses = [];
    },

    // Carrega todas as categorias
    async loadAllCategories() {
      try {
        this.loadingCategories = true;
        this.categoryOptions = []; // Limpa antes de carregar

        // Buscar todas as categorias, filtrando pela oferta
        // Usar o ID da oferta passado como prop, que é o ID correto da oferta
        const response = await getCategories("", this.offerId);

        if (response && response.data) {
          this.categoryOptions = response.data.map((category) => ({
            value: category.id,
            label: category.name,
          }));
        }
      } catch (error) {
        console.error("Erro ao carregar categorias:", error);
        this.$emit(
          "error",
          "Erro ao carregar categorias. Por favor, tente novamente."
        );
        this.categoryOptions = []; // Garante que esteja vazio em caso de erro
      } finally {
        this.loadingCategories = false;
      }
    },

    // Chamado quando uma categoria é selecionada no Autocomplete
    handleCategorySelect(category) {
      if (!category) {
        this.removeCategory(); // Limpa se a seleção for removida
        return;
      }

      // Armazenamos o objeto completo para referência
      this.selectedCategoryObject = category;
      // Para o v-model do Autocomplete, armazenamos apenas o value
      this.selectedCategory = category.value;

      // Limpa opções e seleção de curso anterior
      this.targetCourseOptions = [];
      this.selectedCourse = null;

      // Reseta a paginação
      this.coursesPage = 1;
      this.coursesTotalPages = 1;
      this.hasMoreCourses = false;
    },

    // Remove a categoria selecionada
    removeCategory() {
      this.selectedCategory = null;
      this.selectedCategoryObject = null;
      this.selectedCourse = null; // Limpa seleção de curso
      this.targetCourseOptions = []; // Limpa opções de curso

      // Reseta a paginação
      this.coursesPage = 1;
      this.coursesTotalPages = 1;
      this.hasMoreCourses = false;
    },

    // Carrega cursos para uma categoria específica
    async loadCoursesForCategory(
      categoryId,
      page = 1,
      append = false,
      searchText = ""
    ) {
      if (!categoryId || !this.turma) return;

      // Garantir que estamos usando o ID correto da categoria
      const id = typeof categoryId === "object" ? categoryId.value : categoryId;

      try {
        if (page === 1) {
          this.loadingCourses = true;
          if (!append) {
            this.targetCourseOptions = []; // Limpa antes de carregar apenas se não estiver anexando
          }
        } else {
          this.loadingMoreCourses = true;
        }

        // Buscar cursos disponíveis para duplicação
        // Usar o ID da turma para buscar os cursos potenciais para duplicação
        // O backend já filtra os cursos pela oferta correta
        const response = await getPotentialDuplicationCourses(this.turma.id);

        // Extrair os cursos da resposta, considerando diferentes formatos possíveis
        let coursesData = [];

        // Caso 1: Resposta é um array de cursos diretamente
        if (response && Array.isArray(response)) {
          coursesData = response;
        }

        // Filtrar o curso atual da turma para não permitir duplicação para o mesmo curso
        const currentOfferCourseId =
          this.parentCourse.offerCourseId || this.parentCourse.id;

        // Filtrar cursos por categoria
        coursesData = coursesData.filter((course) => {
          // Verificar se o curso pertence à categoria selecionada
          // Agora usamos o campo categoryid que é retornado diretamente pelo endpoint
          const courseCategory = course.categoryid;
          const belongsToCategory = String(courseCategory) === String(id);

          // Verificar se não é o curso atual da turma
          const offerCourseId = course.offercourseid || course.id;
          const notCurrentCourse =
            String(offerCourseId) !== String(currentOfferCourseId);

          // Verificar se corresponde ao termo de busca (se houver)
          const matchesSearch =
            !searchText ||
            (course.name &&
              course.name.toLowerCase().includes(searchText.toLowerCase())) ||
            (course.fullname &&
              course.fullname.toLowerCase().includes(searchText.toLowerCase()));

          return belongsToCategory && notCurrentCourse && matchesSearch;
        });

        // Filtrar cursos que já foram selecionados
        coursesData = coursesData.filter((course) => {
          const courseId = course.offercourseid || course.id;
          return !this.selectedCoursesPreview.some(
            (c) => String(c.value) === String(courseId)
          );
        });

        // Mapear os cursos para o formato esperado pelo Autocomplete
        const newCourseOptions = coursesData
          .map((course) => {
            let value = course.offercourseid || course.id;

            if (value === undefined || value === null) {
              return null; // Será filtrado abaixo
            }

            const option = {
              value: value,
              label:
                course.name ||
                course.fullname ||
                course.coursename ||
                `Curso ${value}`,
              categoryid: course.categoryid,
              category_name: course.category_name,
            };

            return option;
          })
          .filter((option) => option !== null); // Remover opções inválidas

        // Atualizar as opções de cursos
        if (append) {
          // Adiciona os novos cursos aos existentes
          this.targetCourseOptions = [
            ...this.targetCourseOptions,
            ...newCourseOptions,
          ];
        } else {
          // Substitui completamente a lista
          this.targetCourseOptions = newCourseOptions;
        }

        // Verificar se há mais cursos para carregar
        this.hasMoreCourses = newCourseOptions.length >= this.coursesPerPage;

        // Atualizar a página atual
        if (page > this.coursesPage) {
          this.coursesPage = page;
        }
      } catch (error) {
        console.error("Erro ao carregar cursos da categoria:", error);
        this.$emit(
          "error",
          "Erro ao carregar cursos. Por favor, tente novamente."
        );
        if (!append) {
          this.targetCourseOptions = []; // Garante que esteja vazio em caso de erro apenas se não estiver anexando
        }
      } finally {
        if (page === 1) {
          this.loadingCourses = false;
        } else {
          this.loadingMoreCourses = false;
        }
      }
    },

    // Carrega mais cursos (próxima página)
    async loadMoreCourses() {
      if (
        this.hasMoreCourses &&
        !this.loadingMoreCourses &&
        !this.loadingCourses
      ) {
        const nextPage = this.coursesPage + 1;
        await this.loadCoursesForCategory(
          this.selectedCategory,
          nextPage,
          true
        );
      }
    },

    // Manipula a busca por texto no autocomplete de cursos
    async handleCourseSearch(searchText) {
      if (!this.selectedCategory) return;

      // Reseta a paginação para a primeira página
      this.coursesPage = 1;
      this.coursesTotalPages = 1;
      this.hasMoreCourses = false;

      // Carrega cursos com o termo de busca (ou sem termo se searchText for vazio)
      await this.loadCoursesForCategory(
        this.selectedCategory,
        1,
        false,
        searchText || ""
      );
    },

    // Método para tratar a seleção do curso no Autocomplete
    handleCourseSelect(course) {
      if (
        course &&
        !this.selectedCoursesPreview.some((c) => c.value === course.value)
      ) {
        // Adiciona o curso à lista de pré-visualização se ainda não estiver lá
        this.selectedCoursesPreview.push({
          value: course.value,
          label: course.label,
          categoryid: course.categoryid,
          category_name: course.category_name,
        });

        // Atualiza as opções de cursos para remover o curso selecionado
        this.targetCourseOptions = this.targetCourseOptions.filter(
          (c) => c.value !== course.value
        );
      }

      // Limpa a seleção do autocomplete de curso após adicionar
      this.selectedCourse = null;
    },

    // Método para remover um curso da lista de pré-visualização
    removeCourse(course) {
      const courseIndex = this.selectedCoursesPreview.findIndex(
        (c) => c.value === course.value
      );
      if (courseIndex !== -1) {
        // Remove o curso da lista de pré-visualização
        const removedCourse = this.selectedCoursesPreview.splice(
          courseIndex,
          1
        )[0];

        // Adiciona o curso de volta às opções disponíveis
        this.targetCourseOptions.push(removedCourse);
      }
    },

    // Método para ordenar a tabela
    handleTableSort({ sortBy, sortDesc }) {
      this.sortBy = sortBy;
      this.sortDesc = sortDesc;
    },

    async handleConfirm() {
      if (!this.turma || this.selectedCoursesPreview.length === 0) return;

      try {
        this.$emit("loading", true);
        const turmaNome = this.turma.nome;
        const turmaId = parseInt(this.turma.id, 10);

        if (isNaN(turmaId)) {
          throw new Error("ID da turma inválido");
        }

        // Iniciar o processo de duplicação para todos os cursos selecionados
        this.duplicatingCourses = true;
        this.totalToDuplicate = this.selectedCoursesPreview.length;
        this.duplicatedCount = 0;

        // Array para armazenar resultados das duplicações
        const duplicatedResults = [];

        // Processar cada curso selecionado sequencialmente
        for (const course of this.selectedCoursesPreview) {
          const cursoDestinoId = parseInt(course.value, 10);

          if (isNaN(cursoDestinoId)) {
            console.error(`ID inválido para o curso ${course.label}`);
            continue; // Pular este curso e continuar com o próximo
          }

          try {
            // Atualizar o contador para feedback visual
            this.$emit(
              "loading",
              true,
              `Duplicando para ${course.label} (${this.duplicatedCount + 1}/${this.totalToDuplicate})`
            );

            // Chamar a API para duplicar a turma para este curso
            const result = await duplicateClass(turmaId, cursoDestinoId);

            // Adicionar ao array de resultados
            duplicatedResults.push({
              turmaNome,
              targetCourseName: course.label,
              turmaId,
              targetCourseId: cursoDestinoId,
              result,
            });

            // Incrementar o contador de sucesso
            this.duplicatedCount++;
          } catch (courseError) {
            // Registrar erro para este curso específico, mas continuar com os outros
            console.error(
              `Erro ao duplicar para o curso ${course.label}:`,
              courseError
            );
            this.$emit(
              "error",
              `Erro ao duplicar para o curso ${course.label}: ${courseError.message}`
            );
          }
        }

        // Finalizar o processo de duplicação
        this.duplicatingCourses = false;

        // Emitir evento de sucesso com todos os resultados
        if (duplicatedResults.length > 0) {
          this.$emit("success", {
            turmaNome,
            duplicatedCount: duplicatedResults.length,
            totalSelected: this.totalToDuplicate,
            results: duplicatedResults,
          });

          // Limpar o formulário e fechar o modal
          this.resetForm();
          this.$emit("close");
        } else {
          throw new Error("Nenhuma turma foi duplicada com sucesso.");
        }
      } catch (error) {
        this.$emit("error", error.message || "Erro ao duplicar turmas.");
      } finally {
        this.duplicatingCourses = false;
        this.$emit("loading", false);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.modal-container {
  background-color: #212529;
  border-radius: 6px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  max-height: 90vh;
  width: 100%;
  max-width: 800px; // Aumentado para acomodar a tabela
  border: 1px solid #373b3e;
  z-index: 10001;
  margin: 1rem;

  @media (max-width: 767px) {
    max-width: 95vw;
    max-height: 95vh;
    margin: 0.5rem;
    border-radius: 8px;
  }

  @media (max-width: 480px) {
    max-width: 98vw;
    max-height: 98vh;
    margin: 0.25rem;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #343a40;

  @media (max-width: 480px) {
    padding: 0.75rem;
  }
}

.modal-title {
  margin: 0;
  font-weight: bold;
  font-size: 1.25rem;
  color: #fff;

  @media (max-width: 480px) {
    font-size: 1.1rem;
  }
}

.close-button {
  background: transparent;
  border: none;
  color: #6c757d;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.5rem;
  min-width: 44px;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: var(--primary);
  }

  @media (max-width: 480px) {
    font-size: 1.1rem;
    min-width: 40px;
    min-height: 40px;
  }
}

.modal-body {
  padding: 1rem;
  max-height: 70vh;
  overflow-y: auto;

  @media (max-width: 480px) {
    padding: 0.75rem;
  }
}

.modal-message {
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.modal-info {
  margin-bottom: 1rem;
  font-size: 0.9rem;
  color: #adb5bd;
}

.section-title {
  color: var(--primary);
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 1rem;

  @media (max-width: 480px) {
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
  }
}

.search-section {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;

  @media (max-width: 767px) {
    flex-direction: column;
    gap: 0.75rem;
  }

  @media (max-width: 480px) {
    gap: 0.5rem;
    margin-bottom: 0.75rem;
  }
}

.search-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;

  @media (max-width: 767px) {
    flex: none;
    width: 100%;
  }
}

.table-container {
  margin-bottom: 1rem;

  .empty-preview-message {
    background-color: #343a40;
    padding: 2rem;
    text-align: center;
    border-radius: 4px;

    p {
      color: #adb5bd;
      font-style: italic;
      margin: 0;
    }
  }
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.btn-action {
  background-color: transparent;
  border: none;
  color: #fff;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;

  &.btn-delete {
    color: #dc3545;

    &:hover {
      background-color: rgba(220, 53, 69, 0.1);
    }
  }

  i {
    font-size: 1.25rem;
  }

  @media (max-width: 480px) {
    padding: 0.375rem;
    min-width: 36px;
    min-height: 36px;

    i {
      font-size: 1rem;
    }
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1rem;
  border-top: 1px solid #343a40;

  @media (max-width: 767px) {
    flex-direction: column-reverse;
    gap: 0.75rem;

    .btn-primary,
    .btn-secondary {
      width: 100%;
      justify-content: center;
    }
  }

  @media (max-width: 480px) {
    padding: 0.75rem;
    gap: 0.5rem;
  }
}

.btn-primary {
  background-color: var(--primary);
  color: #fff;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;

  &:disabled {
    opacity: 0.65;
    cursor: not-allowed;
  }
}

.btn-secondary {
  background-color: #6c757d;
  color: #fff;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background-color: #5c636a;
  }
}

.alert-info {
  background-color: #032830;
  border: 1px solid #087990;
  color: #6edff6;
  padding: 0.75rem 1rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  i {
    font-size: 1rem;
  }
}

.mt-3 {
  margin-top: 1rem;
}

.form-label {
  font-weight: 500;
  color: #e9ecef;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  display: block;
}
</style>

<style lang="scss">
/* Estilos responsivos para componentes internos */
.modal-container {
  /* Autocomplete responsivo */
  :deep(.autocomplete-wrapper) {
    width: 100%;

    .input-container {
      width: 100%;
      max-width: none;

      input {
        width: 100%;
        max-width: none;
      }
    }
  }

  /* Tabela responsiva */
  :deep(.custom-table) {
    @media (max-width: 767px) {
      font-size: 0.875rem;

      th, td {
        padding: 0.5rem 0.25rem;
      }

      .action-buttons {
        justify-content: center;
      }
    }

    @media (max-width: 480px) {
      font-size: 0.8rem;

      th, td {
        padding: 0.375rem 0.125rem;
      }
    }
  }

  /* Paginação responsiva */
  :deep(.pagination) {
    @media (max-width: 767px) {
      flex-wrap: wrap;
      justify-content: center;
      gap: 0.25rem;

      .pagination-info {
        order: -1;
        width: 100%;
        text-align: center;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
      }
    }

    @media (max-width: 480px) {
      .pagination-controls {
        flex-wrap: wrap;
        justify-content: center;
      }

      .pagination-info {
        font-size: 0.8rem;
      }
    }
  }

  /* Botões de ação responsivos */
  :deep(.btn-action) {
    @media (max-width: 480px) {
      padding: 0.375rem;
      min-width: 36px;
      min-height: 36px;

      i {
        font-size: 1rem;
      }
    }
  }
}

.modal-container .custom-input {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}
</style>
