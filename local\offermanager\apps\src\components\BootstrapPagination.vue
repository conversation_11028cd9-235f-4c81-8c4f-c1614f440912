<template>
  <nav aria-label="Page navigation">
    <ul class="pagination justify-content-center">
      <li class="page-item" :class="{ disabled: currentPage <= 1 }">
        <button class="page-link" @click="$emit('change', currentPage - 1)" :disabled="currentPage <= 1">
          <i class="fas fa-chevron-left"></i>
        </button>
      </li>

      <li v-for="page in pages" :key="page" class="page-item" :class="{ active: page === currentPage }">
        <button class="page-link" @click="$emit('change', page)">{{ page }}</button>
      </li>

      <li class="page-item" :class="{ disabled: currentPage >= totalPages }">
        <button class="page-link" @click="$emit('change', currentPage + 1)" :disabled="currentPage >= totalPages">
          <i class="fas fa-chevron-right"></i>
        </button>
      </li>
    </ul>
  </nav>
</template>

<script>
export default {
  name: 'BootstrapPagination',
  
  props: {
    currentPage: {
      type: Number,
      required: true
    },
    totalPages: {
      type: Number,
      required: true
    },
    maxVisiblePages: {
      type: Number,
      default: 5
    }
  },

  computed: {
    pages() {
      const pages = []
      const halfVisible = Math.floor(this.maxVisiblePages / 2)
      let start = Math.max(1, this.currentPage - halfVisible)
      let end = Math.min(this.totalPages, start + this.maxVisiblePages - 1)

      if (end - start + 1 < this.maxVisiblePages) {
        start = Math.max(1, end - this.maxVisiblePages + 1)
      }

      for (let i = start; i <= end; i++) {
        pages.push(i)
      }

      return pages
    }
  }
}
</script>

<style scoped>
.pagination {
  margin: 1rem 0;
}

.page-link {
  background-color: #2c3034;
  border-color: #373b3e;
  color: #fff;
  padding: 0.5rem 0.75rem;
}

.page-link:hover {
  background-color: #343a40;
  border-color: #373b3e;
  color: #fff;
}

.page-item.active .page-link {
  background-color: var(--primary);
  border-color: var(--primary);
  color: #fff;
}

.page-item.disabled .page-link {
  background-color: #2c3034;
  border-color: #373b3e;
  color: #6c757d;
}
</style> 