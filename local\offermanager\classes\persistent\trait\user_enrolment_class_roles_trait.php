<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\persistent\trait;

use context_course;

/**
 * Trait user_enrolment_class_roles_trait
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
trait user_enrolment_class_roles_trait
{
    protected $course_context = null;

    public function fetch_course_context()
    {
        $offerclass = $this->get_offer_class();

        $course = $offerclass->get_course();
        $this->course_context = context_course::instance($course->id);
    }

    public function get_course_context()
    {
        if (!$this->course_context) {
            $this->fetch_course_context();
        }

        return $this->course_context;
    }

    public function get_roles(): array
    {
        global $DB;

        $context = $this->get_course_context();

        if (!$context) {
            return [];
        }

        $offerclass = $this->get_offer_class();
        $enrollid = $offerclass->get('enrolid');
        $userid = $this->get('userid');

        $component = 'enrol_' . $offerclass->get('enrol');

        $sql = "SELECT DISTINCT 
                ra.roleid as id,
                IF(rn.name IS NOT NULL, rn.name, r.name) as name,
                r.shortname as shortname
            FROM {role_assignments} ra
                JOIN {role} r ON (r.id = ra.roleid)
                LEFT JOIN {role_names} rn ON (rn.contextid = ra.contextid AND rn.roleid = ra.roleid)
            WHERE ra.userid = :userid
                AND ra.contextid = :contextid
                AND ra.component = :component
                AND ra.itemid = :enrollid
            ORDER BY r.sortorder ASC
        ";

        $roles = $DB->get_records_sql(
            $sql,
            [
                'userid' => $userid,
                'contextid' => $context->id,
                'component' => $component,
                'enrollid' => $enrollid
            ]
        );

        $course_roles = $offerclass->get_course_roles();

        return array_map(
            function ($role) use ($course_roles) {
                return [
                    'id' => $role->id,
                    'name' => $role->name ?: $course_roles[$role->id]
                ];
            },
            $roles
        );
    }

    public function get_formated_roles()
    {
        return array_map(
            function ($role) {
                return [
                    'id' => $role['id'],
                    'name' => $role['name']
                ];
            },
            $this->get_roles()
        );
    }

    public function get_role_data()
    {
        $offerclass = $this->get_offer_class();
        $context = $this->get_course_context();
        $userid = $this->get('userid');
        $component = 'enrol_' . $offerclass->get('enrol');
        $itemid = $offerclass->get('enrolid');

        return [$userid, $context->id, $component, $itemid];
    }

    public function role_assign($roleid)
    {
        list($userid, $contextid, $component, $itemid) = $this->get_role_data();

        return role_assign($roleid, $userid, $contextid, $component, $itemid);
    }

    public function role_unassign($roleid)
    {
        list($userid, $contextid, $component, $itemid) = $this->get_role_data();

        return role_unassign($roleid, $userid, $contextid, $component, $itemid);
    }

    public function update_roles(array $roleids)
    {
        $new_roleid_list = array_map('intval', $roleids);
        $current_roles = $this->get_roles();
        $current_roleid_list = array_column($current_roles, 'id');

        $roleids_to_add = array_diff($new_roleid_list, $current_roleid_list);
        $roleids_to_remove = array_diff($current_roleid_list, $new_roleid_list);

        $changes_made = false;

        foreach ($roleids_to_add as $roleid) {
            $changes_made = $this->role_assign($roleid) || $changes_made;
        }

        foreach ($roleids_to_remove as $roleid) {
            $changes_made = $this->role_unassign($roleid) || $changes_made;
        }

        return $changes_made;
    }
}
