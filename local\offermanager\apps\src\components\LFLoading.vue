<template>
  <Transition>
    <div v-if="isLoading">
      <div class="modal-overlay"></div>
      <div class="loader-wrapper">
        <span class="loader" role="status">
          <span class="sr-only">Carregando...</span>
        </span>
      </div>
    </div>
  </Transition>
</template>

<script>
export default {
  name: "LFLoading",

  props: {
    isLoading: {
      type: Boolean,
      default: false,
    },
  },
};
</script>
<style lang="scss" scoped>
.loader-wrapper {
  position: fixed; /* Alterado de absolute para fixed */
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%); /* Centraliza perfeitamente */
  width: fit-content;
  height: fit-content;
  padding: 2rem;
  z-index: 1050;
}

.modal-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  background: #000;
  opacity: 0.3;
}

.loader {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 6rem;
  margin-top: 3rem;
  margin-bottom: 3rem;
  transition: opacity ease 0.5s;
}

.loader:before,
.loader:after {
  content: "";
  position: absolute;
  border-radius: 50%;
  animation: pulsOut 1.8s ease-in-out infinite;
  filter: drop-shadow(0 0 1rem var(--primary));
}

.loader:before {
  width: 100%;
  padding-bottom: 100%;
  box-shadow: inset 0 0 0 1rem var(--primary);
  animation-name: pulsIn;
}

.loader:after {
  width: calc(100% - 2rem);
  padding-bottom: calc(100% - 2rem);
  box-shadow: 0 0 0 0 var(--primary);
}
</style>
