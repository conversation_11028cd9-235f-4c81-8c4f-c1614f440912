{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template local_offermanager/reenrol_modal

    TODO describe template reenrol_modal

    Example context (json):
    {
    }
}}
{{#js}}
    require(['jquery', "core/config", 'core_form/modalform', 'core/notification'], function($, Config, ModalForm, Notification) {
        let offeruserenrol = {{ offeruserenrol }};
        let courseid = {{ courseid }};
        let $button = $('a.dropdown-item[data-key="reenrolbutton"]');

		$button.on('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            const modalForm = new ModalForm({
                formClass: "local_offermanager_self_reenrol_form", 
                args: {id: offeruserenrol},
                modalConfig: {
                    title: $button.text(),
                    removeOnClose: true,
                },
                saveButtonText: "{{#str}} confirm, core {{/str}}",
                returnFocus: $button[0],
            });

            modalForm.addEventListener(modalForm.events.SERVER_VALIDATION_ERROR,(event) => {
                    Notification.addNotification({
                        type: 'error',
                        message: event.toString()
                    });
            });
            modalForm.addEventListener(modalForm.events.FORM_SUBMITTED,(event) => {
                if (event.detail.result) {
                    Notification.addNotification({
                        type: 'success',
                        message: "{{#str}} message:reenrolment_success , local_offermanager {{/str}}"
                    });
                    setTimeout(() => {
                        window.location.href = Config.wwwroot + '/course/view.php?id=' + courseid;
                    },3000);
                } else {
                    Notification.addNotification({
                        type: 'error',
                        message: "{{#str}} error:cannot_cancel_offer_user_enrol , local_offermanager {{/str}}"
                    });
                }
            });

            modalForm.show();
        });

    });
{{/js}}