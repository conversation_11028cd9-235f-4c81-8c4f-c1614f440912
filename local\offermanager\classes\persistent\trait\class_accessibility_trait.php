<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\persistent\trait;

use local_offermanager\event\offer_class_accessible;
use local_offermanager\constants;

defined('MOODLE_INTERNAL') || die();

/**
 * Trait class_accessibility_trait
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
trait class_accessibility_trait
{

    public function is_accessible()
    {
        return $this->get('isaccessible') == constants::OFFER_CLASS_ACCESSIBLE;
    }

    /**
     * Verifica se a turma está acessível.
     *
     * @param bool $plusone bool to add 1 to the count
     * @return bool Verdadeiro se a turma estiver acessível.
     */
    public function check_if_accessible($plusone = false): bool
    {
        if (!$this->is_active() && $this->has_started()) {
            return constants::OFFER_CLASS_NOT_ACCESSIBLE;
        }

        $course = $this->get_course();

        if (!$course || !$course->visible) {
            return constants::OFFER_CLASS_NOT_ACCESSIBLE;
        }

        $minusers = (int) $this->get_mapped_field('minusers');

        $count = $this->count_user_enrolments();

        $count = $plusone ? ($count + 1) : $count;

        if ($minusers && $count < $minusers) {
            return constants::OFFER_CLASS_NOT_ACCESSIBLE;
        }

        return constants::OFFER_CLASS_ACCESSIBLE;
    }

    /**
     * Torna a turma acessível, atualizando campos e disparando evento.
     *
     * @return void
     */
    public function update_accessability(): void
    {
        if (!$this->is_accessible() && $this->check_if_accessible()) {
            $this->set_accessible();
        }
    }

    /**
     * Torna a turma acessível, atualizando campos e disparando evento.
     *
     * @return void
     */
    public function set_accessible(): void
    {
        $this->set('isaccessible', 1);
        $this->set('timeaccessible', time());
        $this->save();

        $event = offer_class_accessible::instance($this);
        $event->trigger();
    }
}
