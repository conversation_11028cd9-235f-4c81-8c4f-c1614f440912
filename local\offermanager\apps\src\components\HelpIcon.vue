<template>

  <a
    class="btn btn-link p-0"
    data-container="body"
    data-toggle="popover"
    data-placement="auto"
    :data-content="content"
    data-html="true"
    tabindex="0"
    data-trigger="focus"
    :aria-label="title"
    role="button"
  >
    <i
      class="icon fa fa-question-circle text-info fa-fw"
      :title="title"
      :aria-label="title"
      role="img"
    ></i>
  </a>
</template>

<script>
export default {
  name: 'HelpIcon',

  props: {
    title: {
      type: String,
      default: 'Ajuda'
    },
    text: {
      type: String,
      required: true
    },
    postion: {
      type: String,
      default: 'right'
    }
  },

  computed: {
    content() {
      return `<div class="no-overflow"><p class="mb-0">${this.text}</p></div>`
    }
  }
}
</script>

<style lang="scss">
/* Estilo global para garantir que o popover fique acima do modal */
:global(.popover) {
  z-index: 100000 !important;
}
</style>
