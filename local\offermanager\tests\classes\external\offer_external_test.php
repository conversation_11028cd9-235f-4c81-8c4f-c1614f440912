<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\classes\external;

use advanced_testcase;
use local_offermanager\constants;
use local_offermanager\external\offer_external;
use local_offermanager\persistent\offer_model;
use moodle_exception;
use core_external\external_api;

global $CFG;
require_once($CFG->dirroot . '/webservice/tests/helpers.php');

/**
 * Tests for Offer Manager
 *
 * @package    local_offermanager
 * @category   test
 * @copyright  2015 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
final class offer_external_test extends \advanced_testcase
{

    public function test_save_create()
    {
        $this->resetAfterTest(true);

        $offerData = [
            'id' => 0,
            'name' => 'Teste Oferta',
            'description' => 'Descrição da oferta de teste',
            'type' => 'premium',
            'status' => constants::OFFER_STATUS_INACTIVE,
            'audience_ids' => []
        ];

        $result = offer_external::save(
            $offerData['id'],
            $offerData['name'],
            $offerData['description'],
            $offerData['type'],
            $offerData['status'],
            $offerData['audience_ids']
        );

        $result = external_api::clean_returnvalue(
            offer_external::save_returns(),
            $result
        );

        $this->assertNotEmpty($result['id']);
        $this->assertEquals(get_string('offer_created', 'local_offermanager'), $result['message']);

        $offer = new offer_model($result['id']);
        $this->assertEquals('Teste Oferta', $offer->get('name'));
    }

    public function test_save_update()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0);
        $offer->set('name', 'Oferta Inicial');
        $offer->set('description', 'Descrição inicial');
        $offer->set('type', 'basic');
        $offer->set('status', constants::OFFER_STATUS_INACTIVE);
        $offer->save();

        $result = offer_external::save(
            $offer->get('id'),
            'Oferta Atualizada',
            'Nova descrição',
            'premium',
            constants::OFFER_STATUS_INACTIVE,
            []
        );

        $result = external_api::clean_returnvalue(
            offer_external::save_returns(),
            $result
        );

        $this->assertEquals($offer->get('id'), $result['id']);
        $this->assertEquals(get_string('offer_updated', 'local_offermanager'), $result['message']);

        $updatedOffer = new offer_model($offer->get('id'));
        $this->assertEquals('Oferta Atualizada', $updatedOffer->get('name'));
    }

    public function test_save_create_with_audiences(): void
    {
        global $DB;

        $this->resetAfterTest(true);

        $audienceid_1 = $DB->insert_record('local_audience_audiences', ['name' => 'Público-alvo 1']);
        $audienceid_2 = $DB->insert_record('local_audience_audiences', ['name' => 'Público-alvo 2']);

        $result = offer_external::save(
            0,
            'Oferta de Teste',
            'Descrição da oferta de teste',
            'online',
            constants::OFFER_STATUS_INACTIVE,
            [$audienceid_1, $audienceid_2]
        );

        $result = external_api::clean_returnvalue(
            offer_external::save_returns(),
            $result
        );

        $this->assertNotEmpty($result['id']);
        $this->assertEquals(get_string('offer_created', 'local_offermanager'), $result['message']);
        $this->assertTrue($result['audiences_updated']);

        $offer = new offer_model($result['id']);
        $audienceIds = $offer->get_audience_ids();
        $this->assertCount(2, $audienceIds);
        $this->assertContains($audienceid_1, $audienceIds);
        $this->assertContains($audienceid_2, $audienceIds);
    }

    public function test_save_update_with_audiences(): void
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0);
        $offer->set('name', 'Oferta Inicial');
        $offer->set('description', 'Descrição inicial');
        $offer->set('type', 'basic');
        $offer->set('status', constants::OFFER_STATUS_INACTIVE);
        $offer->save();

        $audienceid_1 = $DB->insert_record('local_audience_audiences', ['name' => 'Público-alvo 1']);
        $audienceid_2 = $DB->insert_record('local_audience_audiences', ['name' => 'Público-alvo 2']);

        $result = offer_external::save(
            $offer->get('id'),
            'Oferta Atualizada',
            'Nova descrição',
            'premium',
            constants::OFFER_STATUS_INACTIVE,
            [$audienceid_1, $audienceid_2]
        );

        $result = external_api::clean_returnvalue(
            offer_external::save_returns(),
            $result
        );

        $this->assertEquals($offer->get('id'), $result['id']);
        $this->assertEquals(get_string('offer_updated', 'local_offermanager'), $result['message']);
        $this->assertTrue($result['audiences_updated']);

        $audienceIds = $offer->get_audience_ids();
        $this->assertCount(2, $audienceIds);
        $this->assertContains($audienceid_1, $audienceIds);
        $this->assertContains($audienceid_2, $audienceIds);
    }

    public function test_save_clean_offer_audiences(): void
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0);
        $offer->set('name', 'Oferta Inicial');
        $offer->set('description', 'Descrição inicial');
        $offer->set('type', 'basic');
        $offer->set('status', constants::OFFER_STATUS_INACTIVE);
        $offer->save();

        $audienceid_1 = $DB->insert_record('local_audience_audiences', ['name' => 'Público-alvo 1']);
        $offer->add_audience($audienceid_1);

        $result = offer_external::save(
            $offer->get('id'),
            'Oferta Atualizada',
            'Nova descrição',
            'premium',
            constants::OFFER_STATUS_INACTIVE,
            []
        );

        $result = external_api::clean_returnvalue(
            offer_external::save_returns(),
            $result
        );

        $this->assertEquals($offer->get('id'), $result['id']);
        $this->assertEquals(get_string('offer_updated', 'local_offermanager'), $result['message']);
        $this->assertTrue($result['audiences_updated']);

        $audienceIds = $offer->get_audience_ids();
        $this->assertEmpty($audienceIds);
    }

    public function test_save_invalid_audience_ids(): void
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0);
        $offer->set('name', 'Oferta Inicial');
        $offer->set('description', 'Descrição inicial');
        $offer->set('type', 'basic');
        $offer->set('status', constants::OFFER_STATUS_INACTIVE);
        $offer->save();

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:audience_not_found', 'local_offermanager'));
        offer_external::save(
            $offer->get('id'),
            'Oferta Atualizada',
            'Nova descrição',
            'premium',
            constants::OFFER_STATUS_INACTIVE,
            [999]
        );
    }

    public function test_save_invalid_audience_ids_type(): void
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0);
        $offer->set('name', 'Oferta Inicial');
        $offer->set('description', 'Descrição inicial');
        $offer->set('type', 'basic');
        $offer->set('status', constants::OFFER_STATUS_INACTIVE);
        $offer->save();

        $this->expectException(moodle_exception::class);
        offer_external::save(
            $offer->get('id'),
            'Oferta Atualizada',
            'Nova descrição',
            'premium',
            constants::OFFER_STATUS_INACTIVE,
            ['teste']
        );
    }

    public function test_get_offer_by_id_valid()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0);
        $offer->set('name', 'Oferta Teste');
        $offer->set('description', 'Descrição Teste');
        $offer->set('type', 'standard');
        $offer->save();

        $result = offer_external::get($offer->get('id'));

        $result = external_api::clean_returnvalue(
            offer_external::get_returns(),
            $result
        );

        $this->assertEquals($offer->get('id'), $result['id']);
        $this->assertEquals('Oferta Teste', $result['name']);
    }

    public function test_get_offer_by_id_invalid()
    {
        $this->resetAfterTest(true);

        $this->expectException(moodle_exception::class);
        offer_external::get(9999);
    }

    public function test_delete_offer_valid()
    {
        $this->setAdminUser();
        $this->resetAfterTest(true);

        $offer = new offer_model(0);
        $offer->set('name', 'Oferta a Deletar');
        $offer->set('description', 'Será deletada');
        $offer->set('type', 'basic');
        $offer->save();

        $result = offer_external::delete($offer->get('id'));

        $result = external_api::clean_returnvalue(
            offer_external::delete_returns(),
            $result
        );

        $this->assertEquals($offer->get('id'), $result['id']);
        $this->assertEquals('Oferta excluída com sucesso!', $result['message']);

        $this->expectException(moodle_exception::class);
        offer_external::get($offer->get('id'));
    }

    public function test_delete_offer_invalid()
    {
        $this->resetAfterTest(true);

        $this->expectException(moodle_exception::class);
        offer_external::delete(9999);
    }

    public function test_get_type_options()
    {
        $this->resetAfterTest(true);

        set_config('enabletypeoptions', true, 'local_offermanager');

        set_config('typeoptions', "online\npresencial", 'local_offermanager');
        set_config('defaulttypeoption', 'online', 'local_offermanager');

        $result = offer_external::get_type_options();

        $result = external_api::clean_returnvalue(
            offer_external::get_type_options_returns(),
            $result
        );

        $expected = [
            'enabled' => true,
            'types' => ['online', 'presencial'],
            'default' => 'online',
        ];

        $this->assertEquals($expected, $result);

        set_config('enabletypeoptions', false, 'local_offermanager');
        set_config('typeoptions', '', 'local_offermanager');
        set_config('defaulttypeoption', '', 'local_offermanager');

        $result = offer_external::get_type_options();

        $result = external_api::clean_returnvalue(
            offer_external::get_type_options_returns(),
            $result
        );

        $expected = [
            'enabled' => false,
            'types' => [],
            'default' => false,
        ];

        $this->assertEquals($expected, $result);
    }

    public function test_set_status_to_active_and_exception_when_activating_twice()
    {
        global $DB;
        $this->resetAfterTest(true);
        $this->setAdminUser();

        $offer = new offer_model(0);
        $offer->set('name', 'Oferta Inicial');
        $offer->save();

        $audienceid = $DB->insert_record(
            'local_audience_audiences',
            [
                'name' => 'Público-alvo 1'
            ]
        );

        $offer->add_audience($audienceid);

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offer_class = $offer_course->add_class($enrol_instance->id);

        $return = offer_external::set_status(
            $offer->get('id'),
            constants::OFFER_STATUS_ACTIVE
        );

        $return = external_api::clean_returnvalue(
            offer_external::set_status_returns(),
            $return
        );

        $this->assertEquals(constants::OFFER_STATUS_ACTIVE, $return['status']);
        $this->assertStringContainsString(get_string('event:offeractivated', 'local_offermanager'), $return['message']);

        $offer->read();

        $this->assertEquals(constants::OFFER_STATUS_ACTIVE, $offer->get('status'));

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:offer_already_active', 'local_offermanager'));

        offer_external::set_status(
            $offer->get('id'),
            constants::OFFER_STATUS_ACTIVE
        );
    }

    public function test_set_status_to_inactive_and_exception_when_inactivating_twice()
    {
        global $DB;
        $this->setAdminUser();
        $this->resetAfterTest(true);

        $offer = new offer_model(0);
        $offer->set('name', 'Oferta Inicial');
        $offer->save();

        $audienceid = $DB->insert_record(
            'local_audience_audiences',
            [
                'name' => 'Público-alvo 1'
            ]
        );

        $offer->add_audience($audienceid);

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offer_class = $offer_course->add_class($enrol_instance->id);

        $offer->activate();

        $this->assertEquals(constants::OFFER_STATUS_ACTIVE, $offer->get('status'));

        $return = offer_external::set_status(
            $offer->get('id'),
            constants::OFFER_STATUS_INACTIVE
        );

        $return = external_api::clean_returnvalue(
            offer_external::set_status_returns(),
            $return
        );

        $this->assertEquals(constants::OFFER_STATUS_INACTIVE, $return['status']);
        $this->assertStringContainsString(get_string('event:offerinactivated', 'local_offermanager'), $return['message']);

        $offer->read();

        $this->assertEquals(constants::OFFER_STATUS_INACTIVE, $offer->get('status'));

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:offer_already_inactive', 'local_offermanager'));

        offer_external::set_status(
            $offer->get('id'),
            constants::OFFER_STATUS_INACTIVE
        );
    }

    public function test_set_status_throws_exception_when_offer_doest_exist()
    {
        $this->resetAfterTest(true);

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:offer_not_found', 'local_offermanager'));

        offer_external::set_status(
            999,
            constants::OFFER_STATUS_INACTIVE
        );
    }
}
