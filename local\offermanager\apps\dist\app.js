import { resolveComponent as Ie, openBlock as Ze, createElementBlock as et, createElementVNode as tt, createVNode as Te, withCtx as Ct, createTextVNode as At, shallowRef as xt, unref as W, shallowReactive as It, nextTick as Tt, defineComponent as nt, reactive as Vt, inject as ee, computed as D, h as ot, provide as he, ref as Dt, watch as rt, getCurrentInstance as st, watchEffect as jt, toDisplayString as Mt, createApp as Lt } from "vue";
import { createPinia as Ut } from "pinia";
const it = (e, t) => {
  const n = e.__vccOpts || e;
  for (const [o, r] of t)
    n[o] = r;
  return n;
}, Bt = {
  name: "App"
}, Ht = { id: "app" };
function Gt(e, t, n, o, r, u) {
  const f = Ie("router-link"), h = Ie("router-view");
  return Ze(), et("div", Ht, [
    tt("nav", null, [
      Te(f, { to: "/" }, {
        default: Ct(() => t[0] || (t[0] = [
          At("Home")
        ])),
        _: 1
      })
    ]),
    Te(h)
  ]);
}
const Kt = /* @__PURE__ */ it(Bt, [["render", Gt]]);
function qt() {
  return at().__VUE_DEVTOOLS_GLOBAL_HOOK__;
}
function at() {
  return typeof navigator < "u" && typeof window < "u" ? window : typeof globalThis < "u" ? globalThis : {};
}
const Wt = typeof Proxy == "function", zt = "devtools-plugin:setup", Qt = "plugin:settings:set";
let q, ve;
function Ft() {
  var e;
  return q !== void 0 || (typeof window < "u" && window.performance ? (q = !0, ve = window.performance) : typeof globalThis < "u" && (!((e = globalThis.perf_hooks) === null || e === void 0) && e.performance) ? (q = !0, ve = globalThis.perf_hooks.performance) : q = !1), q;
}
function Yt() {
  return Ft() ? ve.now() : Date.now();
}
class Jt {
  constructor(t, n) {
    this.target = null, this.targetQueue = [], this.onQueue = [], this.plugin = t, this.hook = n;
    const o = {};
    if (t.settings)
      for (const f in t.settings) {
        const h = t.settings[f];
        o[f] = h.defaultValue;
      }
    const r = `__vue-devtools-plugin-settings__${t.id}`;
    let u = Object.assign({}, o);
    try {
      const f = localStorage.getItem(r), h = JSON.parse(f);
      Object.assign(u, h);
    } catch {
    }
    this.fallbacks = {
      getSettings() {
        return u;
      },
      setSettings(f) {
        try {
          localStorage.setItem(r, JSON.stringify(f));
        } catch {
        }
        u = f;
      },
      now() {
        return Yt();
      }
    }, n && n.on(Qt, (f, h) => {
      f === this.plugin.id && this.fallbacks.setSettings(h);
    }), this.proxiedOn = new Proxy({}, {
      get: (f, h) => this.target ? this.target.on[h] : (...l) => {
        this.onQueue.push({
          method: h,
          args: l
        });
      }
    }), this.proxiedTarget = new Proxy({}, {
      get: (f, h) => this.target ? this.target[h] : h === "on" ? this.proxiedOn : Object.keys(this.fallbacks).includes(h) ? (...l) => (this.targetQueue.push({
        method: h,
        args: l,
        resolve: () => {
        }
      }), this.fallbacks[h](...l)) : (...l) => new Promise((p) => {
        this.targetQueue.push({
          method: h,
          args: l,
          resolve: p
        });
      })
    });
  }
  async setRealTarget(t) {
    this.target = t;
    for (const n of this.onQueue)
      this.target.on[n.method](...n.args);
    for (const n of this.targetQueue)
      n.resolve(await this.target[n.method](...n.args));
  }
}
function Xt(e, t) {
  const n = e, o = at(), r = qt(), u = Wt && n.enableEarlyProxy;
  if (r && (o.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__ || !u))
    r.emit(zt, e, t);
  else {
    const f = u ? new Jt(n, r) : null;
    (o.__VUE_DEVTOOLS_PLUGINS__ = o.__VUE_DEVTOOLS_PLUGINS__ || []).push({
      pluginDescriptor: n,
      setupFn: t,
      proxy: f
    }), f && t(f.proxiedTarget);
  }
}
/*!
  * vue-router v4.5.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */
const M = typeof document < "u";
function ct(e) {
  return typeof e == "object" || "displayName" in e || "props" in e || "__vccOpts" in e;
}
function Zt(e) {
  return e.__esModule || e[Symbol.toStringTag] === "Module" || // support CF with dynamic imports that do not
  // add the Module string tag
  e.default && ct(e.default);
}
const O = Object.assign;
function pe(e, t) {
  const n = {};
  for (const o in t) {
    const r = t[o];
    n[o] = I(r) ? r.map(e) : e(r);
  }
  return n;
}
const X = () => {
}, I = Array.isArray;
function E(e) {
  const t = Array.from(arguments).slice(1);
  console.warn.apply(console, ["[Vue Router warn]: " + e].concat(t));
}
const lt = /#/g, en = /&/g, tn = /\//g, nn = /=/g, on = /\?/g, ut = /\+/g, rn = /%5B/g, sn = /%5D/g, ft = /%5E/g, an = /%60/g, dt = /%7B/g, cn = /%7C/g, ht = /%7D/g, ln = /%20/g;
function Ne(e) {
  return encodeURI("" + e).replace(cn, "|").replace(rn, "[").replace(sn, "]");
}
function un(e) {
  return Ne(e).replace(dt, "{").replace(ht, "}").replace(ft, "^");
}
function ye(e) {
  return Ne(e).replace(ut, "%2B").replace(ln, "+").replace(lt, "%23").replace(en, "%26").replace(an, "`").replace(dt, "{").replace(ht, "}").replace(ft, "^");
}
function fn(e) {
  return ye(e).replace(nn, "%3D");
}
function dn(e) {
  return Ne(e).replace(lt, "%23").replace(on, "%3F");
}
function hn(e) {
  return e == null ? "" : dn(e).replace(tn, "%2F");
}
function z(e) {
  try {
    return decodeURIComponent("" + e);
  } catch {
    process.env.NODE_ENV !== "production" && E(`Error decoding "${e}". Using original value`);
  }
  return "" + e;
}
const pn = /\/$/, mn = (e) => e.replace(pn, "");
function me(e, t, n = "/") {
  let o, r = {}, u = "", f = "";
  const h = t.indexOf("#");
  let l = t.indexOf("?");
  return h < l && h >= 0 && (l = -1), l > -1 && (o = t.slice(0, l), u = t.slice(l + 1, h > -1 ? h : t.length), r = e(u)), h > -1 && (o = o || t.slice(0, h), f = t.slice(h, t.length)), o = yn(o ?? t, n), {
    fullPath: o + (u && "?") + u + f,
    path: o,
    query: r,
    hash: z(f)
  };
}
function gn(e, t) {
  const n = t.query ? e(t.query) : "";
  return t.path + (n && "?") + n + (t.hash || "");
}
function Ve(e, t) {
  return !t || !e.toLowerCase().startsWith(t.toLowerCase()) ? e : e.slice(t.length) || "/";
}
function De(e, t, n) {
  const o = t.matched.length - 1, r = n.matched.length - 1;
  return o > -1 && o === r && H(t.matched[o], n.matched[r]) && pt(t.params, n.params) && e(t.query) === e(n.query) && t.hash === n.hash;
}
function H(e, t) {
  return (e.aliasOf || e) === (t.aliasOf || t);
}
function pt(e, t) {
  if (Object.keys(e).length !== Object.keys(t).length)
    return !1;
  for (const n in e)
    if (!vn(e[n], t[n]))
      return !1;
  return !0;
}
function vn(e, t) {
  return I(e) ? je(e, t) : I(t) ? je(t, e) : e === t;
}
function je(e, t) {
  return I(t) ? e.length === t.length && e.every((n, o) => n === t[o]) : e.length === 1 && e[0] === t;
}
function yn(e, t) {
  if (e.startsWith("/"))
    return e;
  if (process.env.NODE_ENV !== "production" && !t.startsWith("/"))
    return E(`Cannot resolve a relative location without an absolute path. Trying to resolve "${e}" from "${t}". It should look like "/${t}".`), e;
  if (!e)
    return t;
  const n = t.split("/"), o = e.split("/"), r = o[o.length - 1];
  (r === ".." || r === ".") && o.push("");
  let u = n.length - 1, f, h;
  for (f = 0; f < o.length; f++)
    if (h = o[f], h !== ".")
      if (h === "..")
        u > 1 && u--;
      else
        break;
  return n.slice(0, u).join("/") + "/" + o.slice(f).join("/");
}
const U = {
  path: "/",
  // TODO: could we use a symbol in the future?
  name: void 0,
  params: {},
  query: {},
  hash: "",
  fullPath: "/",
  matched: [],
  meta: {},
  redirectedFrom: void 0
};
var te;
(function(e) {
  e.pop = "pop", e.push = "push";
})(te || (te = {}));
var Z;
(function(e) {
  e.back = "back", e.forward = "forward", e.unknown = "";
})(Z || (Z = {}));
function _n(e) {
  if (!e)
    if (M) {
      const t = document.querySelector("base");
      e = t && t.getAttribute("href") || "/", e = e.replace(/^\w+:\/\/[^\/]+/, "");
    } else
      e = "/";
  return e[0] !== "/" && e[0] !== "#" && (e = "/" + e), mn(e);
}
const En = /^[^#]+#/;
function wn(e, t) {
  return e.replace(En, "#") + t;
}
function bn(e, t) {
  const n = document.documentElement.getBoundingClientRect(), o = e.getBoundingClientRect();
  return {
    behavior: t.behavior,
    left: o.left - n.left - (t.left || 0),
    top: o.top - n.top - (t.top || 0)
  };
}
const ie = () => ({
  left: window.scrollX,
  top: window.scrollY
});
function Rn(e) {
  let t;
  if ("el" in e) {
    const n = e.el, o = typeof n == "string" && n.startsWith("#");
    if (process.env.NODE_ENV !== "production" && typeof e.el == "string" && (!o || !document.getElementById(e.el.slice(1))))
      try {
        const u = document.querySelector(e.el);
        if (o && u) {
          E(`The selector "${e.el}" should be passed as "el: document.querySelector('${e.el}')" because it starts with "#".`);
          return;
        }
      } catch {
        E(`The selector "${e.el}" is invalid. If you are using an id selector, make sure to escape it. You can find more information about escaping characters in selectors at https://mathiasbynens.be/notes/css-escapes or use CSS.escape (https://developer.mozilla.org/en-US/docs/Web/API/CSS/escape).`);
        return;
      }
    const r = typeof n == "string" ? o ? document.getElementById(n.slice(1)) : document.querySelector(n) : n;
    if (!r) {
      process.env.NODE_ENV !== "production" && E(`Couldn't find element using selector "${e.el}" returned by scrollBehavior.`);
      return;
    }
    t = bn(r, e);
  } else
    t = e;
  "scrollBehavior" in document.documentElement.style ? window.scrollTo(t) : window.scrollTo(t.left != null ? t.left : window.scrollX, t.top != null ? t.top : window.scrollY);
}
function Me(e, t) {
  return (history.state ? history.state.position - t : -1) + e;
}
const _e = /* @__PURE__ */ new Map();
function Nn(e, t) {
  _e.set(e, t);
}
function On(e) {
  const t = _e.get(e);
  return _e.delete(e), t;
}
let Pn = () => location.protocol + "//" + location.host;
function mt(e, t) {
  const { pathname: n, search: o, hash: r } = t, u = e.indexOf("#");
  if (u > -1) {
    let h = r.includes(e.slice(u)) ? e.slice(u).length : 1, l = r.slice(h);
    return l[0] !== "/" && (l = "/" + l), Ve(l, "");
  }
  return Ve(n, e) + o + r;
}
function Sn(e, t, n, o) {
  let r = [], u = [], f = null;
  const h = ({ state: c }) => {
    const d = mt(e, location), y = n.value, _ = t.value;
    let S = 0;
    if (c) {
      if (n.value = d, t.value = c, f && f === y) {
        f = null;
        return;
      }
      S = _ ? c.position - _.position : 0;
    } else
      o(d);
    r.forEach((k) => {
      k(n.value, y, {
        delta: S,
        type: te.pop,
        direction: S ? S > 0 ? Z.forward : Z.back : Z.unknown
      });
    });
  };
  function l() {
    f = n.value;
  }
  function p(c) {
    r.push(c);
    const d = () => {
      const y = r.indexOf(c);
      y > -1 && r.splice(y, 1);
    };
    return u.push(d), d;
  }
  function a() {
    const { history: c } = window;
    c.state && c.replaceState(O({}, c.state, { scroll: ie() }), "");
  }
  function s() {
    for (const c of u)
      c();
    u = [], window.removeEventListener("popstate", h), window.removeEventListener("beforeunload", a);
  }
  return window.addEventListener("popstate", h), window.addEventListener("beforeunload", a, {
    passive: !0
  }), {
    pauseListeners: l,
    listen: p,
    destroy: s
  };
}
function Le(e, t, n, o = !1, r = !1) {
  return {
    back: e,
    current: t,
    forward: n,
    replaced: o,
    position: window.history.length,
    scroll: r ? ie() : null
  };
}
function kn(e) {
  const { history: t, location: n } = window, o = {
    value: mt(e, n)
  }, r = { value: t.state };
  r.value || u(o.value, {
    back: null,
    current: o.value,
    forward: null,
    // the length is off by one, we need to decrease it
    position: t.length - 1,
    replaced: !0,
    // don't add a scroll as the user may have an anchor, and we want
    // scrollBehavior to be triggered without a saved position
    scroll: null
  }, !0);
  function u(l, p, a) {
    const s = e.indexOf("#"), c = s > -1 ? (n.host && document.querySelector("base") ? e : e.slice(s)) + l : Pn() + e + l;
    try {
      t[a ? "replaceState" : "pushState"](p, "", c), r.value = p;
    } catch (d) {
      process.env.NODE_ENV !== "production" ? E("Error with push/replace State", d) : console.error(d), n[a ? "replace" : "assign"](c);
    }
  }
  function f(l, p) {
    const a = O({}, t.state, Le(
      r.value.back,
      // keep back and forward entries but override current position
      l,
      r.value.forward,
      !0
    ), p, { position: r.value.position });
    u(l, a, !0), o.value = l;
  }
  function h(l, p) {
    const a = O(
      {},
      // use current history state to gracefully handle a wrong call to
      // history.replaceState
      // https://github.com/vuejs/router/issues/366
      r.value,
      t.state,
      {
        forward: l,
        scroll: ie()
      }
    );
    process.env.NODE_ENV !== "production" && !t.state && E(`history.state seems to have been manually replaced without preserving the necessary values. Make sure to preserve existing history state if you are manually calling history.replaceState:

history.replaceState(history.state, '', url)

You can find more information at https://router.vuejs.org/guide/migration/#Usage-of-history-state`), u(a.current, a, !0);
    const s = O({}, Le(o.value, l, null), { position: a.position + 1 }, p);
    u(l, s, !1), o.value = l;
  }
  return {
    location: o,
    state: r,
    push: h,
    replace: f
  };
}
function $n(e) {
  e = _n(e);
  const t = kn(e), n = Sn(e, t.state, t.location, t.replace);
  function o(u, f = !0) {
    f || n.pauseListeners(), history.go(u);
  }
  const r = O({
    // it's overridden right after
    location: "",
    base: e,
    go: o,
    createHref: wn.bind(null, e)
  }, t, n);
  return Object.defineProperty(r, "location", {
    enumerable: !0,
    get: () => t.location.value
  }), Object.defineProperty(r, "state", {
    enumerable: !0,
    get: () => t.state.value
  }), r;
}
function se(e) {
  return typeof e == "string" || e && typeof e == "object";
}
function gt(e) {
  return typeof e == "string" || typeof e == "symbol";
}
const Ee = Symbol(process.env.NODE_ENV !== "production" ? "navigation failure" : "");
var Ue;
(function(e) {
  e[e.aborted = 4] = "aborted", e[e.cancelled = 8] = "cancelled", e[e.duplicated = 16] = "duplicated";
})(Ue || (Ue = {}));
const Cn = {
  1({ location: e, currentLocation: t }) {
    return `No match for
 ${JSON.stringify(e)}${t ? `
while being at
` + JSON.stringify(t) : ""}`;
  },
  2({ from: e, to: t }) {
    return `Redirected from "${e.fullPath}" to "${xn(t)}" via a navigation guard.`;
  },
  4({ from: e, to: t }) {
    return `Navigation aborted from "${e.fullPath}" to "${t.fullPath}" via a navigation guard.`;
  },
  8({ from: e, to: t }) {
    return `Navigation cancelled from "${e.fullPath}" to "${t.fullPath}" with a new navigation.`;
  },
  16({ from: e, to: t }) {
    return `Avoided redundant navigation to current location: "${e.fullPath}".`;
  }
};
function Q(e, t) {
  return process.env.NODE_ENV !== "production" ? O(new Error(Cn[e](t)), {
    type: e,
    [Ee]: !0
  }, t) : O(new Error(), {
    type: e,
    [Ee]: !0
  }, t);
}
function j(e, t) {
  return e instanceof Error && Ee in e && (t == null || !!(e.type & t));
}
const An = ["params", "query", "hash"];
function xn(e) {
  if (typeof e == "string")
    return e;
  if (e.path != null)
    return e.path;
  const t = {};
  for (const n of An)
    n in e && (t[n] = e[n]);
  return JSON.stringify(t, null, 2);
}
const Be = "[^/]+?", In = {
  sensitive: !1,
  strict: !1,
  start: !0,
  end: !0
}, Tn = /[.+*?^${}()[\]/\\]/g;
function Vn(e, t) {
  const n = O({}, In, t), o = [];
  let r = n.start ? "^" : "";
  const u = [];
  for (const p of e) {
    const a = p.length ? [] : [
      90
      /* PathScore.Root */
    ];
    n.strict && !p.length && (r += "/");
    for (let s = 0; s < p.length; s++) {
      const c = p[s];
      let d = 40 + (n.sensitive ? 0.25 : 0);
      if (c.type === 0)
        s || (r += "/"), r += c.value.replace(Tn, "\\$&"), d += 40;
      else if (c.type === 1) {
        const { value: y, repeatable: _, optional: S, regexp: k } = c;
        u.push({
          name: y,
          repeatable: _,
          optional: S
        });
        const R = k || Be;
        if (R !== Be) {
          d += 10;
          try {
            new RegExp(`(${R})`);
          } catch (x) {
            throw new Error(`Invalid custom RegExp for param "${y}" (${R}): ` + x.message);
          }
        }
        let N = _ ? `((?:${R})(?:/(?:${R}))*)` : `(${R})`;
        s || (N = // avoid an optional / if there are more segments e.g. /:p?-static
        // or /:p?-:p2
        S && p.length < 2 ? `(?:/${N})` : "/" + N), S && (N += "?"), r += N, d += 20, S && (d += -8), _ && (d += -20), R === ".*" && (d += -50);
      }
      a.push(d);
    }
    o.push(a);
  }
  if (n.strict && n.end) {
    const p = o.length - 1;
    o[p][o[p].length - 1] += 0.7000000000000001;
  }
  n.strict || (r += "/?"), n.end ? r += "$" : n.strict && !r.endsWith("/") && (r += "(?:/|$)");
  const f = new RegExp(r, n.sensitive ? "" : "i");
  function h(p) {
    const a = p.match(f), s = {};
    if (!a)
      return null;
    for (let c = 1; c < a.length; c++) {
      const d = a[c] || "", y = u[c - 1];
      s[y.name] = d && y.repeatable ? d.split("/") : d;
    }
    return s;
  }
  function l(p) {
    let a = "", s = !1;
    for (const c of e) {
      (!s || !a.endsWith("/")) && (a += "/"), s = !1;
      for (const d of c)
        if (d.type === 0)
          a += d.value;
        else if (d.type === 1) {
          const { value: y, repeatable: _, optional: S } = d, k = y in p ? p[y] : "";
          if (I(k) && !_)
            throw new Error(`Provided param "${y}" is an array but it is not repeatable (* or + modifiers)`);
          const R = I(k) ? k.join("/") : k;
          if (!R)
            if (S)
              c.length < 2 && (a.endsWith("/") ? a = a.slice(0, -1) : s = !0);
            else
              throw new Error(`Missing required param "${y}"`);
          a += R;
        }
    }
    return a || "/";
  }
  return {
    re: f,
    score: o,
    keys: u,
    parse: h,
    stringify: l
  };
}
function Dn(e, t) {
  let n = 0;
  for (; n < e.length && n < t.length; ) {
    const o = t[n] - e[n];
    if (o)
      return o;
    n++;
  }
  return e.length < t.length ? e.length === 1 && e[0] === 40 + 40 ? -1 : 1 : e.length > t.length ? t.length === 1 && t[0] === 40 + 40 ? 1 : -1 : 0;
}
function vt(e, t) {
  let n = 0;
  const o = e.score, r = t.score;
  for (; n < o.length && n < r.length; ) {
    const u = Dn(o[n], r[n]);
    if (u)
      return u;
    n++;
  }
  if (Math.abs(r.length - o.length) === 1) {
    if (He(o))
      return 1;
    if (He(r))
      return -1;
  }
  return r.length - o.length;
}
function He(e) {
  const t = e[e.length - 1];
  return e.length > 0 && t[t.length - 1] < 0;
}
const jn = {
  type: 0,
  value: ""
}, Mn = /[a-zA-Z0-9_]/;
function Ln(e) {
  if (!e)
    return [[]];
  if (e === "/")
    return [[jn]];
  if (!e.startsWith("/"))
    throw new Error(process.env.NODE_ENV !== "production" ? `Route paths should start with a "/": "${e}" should be "/${e}".` : `Invalid path "${e}"`);
  function t(d) {
    throw new Error(`ERR (${n})/"${p}": ${d}`);
  }
  let n = 0, o = n;
  const r = [];
  let u;
  function f() {
    u && r.push(u), u = [];
  }
  let h = 0, l, p = "", a = "";
  function s() {
    p && (n === 0 ? u.push({
      type: 0,
      value: p
    }) : n === 1 || n === 2 || n === 3 ? (u.length > 1 && (l === "*" || l === "+") && t(`A repeatable param (${p}) must be alone in its segment. eg: '/:ids+.`), u.push({
      type: 1,
      value: p,
      regexp: a,
      repeatable: l === "*" || l === "+",
      optional: l === "*" || l === "?"
    })) : t("Invalid state to consume buffer"), p = "");
  }
  function c() {
    p += l;
  }
  for (; h < e.length; ) {
    if (l = e[h++], l === "\\" && n !== 2) {
      o = n, n = 4;
      continue;
    }
    switch (n) {
      case 0:
        l === "/" ? (p && s(), f()) : l === ":" ? (s(), n = 1) : c();
        break;
      case 4:
        c(), n = o;
        break;
      case 1:
        l === "(" ? n = 2 : Mn.test(l) ? c() : (s(), n = 0, l !== "*" && l !== "?" && l !== "+" && h--);
        break;
      case 2:
        l === ")" ? a[a.length - 1] == "\\" ? a = a.slice(0, -1) + l : n = 3 : a += l;
        break;
      case 3:
        s(), n = 0, l !== "*" && l !== "?" && l !== "+" && h--, a = "";
        break;
      default:
        t("Unknown state");
        break;
    }
  }
  return n === 2 && t(`Unfinished custom RegExp for param "${p}"`), s(), f(), r;
}
function Un(e, t, n) {
  const o = Vn(Ln(e.path), n);
  if (process.env.NODE_ENV !== "production") {
    const u = /* @__PURE__ */ new Set();
    for (const f of o.keys)
      u.has(f.name) && E(`Found duplicated params with name "${f.name}" for path "${e.path}". Only the last one will be available on "$route.params".`), u.add(f.name);
  }
  const r = O(o, {
    record: e,
    parent: t,
    // these needs to be populated by the parent
    children: [],
    alias: []
  });
  return t && !r.record.aliasOf == !t.record.aliasOf && t.children.push(r), r;
}
function Bn(e, t) {
  const n = [], o = /* @__PURE__ */ new Map();
  t = We({ strict: !1, end: !0, sensitive: !1 }, t);
  function r(s) {
    return o.get(s);
  }
  function u(s, c, d) {
    const y = !d, _ = Ke(s);
    process.env.NODE_ENV !== "production" && qn(_, c), _.aliasOf = d && d.record;
    const S = We(t, s), k = [_];
    if ("alias" in s) {
      const x = typeof s.alias == "string" ? [s.alias] : s.alias;
      for (const V of x)
        k.push(
          // we need to normalize again to ensure the `mods` property
          // being non enumerable
          Ke(O({}, _, {
            // this allows us to hold a copy of the `components` option
            // so that async components cache is hold on the original record
            components: d ? d.record.components : _.components,
            path: V,
            // we might be the child of an alias
            aliasOf: d ? d.record : _
            // the aliases are always of the same kind as the original since they
            // are defined on the same record
          }))
        );
    }
    let R, N;
    for (const x of k) {
      const { path: V } = x;
      if (c && V[0] !== "/") {
        const L = c.record.path, T = L[L.length - 1] === "/" ? "" : "/";
        x.path = c.record.path + (V && T + V);
      }
      if (process.env.NODE_ENV !== "production" && x.path === "*")
        throw new Error(`Catch all routes ("*") must now be defined using a param with a custom regexp.
See more at https://router.vuejs.org/guide/migration/#Removed-star-or-catch-all-routes.`);
      if (R = Un(x, c, S), process.env.NODE_ENV !== "production" && c && V[0] === "/" && zn(R, c), d ? (d.alias.push(R), process.env.NODE_ENV !== "production" && Kn(d, R)) : (N = N || R, N !== R && N.alias.push(R), y && s.name && !qe(R) && (process.env.NODE_ENV !== "production" && Wn(s, c), f(s.name))), yt(R) && l(R), _.children) {
        const L = _.children;
        for (let T = 0; T < L.length; T++)
          u(L[T], R, d && d.children[T]);
      }
      d = d || R;
    }
    return N ? () => {
      f(N);
    } : X;
  }
  function f(s) {
    if (gt(s)) {
      const c = o.get(s);
      c && (o.delete(s), n.splice(n.indexOf(c), 1), c.children.forEach(f), c.alias.forEach(f));
    } else {
      const c = n.indexOf(s);
      c > -1 && (n.splice(c, 1), s.record.name && o.delete(s.record.name), s.children.forEach(f), s.alias.forEach(f));
    }
  }
  function h() {
    return n;
  }
  function l(s) {
    const c = Qn(s, n);
    n.splice(c, 0, s), s.record.name && !qe(s) && o.set(s.record.name, s);
  }
  function p(s, c) {
    let d, y = {}, _, S;
    if ("name" in s && s.name) {
      if (d = o.get(s.name), !d)
        throw Q(1, {
          location: s
        });
      if (process.env.NODE_ENV !== "production") {
        const N = Object.keys(s.params || {}).filter((x) => !d.keys.find((V) => V.name === x));
        N.length && E(`Discarded invalid param(s) "${N.join('", "')}" when navigating. See https://github.com/vuejs/router/blob/main/packages/router/CHANGELOG.md#414-2022-08-22 for more details.`);
      }
      S = d.record.name, y = O(
        // paramsFromLocation is a new object
        Ge(
          c.params,
          // only keep params that exist in the resolved location
          // only keep optional params coming from a parent record
          d.keys.filter((N) => !N.optional).concat(d.parent ? d.parent.keys.filter((N) => N.optional) : []).map((N) => N.name)
        ),
        // discard any existing params in the current location that do not exist here
        // #1497 this ensures better active/exact matching
        s.params && Ge(s.params, d.keys.map((N) => N.name))
      ), _ = d.stringify(y);
    } else if (s.path != null)
      _ = s.path, process.env.NODE_ENV !== "production" && !_.startsWith("/") && E(`The Matcher cannot resolve relative paths but received "${_}". Unless you directly called \`matcher.resolve("${_}")\`, this is probably a bug in vue-router. Please open an issue at https://github.com/vuejs/router/issues/new/choose.`), d = n.find((N) => N.re.test(_)), d && (y = d.parse(_), S = d.record.name);
    else {
      if (d = c.name ? o.get(c.name) : n.find((N) => N.re.test(c.path)), !d)
        throw Q(1, {
          location: s,
          currentLocation: c
        });
      S = d.record.name, y = O({}, c.params, s.params), _ = d.stringify(y);
    }
    const k = [];
    let R = d;
    for (; R; )
      k.unshift(R.record), R = R.parent;
    return {
      name: S,
      path: _,
      params: y,
      matched: k,
      meta: Gn(k)
    };
  }
  e.forEach((s) => u(s));
  function a() {
    n.length = 0, o.clear();
  }
  return {
    addRoute: u,
    resolve: p,
    removeRoute: f,
    clearRoutes: a,
    getRoutes: h,
    getRecordMatcher: r
  };
}
function Ge(e, t) {
  const n = {};
  for (const o of t)
    o in e && (n[o] = e[o]);
  return n;
}
function Ke(e) {
  const t = {
    path: e.path,
    redirect: e.redirect,
    name: e.name,
    meta: e.meta || {},
    aliasOf: e.aliasOf,
    beforeEnter: e.beforeEnter,
    props: Hn(e),
    children: e.children || [],
    instances: {},
    leaveGuards: /* @__PURE__ */ new Set(),
    updateGuards: /* @__PURE__ */ new Set(),
    enterCallbacks: {},
    // must be declared afterwards
    // mods: {},
    components: "components" in e ? e.components || null : e.component && { default: e.component }
  };
  return Object.defineProperty(t, "mods", {
    value: {}
  }), t;
}
function Hn(e) {
  const t = {}, n = e.props || !1;
  if ("component" in e)
    t.default = n;
  else
    for (const o in e.components)
      t[o] = typeof n == "object" ? n[o] : n;
  return t;
}
function qe(e) {
  for (; e; ) {
    if (e.record.aliasOf)
      return !0;
    e = e.parent;
  }
  return !1;
}
function Gn(e) {
  return e.reduce((t, n) => O(t, n.meta), {});
}
function We(e, t) {
  const n = {};
  for (const o in e)
    n[o] = o in t ? t[o] : e[o];
  return n;
}
function we(e, t) {
  return e.name === t.name && e.optional === t.optional && e.repeatable === t.repeatable;
}
function Kn(e, t) {
  for (const n of e.keys)
    if (!n.optional && !t.keys.find(we.bind(null, n)))
      return E(`Alias "${t.record.path}" and the original record: "${e.record.path}" must have the exact same param named "${n.name}"`);
  for (const n of t.keys)
    if (!n.optional && !e.keys.find(we.bind(null, n)))
      return E(`Alias "${t.record.path}" and the original record: "${e.record.path}" must have the exact same param named "${n.name}"`);
}
function qn(e, t) {
  t && t.record.name && !e.name && !e.path && E(`The route named "${String(t.record.name)}" has a child without a name and an empty path. Using that name won't render the empty path child so you probably want to move the name to the child instead. If this is intentional, add a name to the child route to remove the warning.`);
}
function Wn(e, t) {
  for (let n = t; n; n = n.parent)
    if (n.record.name === e.name)
      throw new Error(`A route named "${String(e.name)}" has been added as a ${t === n ? "child" : "descendant"} of a route with the same name. Route names must be unique and a nested route cannot use the same name as an ancestor.`);
}
function zn(e, t) {
  for (const n of t.keys)
    if (!e.keys.find(we.bind(null, n)))
      return E(`Absolute path "${e.record.path}" must have the exact same param named "${n.name}" as its parent "${t.record.path}".`);
}
function Qn(e, t) {
  let n = 0, o = t.length;
  for (; n !== o; ) {
    const u = n + o >> 1;
    vt(e, t[u]) < 0 ? o = u : n = u + 1;
  }
  const r = Fn(e);
  return r && (o = t.lastIndexOf(r, o - 1), process.env.NODE_ENV !== "production" && o < 0 && E(`Finding ancestor route "${r.record.path}" failed for "${e.record.path}"`)), o;
}
function Fn(e) {
  let t = e;
  for (; t = t.parent; )
    if (yt(t) && vt(e, t) === 0)
      return t;
}
function yt({ record: e }) {
  return !!(e.name || e.components && Object.keys(e.components).length || e.redirect);
}
function Yn(e) {
  const t = {};
  if (e === "" || e === "?")
    return t;
  const o = (e[0] === "?" ? e.slice(1) : e).split("&");
  for (let r = 0; r < o.length; ++r) {
    const u = o[r].replace(ut, " "), f = u.indexOf("="), h = z(f < 0 ? u : u.slice(0, f)), l = f < 0 ? null : z(u.slice(f + 1));
    if (h in t) {
      let p = t[h];
      I(p) || (p = t[h] = [p]), p.push(l);
    } else
      t[h] = l;
  }
  return t;
}
function ze(e) {
  let t = "";
  for (let n in e) {
    const o = e[n];
    if (n = fn(n), o == null) {
      o !== void 0 && (t += (t.length ? "&" : "") + n);
      continue;
    }
    (I(o) ? o.map((u) => u && ye(u)) : [o && ye(o)]).forEach((u) => {
      u !== void 0 && (t += (t.length ? "&" : "") + n, u != null && (t += "=" + u));
    });
  }
  return t;
}
function Jn(e) {
  const t = {};
  for (const n in e) {
    const o = e[n];
    o !== void 0 && (t[n] = I(o) ? o.map((r) => r == null ? null : "" + r) : o == null ? o : "" + o);
  }
  return t;
}
const Xn = Symbol(process.env.NODE_ENV !== "production" ? "router view location matched" : ""), Qe = Symbol(process.env.NODE_ENV !== "production" ? "router view depth" : ""), Oe = Symbol(process.env.NODE_ENV !== "production" ? "router" : ""), _t = Symbol(process.env.NODE_ENV !== "production" ? "route location" : ""), be = Symbol(process.env.NODE_ENV !== "production" ? "router view location" : "");
function Y() {
  let e = [];
  function t(o) {
    return e.push(o), () => {
      const r = e.indexOf(o);
      r > -1 && e.splice(r, 1);
    };
  }
  function n() {
    e = [];
  }
  return {
    add: t,
    list: () => e.slice(),
    reset: n
  };
}
function B(e, t, n, o, r, u = (f) => f()) {
  const f = o && // name is defined if record is because of the function overload
  (o.enterCallbacks[r] = o.enterCallbacks[r] || []);
  return () => new Promise((h, l) => {
    const p = (c) => {
      c === !1 ? l(Q(4, {
        from: n,
        to: t
      })) : c instanceof Error ? l(c) : se(c) ? l(Q(2, {
        from: t,
        to: c
      })) : (f && // since enterCallbackArray is truthy, both record and name also are
      o.enterCallbacks[r] === f && typeof c == "function" && f.push(c), h());
    }, a = u(() => e.call(o && o.instances[r], t, n, process.env.NODE_ENV !== "production" ? Zn(p, t, n) : p));
    let s = Promise.resolve(a);
    if (e.length < 3 && (s = s.then(p)), process.env.NODE_ENV !== "production" && e.length > 2) {
      const c = `The "next" callback was never called inside of ${e.name ? '"' + e.name + '"' : ""}:
${e.toString()}
. If you are returning a value instead of calling "next", make sure to remove the "next" parameter from your function.`;
      if (typeof a == "object" && "then" in a)
        s = s.then((d) => p._called ? d : (E(c), Promise.reject(new Error("Invalid navigation guard"))));
      else if (a !== void 0 && !p._called) {
        E(c), l(new Error("Invalid navigation guard"));
        return;
      }
    }
    s.catch((c) => l(c));
  });
}
function Zn(e, t, n) {
  let o = 0;
  return function() {
    o++ === 1 && E(`The "next" callback was called more than once in one navigation guard when going from "${n.fullPath}" to "${t.fullPath}". It should be called exactly one time in each navigation guard. This will fail in production.`), e._called = !0, o === 1 && e.apply(null, arguments);
  };
}
function ge(e, t, n, o, r = (u) => u()) {
  const u = [];
  for (const f of e) {
    process.env.NODE_ENV !== "production" && !f.components && !f.children.length && E(`Record with path "${f.path}" is either missing a "component(s)" or "children" property.`);
    for (const h in f.components) {
      let l = f.components[h];
      if (process.env.NODE_ENV !== "production") {
        if (!l || typeof l != "object" && typeof l != "function")
          throw E(`Component "${h}" in record with path "${f.path}" is not a valid component. Received "${String(l)}".`), new Error("Invalid route component");
        if ("then" in l) {
          E(`Component "${h}" in record with path "${f.path}" is a Promise instead of a function that returns a Promise. Did you write "import('./MyPage.vue')" instead of "() => import('./MyPage.vue')" ? This will break in production if not fixed.`);
          const p = l;
          l = () => p;
        } else
          l.__asyncLoader && // warn only once per component
          !l.__warnedDefineAsync && (l.__warnedDefineAsync = !0, E(`Component "${h}" in record with path "${f.path}" is defined using "defineAsyncComponent()". Write "() => import('./MyPage.vue')" instead of "defineAsyncComponent(() => import('./MyPage.vue'))".`));
      }
      if (!(t !== "beforeRouteEnter" && !f.instances[h]))
        if (ct(l)) {
          const a = (l.__vccOpts || l)[t];
          a && u.push(B(a, n, o, f, h, r));
        } else {
          let p = l();
          process.env.NODE_ENV !== "production" && !("catch" in p) && (E(`Component "${h}" in record with path "${f.path}" is a function that does not return a Promise. If you were passing a functional component, make sure to add a "displayName" to the component. This will break in production if not fixed.`), p = Promise.resolve(p)), u.push(() => p.then((a) => {
            if (!a)
              throw new Error(`Couldn't resolve component "${h}" at "${f.path}"`);
            const s = Zt(a) ? a.default : a;
            f.mods[h] = a, f.components[h] = s;
            const d = (s.__vccOpts || s)[t];
            return d && B(d, n, o, f, h, r)();
          }));
        }
    }
  }
  return u;
}
function Fe(e) {
  const t = ee(Oe), n = ee(_t);
  let o = !1, r = null;
  const u = D(() => {
    const a = W(e.to);
    return process.env.NODE_ENV !== "production" && (!o || a !== r) && (se(a) || (o ? E(`Invalid value for prop "to" in useLink()
- to:`, a, `
- previous to:`, r, `
- props:`, e) : E(`Invalid value for prop "to" in useLink()
- to:`, a, `
- props:`, e)), r = a, o = !0), t.resolve(a);
  }), f = D(() => {
    const { matched: a } = u.value, { length: s } = a, c = a[s - 1], d = n.matched;
    if (!c || !d.length)
      return -1;
    const y = d.findIndex(H.bind(null, c));
    if (y > -1)
      return y;
    const _ = Ye(a[s - 2]);
    return (
      // we are dealing with nested routes
      s > 1 && // if the parent and matched route have the same path, this link is
      // referring to the empty child. Or we currently are on a different
      // child of the same parent
      Ye(c) === _ && // avoid comparing the child with its parent
      d[d.length - 1].path !== _ ? d.findIndex(H.bind(null, a[s - 2])) : y
    );
  }), h = D(() => f.value > -1 && ro(n.params, u.value.params)), l = D(() => f.value > -1 && f.value === n.matched.length - 1 && pt(n.params, u.value.params));
  function p(a = {}) {
    if (oo(a)) {
      const s = t[W(e.replace) ? "replace" : "push"](
        W(e.to)
        // avoid uncaught errors are they are logged anyway
      ).catch(X);
      return e.viewTransition && typeof document < "u" && "startViewTransition" in document && document.startViewTransition(() => s), s;
    }
    return Promise.resolve();
  }
  if (process.env.NODE_ENV !== "production" && M) {
    const a = st();
    if (a) {
      const s = {
        route: u.value,
        isActive: h.value,
        isExactActive: l.value,
        error: null
      };
      a.__vrl_devtools = a.__vrl_devtools || [], a.__vrl_devtools.push(s), jt(() => {
        s.route = u.value, s.isActive = h.value, s.isExactActive = l.value, s.error = se(W(e.to)) ? null : 'Invalid "to" value';
      }, { flush: "post" });
    }
  }
  return {
    route: u,
    href: D(() => u.value.href),
    isActive: h,
    isExactActive: l,
    navigate: p
  };
}
function eo(e) {
  return e.length === 1 ? e[0] : e;
}
const to = /* @__PURE__ */ nt({
  name: "RouterLink",
  compatConfig: { MODE: 3 },
  props: {
    to: {
      type: [String, Object],
      required: !0
    },
    replace: Boolean,
    activeClass: String,
    // inactiveClass: String,
    exactActiveClass: String,
    custom: Boolean,
    ariaCurrentValue: {
      type: String,
      default: "page"
    }
  },
  useLink: Fe,
  setup(e, { slots: t }) {
    const n = Vt(Fe(e)), { options: o } = ee(Oe), r = D(() => ({
      [Je(e.activeClass, o.linkActiveClass, "router-link-active")]: n.isActive,
      // [getLinkClass(
      //   props.inactiveClass,
      //   options.linkInactiveClass,
      //   'router-link-inactive'
      // )]: !link.isExactActive,
      [Je(e.exactActiveClass, o.linkExactActiveClass, "router-link-exact-active")]: n.isExactActive
    }));
    return () => {
      const u = t.default && eo(t.default(n));
      return e.custom ? u : ot("a", {
        "aria-current": n.isExactActive ? e.ariaCurrentValue : null,
        href: n.href,
        // this would override user added attrs but Vue will still add
        // the listener, so we end up triggering both
        onClick: n.navigate,
        class: r.value
      }, u);
    };
  }
}), no = to;
function oo(e) {
  if (!(e.metaKey || e.altKey || e.ctrlKey || e.shiftKey) && !e.defaultPrevented && !(e.button !== void 0 && e.button !== 0)) {
    if (e.currentTarget && e.currentTarget.getAttribute) {
      const t = e.currentTarget.getAttribute("target");
      if (/\b_blank\b/i.test(t))
        return;
    }
    return e.preventDefault && e.preventDefault(), !0;
  }
}
function ro(e, t) {
  for (const n in t) {
    const o = t[n], r = e[n];
    if (typeof o == "string") {
      if (o !== r)
        return !1;
    } else if (!I(r) || r.length !== o.length || o.some((u, f) => u !== r[f]))
      return !1;
  }
  return !0;
}
function Ye(e) {
  return e ? e.aliasOf ? e.aliasOf.path : e.path : "";
}
const Je = (e, t, n) => e ?? t ?? n, so = /* @__PURE__ */ nt({
  name: "RouterView",
  // #674 we manually inherit them
  inheritAttrs: !1,
  props: {
    name: {
      type: String,
      default: "default"
    },
    route: Object
  },
  // Better compat for @vue/compat users
  // https://github.com/vuejs/router/issues/1315
  compatConfig: { MODE: 3 },
  setup(e, { attrs: t, slots: n }) {
    process.env.NODE_ENV !== "production" && ao();
    const o = ee(be), r = D(() => e.route || o.value), u = ee(Qe, 0), f = D(() => {
      let p = W(u);
      const { matched: a } = r.value;
      let s;
      for (; (s = a[p]) && !s.components; )
        p++;
      return p;
    }), h = D(() => r.value.matched[f.value]);
    he(Qe, D(() => f.value + 1)), he(Xn, h), he(be, r);
    const l = Dt();
    return rt(() => [l.value, h.value, e.name], ([p, a, s], [c, d, y]) => {
      a && (a.instances[s] = p, d && d !== a && p && p === c && (a.leaveGuards.size || (a.leaveGuards = d.leaveGuards), a.updateGuards.size || (a.updateGuards = d.updateGuards))), p && a && // if there is no instance but to and from are the same this might be
      // the first visit
      (!d || !H(a, d) || !c) && (a.enterCallbacks[s] || []).forEach((_) => _(p));
    }, { flush: "post" }), () => {
      const p = r.value, a = e.name, s = h.value, c = s && s.components[a];
      if (!c)
        return Xe(n.default, { Component: c, route: p });
      const d = s.props[a], y = d ? d === !0 ? p.params : typeof d == "function" ? d(p) : d : null, S = ot(c, O({}, y, t, {
        onVnodeUnmounted: (k) => {
          k.component.isUnmounted && (s.instances[a] = null);
        },
        ref: l
      }));
      if (process.env.NODE_ENV !== "production" && M && S.ref) {
        const k = {
          depth: f.value,
          name: s.name,
          path: s.path,
          meta: s.meta
        };
        (I(S.ref) ? S.ref.map((N) => N.i) : [S.ref.i]).forEach((N) => {
          N.__vrv_devtools = k;
        });
      }
      return (
        // pass the vnode to the slot as a prop.
        // h and <component :is="..."> both accept vnodes
        Xe(n.default, { Component: S, route: p }) || S
      );
    };
  }
});
function Xe(e, t) {
  if (!e)
    return null;
  const n = e(t);
  return n.length === 1 ? n[0] : n;
}
const io = so;
function ao() {
  const e = st(), t = e.parent && e.parent.type.name, n = e.parent && e.parent.subTree && e.parent.subTree.type;
  if (t && (t === "KeepAlive" || t.includes("Transition")) && typeof n == "object" && n.name === "RouterView") {
    const o = t === "KeepAlive" ? "keep-alive" : "transition";
    E(`<router-view> can no longer be used directly inside <transition> or <keep-alive>.
Use slot props instead:

<router-view v-slot="{ Component }">
  <${o}>
    <component :is="Component" />
  </${o}>
</router-view>`);
  }
}
function J(e, t) {
  const n = O({}, e, {
    // remove variables that can contain vue instances
    matched: e.matched.map((o) => _o(o, ["instances", "children", "aliasOf"]))
  });
  return {
    _custom: {
      type: null,
      readOnly: !0,
      display: e.fullPath,
      tooltip: t,
      value: n
    }
  };
}
function re(e) {
  return {
    _custom: {
      display: e
    }
  };
}
let co = 0;
function lo(e, t, n) {
  if (t.__hasDevtools)
    return;
  t.__hasDevtools = !0;
  const o = co++;
  Xt({
    id: "org.vuejs.router" + (o ? "." + o : ""),
    label: "Vue Router",
    packageName: "vue-router",
    homepage: "https://router.vuejs.org",
    logo: "https://router.vuejs.org/logo.png",
    componentStateTypes: ["Routing"],
    app: e
  }, (r) => {
    typeof r.now != "function" && console.warn("[Vue Router]: You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."), r.on.inspectComponent((a, s) => {
      a.instanceData && a.instanceData.state.push({
        type: "Routing",
        key: "$route",
        editable: !1,
        value: J(t.currentRoute.value, "Current Route")
      });
    }), r.on.visitComponentTree(({ treeNode: a, componentInstance: s }) => {
      if (s.__vrv_devtools) {
        const c = s.__vrv_devtools;
        a.tags.push({
          label: (c.name ? `${c.name.toString()}: ` : "") + c.path,
          textColor: 0,
          tooltip: "This component is rendered by &lt;router-view&gt;",
          backgroundColor: Et
        });
      }
      I(s.__vrl_devtools) && (s.__devtoolsApi = r, s.__vrl_devtools.forEach((c) => {
        let d = c.route.path, y = Rt, _ = "", S = 0;
        c.error ? (d = c.error, y = mo, S = go) : c.isExactActive ? (y = bt, _ = "This is exactly active") : c.isActive && (y = wt, _ = "This link is active"), a.tags.push({
          label: d,
          textColor: S,
          tooltip: _,
          backgroundColor: y
        });
      }));
    }), rt(t.currentRoute, () => {
      l(), r.notifyComponentUpdate(), r.sendInspectorTree(h), r.sendInspectorState(h);
    });
    const u = "router:navigations:" + o;
    r.addTimelineLayer({
      id: u,
      label: `Router${o ? " " + o : ""} Navigations`,
      color: 4237508
    }), t.onError((a, s) => {
      r.addTimelineEvent({
        layerId: u,
        event: {
          title: "Error during Navigation",
          subtitle: s.fullPath,
          logType: "error",
          time: r.now(),
          data: { error: a },
          groupId: s.meta.__navigationId
        }
      });
    });
    let f = 0;
    t.beforeEach((a, s) => {
      const c = {
        guard: re("beforeEach"),
        from: J(s, "Current Location during this navigation"),
        to: J(a, "Target location")
      };
      Object.defineProperty(a.meta, "__navigationId", {
        value: f++
      }), r.addTimelineEvent({
        layerId: u,
        event: {
          time: r.now(),
          title: "Start of navigation",
          subtitle: a.fullPath,
          data: c,
          groupId: a.meta.__navigationId
        }
      });
    }), t.afterEach((a, s, c) => {
      const d = {
        guard: re("afterEach")
      };
      c ? (d.failure = {
        _custom: {
          type: Error,
          readOnly: !0,
          display: c ? c.message : "",
          tooltip: "Navigation Failure",
          value: c
        }
      }, d.status = re("❌")) : d.status = re("✅"), d.from = J(s, "Current Location during this navigation"), d.to = J(a, "Target location"), r.addTimelineEvent({
        layerId: u,
        event: {
          title: "End of navigation",
          subtitle: a.fullPath,
          time: r.now(),
          data: d,
          logType: c ? "warning" : "default",
          groupId: a.meta.__navigationId
        }
      });
    });
    const h = "router-inspector:" + o;
    r.addInspector({
      id: h,
      label: "Routes" + (o ? " " + o : ""),
      icon: "book",
      treeFilterPlaceholder: "Search routes"
    });
    function l() {
      if (!p)
        return;
      const a = p;
      let s = n.getRoutes().filter((c) => !c.parent || // these routes have a parent with no component which will not appear in the view
      // therefore we still need to include them
      !c.parent.record.components);
      s.forEach(Pt), a.filter && (s = s.filter((c) => (
        // save matches state based on the payload
        Re(c, a.filter.toLowerCase())
      ))), s.forEach((c) => Ot(c, t.currentRoute.value)), a.rootNodes = s.map(Nt);
    }
    let p;
    r.on.getInspectorTree((a) => {
      p = a, a.app === e && a.inspectorId === h && l();
    }), r.on.getInspectorState((a) => {
      if (a.app === e && a.inspectorId === h) {
        const c = n.getRoutes().find((d) => d.record.__vd_id === a.nodeId);
        c && (a.state = {
          options: fo(c)
        });
      }
    }), r.sendInspectorTree(h), r.sendInspectorState(h);
  });
}
function uo(e) {
  return e.optional ? e.repeatable ? "*" : "?" : e.repeatable ? "+" : "";
}
function fo(e) {
  const { record: t } = e, n = [
    { editable: !1, key: "path", value: t.path }
  ];
  return t.name != null && n.push({
    editable: !1,
    key: "name",
    value: t.name
  }), n.push({ editable: !1, key: "regexp", value: e.re }), e.keys.length && n.push({
    editable: !1,
    key: "keys",
    value: {
      _custom: {
        type: null,
        readOnly: !0,
        display: e.keys.map((o) => `${o.name}${uo(o)}`).join(" "),
        tooltip: "Param keys",
        value: e.keys
      }
    }
  }), t.redirect != null && n.push({
    editable: !1,
    key: "redirect",
    value: t.redirect
  }), e.alias.length && n.push({
    editable: !1,
    key: "aliases",
    value: e.alias.map((o) => o.record.path)
  }), Object.keys(e.record.meta).length && n.push({
    editable: !1,
    key: "meta",
    value: e.record.meta
  }), n.push({
    key: "score",
    editable: !1,
    value: {
      _custom: {
        type: null,
        readOnly: !0,
        display: e.score.map((o) => o.join(", ")).join(" | "),
        tooltip: "Score used to sort routes",
        value: e.score
      }
    }
  }), n;
}
const Et = 15485081, wt = 2450411, bt = 8702998, ho = 2282478, Rt = 16486972, po = 6710886, mo = 16704226, go = 12131356;
function Nt(e) {
  const t = [], { record: n } = e;
  n.name != null && t.push({
    label: String(n.name),
    textColor: 0,
    backgroundColor: ho
  }), n.aliasOf && t.push({
    label: "alias",
    textColor: 0,
    backgroundColor: Rt
  }), e.__vd_match && t.push({
    label: "matches",
    textColor: 0,
    backgroundColor: Et
  }), e.__vd_exactActive && t.push({
    label: "exact",
    textColor: 0,
    backgroundColor: bt
  }), e.__vd_active && t.push({
    label: "active",
    textColor: 0,
    backgroundColor: wt
  }), n.redirect && t.push({
    label: typeof n.redirect == "string" ? `redirect: ${n.redirect}` : "redirects",
    textColor: 16777215,
    backgroundColor: po
  });
  let o = n.__vd_id;
  return o == null && (o = String(vo++), n.__vd_id = o), {
    id: o,
    label: n.path,
    tags: t,
    children: e.children.map(Nt)
  };
}
let vo = 0;
const yo = /^\/(.*)\/([a-z]*)$/;
function Ot(e, t) {
  const n = t.matched.length && H(t.matched[t.matched.length - 1], e.record);
  e.__vd_exactActive = e.__vd_active = n, n || (e.__vd_active = t.matched.some((o) => H(o, e.record))), e.children.forEach((o) => Ot(o, t));
}
function Pt(e) {
  e.__vd_match = !1, e.children.forEach(Pt);
}
function Re(e, t) {
  const n = String(e.re).match(yo);
  if (e.__vd_match = !1, !n || n.length < 3)
    return !1;
  if (new RegExp(n[1].replace(/\$$/, ""), n[2]).test(t))
    return e.children.forEach((f) => Re(f, t)), e.record.path !== "/" || t === "/" ? (e.__vd_match = e.re.test(t), !0) : !1;
  const r = e.record.path.toLowerCase(), u = z(r);
  return !t.startsWith("/") && (u.includes(t) || r.includes(t)) || u.startsWith(t) || r.startsWith(t) || e.record.name && String(e.record.name).includes(t) ? !0 : e.children.some((f) => Re(f, t));
}
function _o(e, t) {
  const n = {};
  for (const o in e)
    t.includes(o) || (n[o] = e[o]);
  return n;
}
function Eo(e) {
  const t = Bn(e.routes, e), n = e.parseQuery || Yn, o = e.stringifyQuery || ze, r = e.history;
  if (process.env.NODE_ENV !== "production" && !r)
    throw new Error('Provide the "history" option when calling "createRouter()": https://router.vuejs.org/api/interfaces/RouterOptions.html#history');
  const u = Y(), f = Y(), h = Y(), l = xt(U);
  let p = U;
  M && e.scrollBehavior && "scrollRestoration" in history && (history.scrollRestoration = "manual");
  const a = pe.bind(null, (i) => "" + i), s = pe.bind(null, hn), c = (
    // @ts-expect-error: intentionally avoid the type check
    pe.bind(null, z)
  );
  function d(i, g) {
    let m, v;
    return gt(i) ? (m = t.getRecordMatcher(i), process.env.NODE_ENV !== "production" && !m && E(`Parent route "${String(i)}" not found when adding child route`, g), v = g) : v = i, t.addRoute(v, m);
  }
  function y(i) {
    const g = t.getRecordMatcher(i);
    g ? t.removeRoute(g) : process.env.NODE_ENV !== "production" && E(`Cannot remove non-existent route "${String(i)}"`);
  }
  function _() {
    return t.getRoutes().map((i) => i.record);
  }
  function S(i) {
    return !!t.getRecordMatcher(i);
  }
  function k(i, g) {
    if (g = O({}, g || l.value), typeof i == "string") {
      const w = me(n, i, g.path), $ = t.resolve({ path: w.path }, g), G = r.createHref(w.fullPath);
      return process.env.NODE_ENV !== "production" && (G.startsWith("//") ? E(`Location "${i}" resolved to "${G}". A resolved location cannot start with multiple slashes.`) : $.matched.length || E(`No match found for location with path "${i}"`)), O(w, $, {
        params: c($.params),
        hash: z(w.hash),
        redirectedFrom: void 0,
        href: G
      });
    }
    if (process.env.NODE_ENV !== "production" && !se(i))
      return E(`router.resolve() was passed an invalid location. This will fail in production.
- Location:`, i), k({});
    let m;
    if (i.path != null)
      process.env.NODE_ENV !== "production" && "params" in i && !("name" in i) && // @ts-expect-error: the type is never
      Object.keys(i.params).length && E(`Path "${i.path}" was passed with params but they will be ignored. Use a named route alongside params instead.`), m = O({}, i, {
        path: me(n, i.path, g.path).path
      });
    else {
      const w = O({}, i.params);
      for (const $ in w)
        w[$] == null && delete w[$];
      m = O({}, i, {
        params: s(w)
      }), g.params = s(g.params);
    }
    const v = t.resolve(m, g), P = i.hash || "";
    process.env.NODE_ENV !== "production" && P && !P.startsWith("#") && E(`A \`hash\` should always start with the character "#". Replace "${P}" with "#${P}".`), v.params = a(c(v.params));
    const C = gn(o, O({}, i, {
      hash: un(P),
      path: v.path
    })), b = r.createHref(C);
    return process.env.NODE_ENV !== "production" && (b.startsWith("//") ? E(`Location "${i}" resolved to "${b}". A resolved location cannot start with multiple slashes.`) : v.matched.length || E(`No match found for location with path "${i.path != null ? i.path : i}"`)), O({
      fullPath: C,
      // keep the hash encoded so fullPath is effectively path + encodedQuery +
      // hash
      hash: P,
      query: (
        // if the user is using a custom query lib like qs, we might have
        // nested objects, so we keep the query as is, meaning it can contain
        // numbers at `$route.query`, but at the point, the user will have to
        // use their own type anyway.
        // https://github.com/vuejs/router/issues/328#issuecomment-649481567
        o === ze ? Jn(i.query) : i.query || {}
      )
    }, v, {
      redirectedFrom: void 0,
      href: b
    });
  }
  function R(i) {
    return typeof i == "string" ? me(n, i, l.value.path) : O({}, i);
  }
  function N(i, g) {
    if (p !== i)
      return Q(8, {
        from: g,
        to: i
      });
  }
  function x(i) {
    return T(i);
  }
  function V(i) {
    return x(O(R(i), { replace: !0 }));
  }
  function L(i) {
    const g = i.matched[i.matched.length - 1];
    if (g && g.redirect) {
      const { redirect: m } = g;
      let v = typeof m == "function" ? m(i) : m;
      if (typeof v == "string" && (v = v.includes("?") || v.includes("#") ? v = R(v) : (
        // force empty params
        { path: v }
      ), v.params = {}), process.env.NODE_ENV !== "production" && v.path == null && !("name" in v))
        throw E(`Invalid redirect found:
${JSON.stringify(v, null, 2)}
 when navigating to "${i.fullPath}". A redirect must contain a name or path. This will break in production.`), new Error("Invalid redirect");
      return O({
        query: i.query,
        hash: i.hash,
        // avoid transferring params if the redirect has a path
        params: v.path != null ? {} : i.params
      }, v);
    }
  }
  function T(i, g) {
    const m = p = k(i), v = l.value, P = i.state, C = i.force, b = i.replace === !0, w = L(m);
    if (w)
      return T(
        O(R(w), {
          state: typeof w == "object" ? O({}, P, w.state) : P,
          force: C,
          replace: b
        }),
        // keep original redirectedFrom if it exists
        g || m
      );
    const $ = m;
    $.redirectedFrom = g;
    let G;
    return !C && De(o, v, m) && (G = Q(16, { to: $, from: v }), Ae(
      v,
      v,
      // this is a push, the only way for it to be triggered from a
      // history.listen is with a redirect, which makes it become a push
      !0,
      // This cannot be the first navigation because the initial location
      // cannot be manually navigated to
      !1
    )), (G ? Promise.resolve(G) : Se($, v)).catch((A) => j(A) ? (
      // navigation redirects still mark the router as ready
      j(
        A,
        2
        /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */
      ) ? A : ue(A)
    ) : (
      // reject any unknown error
      le(A, $, v)
    )).then((A) => {
      if (A) {
        if (j(
          A,
          2
          /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */
        ))
          return process.env.NODE_ENV !== "production" && // we are redirecting to the same location we were already at
          De(o, k(A.to), $) && // and we have done it a couple of times
          g && // @ts-expect-error: added only in dev
          (g._count = g._count ? (
            // @ts-expect-error
            g._count + 1
          ) : 1) > 30 ? (E(`Detected a possibly infinite redirection in a navigation guard when going from "${v.fullPath}" to "${$.fullPath}". Aborting to avoid a Stack Overflow.
 Are you always returning a new location within a navigation guard? That would lead to this error. Only return when redirecting or aborting, that should fix this. This might break in production if not fixed.`), Promise.reject(new Error("Infinite redirect in navigation guard"))) : T(
            // keep options
            O({
              // preserve an existing replacement but allow the redirect to override it
              replace: b
            }, R(A.to), {
              state: typeof A.to == "object" ? O({}, P, A.to.state) : P,
              force: C
            }),
            // preserve the original redirectedFrom if any
            g || $
          );
      } else
        A = $e($, v, !0, b, P);
      return ke($, v, A), A;
    });
  }
  function St(i, g) {
    const m = N(i, g);
    return m ? Promise.reject(m) : Promise.resolve();
  }
  function ae(i) {
    const g = oe.values().next().value;
    return g && typeof g.runWithContext == "function" ? g.runWithContext(i) : i();
  }
  function Se(i, g) {
    let m;
    const [v, P, C] = wo(i, g);
    m = ge(v.reverse(), "beforeRouteLeave", i, g);
    for (const w of v)
      w.leaveGuards.forEach(($) => {
        m.push(B($, i, g));
      });
    const b = St.bind(null, i, g);
    return m.push(b), K(m).then(() => {
      m = [];
      for (const w of u.list())
        m.push(B(w, i, g));
      return m.push(b), K(m);
    }).then(() => {
      m = ge(P, "beforeRouteUpdate", i, g);
      for (const w of P)
        w.updateGuards.forEach(($) => {
          m.push(B($, i, g));
        });
      return m.push(b), K(m);
    }).then(() => {
      m = [];
      for (const w of C)
        if (w.beforeEnter)
          if (I(w.beforeEnter))
            for (const $ of w.beforeEnter)
              m.push(B($, i, g));
          else
            m.push(B(w.beforeEnter, i, g));
      return m.push(b), K(m);
    }).then(() => (i.matched.forEach((w) => w.enterCallbacks = {}), m = ge(C, "beforeRouteEnter", i, g, ae), m.push(b), K(m))).then(() => {
      m = [];
      for (const w of f.list())
        m.push(B(w, i, g));
      return m.push(b), K(m);
    }).catch((w) => j(
      w,
      8
      /* ErrorTypes.NAVIGATION_CANCELLED */
    ) ? w : Promise.reject(w));
  }
  function ke(i, g, m) {
    h.list().forEach((v) => ae(() => v(i, g, m)));
  }
  function $e(i, g, m, v, P) {
    const C = N(i, g);
    if (C)
      return C;
    const b = g === U, w = M ? history.state : {};
    m && (v || b ? r.replace(i.fullPath, O({
      scroll: b && w && w.scroll
    }, P)) : r.push(i.fullPath, P)), l.value = i, Ae(i, g, m, b), ue();
  }
  let F;
  function kt() {
    F || (F = r.listen((i, g, m) => {
      if (!xe.listening)
        return;
      const v = k(i), P = L(v);
      if (P) {
        T(O(P, { replace: !0, force: !0 }), v).catch(X);
        return;
      }
      p = v;
      const C = l.value;
      M && Nn(Me(C.fullPath, m.delta), ie()), Se(v, C).catch((b) => j(
        b,
        12
        /* ErrorTypes.NAVIGATION_CANCELLED */
      ) ? b : j(
        b,
        2
        /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */
      ) ? (T(
        O(R(b.to), {
          force: !0
        }),
        v
        // avoid an uncaught rejection, let push call triggerError
      ).then((w) => {
        j(
          w,
          20
          /* ErrorTypes.NAVIGATION_DUPLICATED */
        ) && !m.delta && m.type === te.pop && r.go(-1, !1);
      }).catch(X), Promise.reject()) : (m.delta && r.go(-m.delta, !1), le(b, v, C))).then((b) => {
        b = b || $e(
          // after navigation, all matched components are resolved
          v,
          C,
          !1
        ), b && (m.delta && // a new navigation has been triggered, so we do not want to revert, that will change the current history
        // entry while a different route is displayed
        !j(
          b,
          8
          /* ErrorTypes.NAVIGATION_CANCELLED */
        ) ? r.go(-m.delta, !1) : m.type === te.pop && j(
          b,
          20
          /* ErrorTypes.NAVIGATION_DUPLICATED */
        ) && r.go(-1, !1)), ke(v, C, b);
      }).catch(X);
    }));
  }
  let ce = Y(), Ce = Y(), ne;
  function le(i, g, m) {
    ue(i);
    const v = Ce.list();
    return v.length ? v.forEach((P) => P(i, g, m)) : (process.env.NODE_ENV !== "production" && E("uncaught error during route navigation:"), console.error(i)), Promise.reject(i);
  }
  function $t() {
    return ne && l.value !== U ? Promise.resolve() : new Promise((i, g) => {
      ce.add([i, g]);
    });
  }
  function ue(i) {
    return ne || (ne = !i, kt(), ce.list().forEach(([g, m]) => i ? m(i) : g()), ce.reset()), i;
  }
  function Ae(i, g, m, v) {
    const { scrollBehavior: P } = e;
    if (!M || !P)
      return Promise.resolve();
    const C = !m && On(Me(i.fullPath, 0)) || (v || !m) && history.state && history.state.scroll || null;
    return Tt().then(() => P(i, g, C)).then((b) => b && Rn(b)).catch((b) => le(b, i, g));
  }
  const fe = (i) => r.go(i);
  let de;
  const oe = /* @__PURE__ */ new Set(), xe = {
    currentRoute: l,
    listening: !0,
    addRoute: d,
    removeRoute: y,
    clearRoutes: t.clearRoutes,
    hasRoute: S,
    getRoutes: _,
    resolve: k,
    options: e,
    push: x,
    replace: V,
    go: fe,
    back: () => fe(-1),
    forward: () => fe(1),
    beforeEach: u.add,
    beforeResolve: f.add,
    afterEach: h.add,
    onError: Ce.add,
    isReady: $t,
    install(i) {
      const g = this;
      i.component("RouterLink", no), i.component("RouterView", io), i.config.globalProperties.$router = g, Object.defineProperty(i.config.globalProperties, "$route", {
        enumerable: !0,
        get: () => W(l)
      }), M && // used for the initial navigation client side to avoid pushing
      // multiple times when the router is used in multiple apps
      !de && l.value === U && (de = !0, x(r.location).catch((P) => {
        process.env.NODE_ENV !== "production" && E("Unexpected error when starting the router:", P);
      }));
      const m = {};
      for (const P in U)
        Object.defineProperty(m, P, {
          get: () => l.value[P],
          enumerable: !0
        });
      i.provide(Oe, g), i.provide(_t, It(m)), i.provide(be, l);
      const v = i.unmount;
      oe.add(i), i.unmount = function() {
        oe.delete(i), oe.size < 1 && (p = U, F && F(), F = null, l.value = U, de = !1, ne = !1), v();
      }, process.env.NODE_ENV !== "production" && M && lo(i, g, t);
    }
  };
  function K(i) {
    return i.reduce((g, m) => g.then(() => ae(m)), Promise.resolve());
  }
  return xe;
}
function wo(e, t) {
  const n = [], o = [], r = [], u = Math.max(t.matched.length, e.matched.length);
  for (let f = 0; f < u; f++) {
    const h = t.matched[f];
    h && (e.matched.find((p) => H(p, h)) ? o.push(h) : n.push(h));
    const l = e.matched[f];
    l && (t.matched.find((p) => H(p, l)) || r.push(l));
  }
  return [n, o, r];
}
const bo = {
  name: "HomeView",
  data() {
    return {
      message: "Bem-vindo ao Gerenciador de Ofertas!"
    };
  }
}, Ro = { class: "home" };
function No(e, t, n, o, r, u) {
  return Ze(), et("div", Ro, [
    tt("h1", null, Mt(r.message), 1)
  ]);
}
const Oo = /* @__PURE__ */ it(bo, [["render", No], ["__scopeId", "data-v-e30771c5"]]), Po = [
  {
    path: "/",
    name: "home",
    component: Oo
  }
], So = Eo({
  history: $n("/"),
  routes: Po
}), Pe = Lt(Kt);
Pe.use(Ut());
Pe.use(So);
Pe.mount("#app");
