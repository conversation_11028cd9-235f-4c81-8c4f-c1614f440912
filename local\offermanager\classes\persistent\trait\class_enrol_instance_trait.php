<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\persistent\trait;

use local_offermanager\enrol_setup;
use local_offermanager\event\offer_class_updated;
use local_offermanager\persistent\offer_class_model;
use local_offermanager\persistent\offer_course_model;
use moodle_exception;
use Exception;
use stdClass;

defined('MOODLE_INTERNAL') || die();

/**
 * Trait class_enrol_instance_trait
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
trait class_enrol_instance_trait
{
    /** @var stdClass|null Instância de inscrição (enrol) */
    protected $enrolinstance = null;

    /** @var enrol_plugin|null Plugin de inscrição */
    protected $plugin = null;

    protected $fields_map;

    public function get_mapped_field(string $field)
    {
        $fields_map = $this->get_fieldsmap();
        $instance = $this->get_enrol_instance();
        if (!$fields_map) {
            throw new moodle_exception('error:field_not_mapped', 'local_offermanager');
        }

        return $instance->{$fields_map[$field]};
    }

    public function get_fieldsmap()
    {
        if (!$this->fields_map) {
            $this->fields_map = enrol_setup::FIELDS_MAP;
        }

        return $this->fields_map;
    }
    /**
     * Adiciona uma instância
     * 
     * @param string $enrol
     * @param int $offercourseid
     * @param string $classname
     * @param int $startdate
     * @param int[] $teacher
     * @param array $optional_fields
     * 
     * @return object $enrolinstance
     */
    public static function add_instance(
        $enrol,
        $offercourseid,
        $classname,
        $startdate,
        $teachers = [],
        $optional_fields = []
    ) {
        $offer_enrol_plugins = enrol_setup::get_dependent_enrol_plugins();

        if (!in_array($enrol, $offer_enrol_plugins)) {
            throw new moodle_exception('error:enrol_plugin_not_found', 'local_offermanager');
        }

        $enrolplugin = enrol_get_plugin($enrol);

        if (!$enrolplugin) {
            throw new moodle_exception('error:enrol_plugin_not_found', 'local_offermanager');
        }

        $offer_course = offer_course_model::get_record(['id' => $offercourseid]);

        if (!$offer_course) {
            throw new moodle_exception('error:offer_course_not_found', 'local_offermanager');
        }

        $course = $offer_course->get_course();

        if (!$course) {
            throw new moodle_exception('error:course_not_found', 'local_offermanager');
        }

        $fields_array = [
            'offercourseid' => $offer_course->get('id'),
            'classname' => $classname,
            'startdate' => $startdate
        ];

        foreach ($optional_fields as $name => $value) {
            $fields_array[$name] = $value;
        }

        $instanceid = $enrolplugin->add_instance($course, $fields_array);

        if (!$instanceid) {
            throw new moodle_exception('error:cannot_created_offer_class', 'local_offermanager');
        }

        $offer_class = offer_class_model::get_by_enrolid($instanceid);

        if ($teachers) {
            $offer_class->update_teachers($teachers);
        }

        $offer_class->fetch_enrol_instance();

        return $offer_class->get_enrol_instance();
    }

    /**
     * Atualiza uma instância
     * 
     * @param int $offerclassid
     * @param string|null $classname
     * @param int|null $startdate
     * @param int[] $teacher
     * @param array $optional_fields
     * 
     * @return object $enrolinstance
     */
    public function update_instance(
        $offerclassid,
        $classname = null,
        $startdate = null,
        $teachers = [],
        $optional_fields = []
    ) {
        $offer_class = new offer_class_model($offerclassid);

        if (!$offer_class->get('id')) {
            throw new moodle_exception('error:offer_class_not_found', 'local_offermanager');
        }

        $enrolplugin = $this->get_plugin();

        if (!$enrolplugin) {
            throw new moodle_exception('error:enrol_plugin_not_found', 'local_offermanager');
        }

        $instance = $offer_class->get_enrol_instance();

        $fields_array = [];

        $fields_array['classname'] = $classname ?? $instance->name;
        $fields_array['startdate'] = $startdate ?? $instance->enrolstartdate;

        foreach ($optional_fields as $name => $value) {
            $fields_array[$name] = $value;
        }

        $return = $enrolplugin->update_instance($instance, $fields_array);

        if (!$return) {
            throw new moodle_exception('error:cannot_created_offer_class', 'local_offermanager');
        }

        $offer_class->fetch_enrol_instance();

        $offer_class->update_teachers($teachers);

        $event = offer_class_updated::instance($this);
        $event->trigger();

        return $offer_class->get_enrol_instance();
    }


    public function delete_instance()
    {
        global $DB;

        $instance = $this->get_enrol_instance();

        if (!$instance) {
            throw new moodle_exception('error:enrol_instance_not_found', 'local_offermanager');
        }

        $plugin = $this->get_plugin();

        if (!$plugin) {
            throw new moodle_exception('error:enrol_plugin_not_found', 'local_offermanager');
        }

        $transaction = $DB->start_delegated_transaction();

        $offerclassid = $this->get('id');

        
        try {
            
            $plugin->delete_instance($instance);
           
            if (!offer_class_model::record_exists($offerclassid)) {
                $transaction->allow_commit();
                return true;
            } else {
                return false;
            }
        } catch (Exception $e) {
            $transaction->rollback($e);
            throw $e;
        }
    }

    /**
     * Atualiza a instância de inscrição (enrol) em cache com os dados mais recentes do banco de dados.
     *
     * @return stdClass
     */
    public function fetch_enrol_instance()
    {
        global $DB;

        $this->enrolinstance = $DB->get_record('enrol', ['id' => $this->get('enrolid')]);

        return $this->enrolinstance;
    }

    /**
     * Retorna a instância de inscrição (enrol) associada.
     *
     * @return stdClass
     * @throws moodle_exception
     */
    public function get_enrol_instance(): stdClass
    {
        if (is_null($this->enrolinstance)) {
            $instance = $this->fetch_enrol_instance();

            if (!$instance) {
                throw new moodle_exception('error:enrol_instance_not_found', 'local_offermanager');
            }
        }
        return $this->enrolinstance;
    }

    /**
     * Retorna a instância associadas a instância de inscrição.
     *
     * @return object|bool false if doesn't exists
     */
    public static function get_by_enrolid(int $enrolid): object|bool
    {
        return self::get_record([
            'enrolid' => $enrolid
        ]);
    }

    /**
     * Retorna o plugin de inscrição associado.
     *
     * @return enrol_plugin
     * @throws moodle_exception
     */
    public function get_plugin()
    {
        if (is_null($this->plugin)) {
            $enrol_instance = $this->get_enrol_instance();
            $this->plugin = enrol_get_plugin($enrol_instance->enrol);
            if (!$this->plugin) {
                throw new moodle_exception('error:enrol_plugin_not_found', 'local_offermanager');
            }
        }
        return $this->plugin;
    }

    /**
     * Retorna os dados da extensão da turma.
     * 
     * @return object
     * @throws moodle_exception
     */
    public function get_extension_data()
    {
        return json_decode($this->get_mapped_field('extensiondata'));
    }

    public function enable_enrol_instance()
    {
        $plugin = $this->get_plugin();
        $instance = $this->get_enrol_instance();
        $plugin->update_status($instance, ENROL_INSTANCE_ENABLED);
    }
}
