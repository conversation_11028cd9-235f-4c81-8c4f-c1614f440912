export const ajax = async (methodname, args) => {
  try {
    const url = window.M.cfg.wwwroot + '/lib/ajax/service.php?sesskey=' + window.M.cfg.sesskey + '&info=' + methodname;
    const data = {
      index: 0,
      methodname,
      args
    };

    const result = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify([data])
    });

    // Primeiro, clona a resposta para poder ler o corpo duas vezes se necessário
    const clonedResponse = result.clone();

    try {
      const jsonResult = await result.json();
      return jsonResult[0];
    } catch (parseError) {
      // Se a resposta não for um JSON válido, retorna a resposta como texto
      const textResult = await clonedResponse.text();
      return { error: textResult };
    }
  } catch (error) {
    console.error('Erro na chamada AJAX:', error);
    throw error;
  }
};

export const requestWithFiles = async (request, files) => {
  try {
    const formData = new FormData();
    formData.append('sesskey', window.M.cfg.sesskey);
    formData.append('info', request.methodname);
    formData.append('args', JSON.stringify(request.args));

    Object.entries(files).forEach(([key, file]) => {
      formData.append(key, file);
    });

    const response = await fetch(window.M.cfg.wwwroot + '/lib/ajax/service.php', {
      method: 'POST',
      body: formData
    });

    // Primeiro, clona a resposta para poder ler o corpo duas vezes se necessário
    const clonedResponse = response.clone();

    try {
      const result = await response.json();
      return result[0];
    } catch (parseError) {
      // Se a resposta não for um JSON válido, retorna a resposta como texto
      const textResult = await clonedResponse.text();
      return { error: textResult };
    }
  } catch (error) {
    console.error('Erro no upload de arquivo:', error);
    throw error;
  }
};
