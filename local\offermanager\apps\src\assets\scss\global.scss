/* Estilos globais para elementos comuns */

/* Garantir que os popovers fiquem acima dos modais */
.popover {
  z-index: 100000 !important;
}

/* Cabeçalhos */
.header-container {
  margin-bottom: 2rem;

  h1 {
    font-size: 1.75rem;
    font-weight: 600;
    color: #fff;
    margin: 0;
  }
}

/* Containers */
.content-container {
  background-color: #212529;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

/* Tabelas */
.table-container {
  overflow-x: auto;
  background-color: #212529;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

/* Botões de ação */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 5px;
}

/* Botão de ação padrão para todos os componentes */
.btn-action {
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;

    &:hover {
      background-color: transparent;
    }
  }

  img {
    width: 16px;
    height: 16px;
  }

  i {
    font-size: 1.25rem;
  }
}

/* Status badges */
.status-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 500;
  text-align: center;

  &.active {
    background-color: rgba(40, 167, 69, 0.2);
    color: #28a745;
  }

  &.inactive {
    background-color: rgba(220, 53, 69, 0.2);
    color: #dc3545;
  }
}

/* Formulários */
.form-group {
  margin-bottom: 1.5rem;

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 2rem;

  > * {
    margin-left: 1rem;
  }
}

/* Posicionamento específico */
.new-offer-container {
  margin-left: auto;

  /* Tablet específico - manter à direita */
  @media (max-width: 1024px) and (min-width: 769px) {
    width: auto;
    margin-left: auto;
    margin-top: 1rem;
    display: flex;
    justify-content: flex-end;
  }

  @media (max-width: 768px) {
    width: 100%;
    margin-left: 0;
    margin-top: 1rem;
    display: flex;
    justify-content: flex-end;
  }

  /* Celular específico (390x844) */
  @media (max-width: 390px) {
    width: 100%;
    margin-left: 0;
    margin-top: 1rem;
    display: flex;
    justify-content: flex-end;
  }
}

.fa-trash {
  color: #dc3545;
}