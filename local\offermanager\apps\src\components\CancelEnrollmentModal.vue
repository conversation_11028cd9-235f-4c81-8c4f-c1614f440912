<template>
  <div v-if="show" class="modal-backdrop" @click="$emit('close')">
    <div class="modal-container" @click.stop>
      <!-- Cabeçalho do modal -->
      <div class="modal-header">
        <h3 class="modal-title">Cancelar matrícula</h3>
        <button class="modal-close" @click="$emit('close')">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <!-- Corpo do modal -->
      <div class="modal-body">
        <div class="confirmation-text">
          <p>Você tem certeza de que deseja cancelar esta matrícula?</p>
        </div>
        
        <div class="user-info">
          <p>Ao cancelar a matrícula do usuário "<strong>{{ user?.fullName || '<nome_do_usuário>' }}</strong>", o progresso dele até aqui será salvo e ele não terá mais acesso ao conteúdo do curso.</p>
        </div>

        <div class="reason-section">
          <label class="reason-label">Justificativa do cancelamento</label>
          <div class="text-editor-wrapper">
            <TextEditor 
              v-model="cancellationReason" 
              :placeholder="'Digite aqui a justificativa motivo do cancelamento da matrícula'"
              :rows="6"
            />
          </div>
        </div>
      </div>

      <!-- Rodapé do modal -->
      <div class="modal-footer">
        <div class="footer-buttons">
          <button 
            class="btn btn-danger" 
            @click="handleConfirm"
            :disabled="isSubmitting"
          >
            {{ isSubmitting ? "Processando..." : "Confirmar" }}
          </button>
          <button class="btn btn-secondary" @click="$emit('close')">
            Cancelar
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TextEditor from './TextEditor.vue';

export default {
  name: "CancelEnrollmentModal",
  
  components: {
    TextEditor,
  },
  
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    user: {
      type: Object,
      default: null,
    },
    offerclassid: {
      type: [Number, String],
      required: true,
    }
  },
  
  emits: ["close", "confirm", "error"],
  
  data() {
    return {
      isSubmitting: false,
      cancellationReason: '',
    };
  },
  
  watch: {
    show(newValue) {
      if (newValue) {
        this.cancellationReason = '';
        this.isSubmitting = false;
      } else {
        this.resetForm();
      }
    }
  },
  
  methods: {
    handleConfirm() {
      if (this.isSubmitting) return;
            
      this.isSubmitting = true;
      
      // Emit confirm event with user data and reason
      this.$emit('confirm', {
        user: this.user,
        reason: this.cancellationReason.trim(),
        offerclassid: this.offerclassid
      });
    },
    
    resetForm() {
      this.cancellationReason = '';
      this.isSubmitting = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.modal-container {
  background-color: #212529;
  border-radius: 6px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  max-height: 90vh;
  width: 100%;
  max-width: 600px;
  border: 1px solid #373b3e;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #373b3e;
  background-color: #212529;

  .modal-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #f8f9fa;
  }

  .modal-close {
    background: none;
    border: none;
    color: #6c757d;
    font-size: 1rem;
    cursor: pointer;
    padding: 0.25rem;
    line-height: 1;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;

    &:hover {
      color: #adb5bd;
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
}

.modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;

  .confirmation-text {
    margin-bottom: 1.5rem;
    
    p {
      color: #f8f9fa;
      font-size: 1.125rem;
      font-weight: 500;
      margin: 0;
    }
  }

  .user-info {
    margin-bottom: 2rem;
    padding: 1rem;
    background-color: rgba(108, 117, 125, 0.1);
    border-radius: 4px;
    border-left: 3px solid #6c757d;
    
    p {
      color: #adb5bd;
      font-size: 0.9rem;
      line-height: 1.5;
      margin: 0;
      
      strong {
        color: #f8f9fa;
      }
    }
  }

  .reason-section {
    .reason-label {
      display: block;
      color: #f8f9fa;
      font-size: 1rem;
      font-weight: 500;
      margin-bottom: 0.75rem;
    }

    .text-editor-wrapper {
      :deep(.text-editor-container) {
        margin-bottom: 0;
        max-width: none;
      }

      :deep(.editor-content) {
        min-height: 120px;
        font-size: 0.9rem;
        line-height: 1.5;
      }

      :deep(.editor-toolbar) {
        background-color: #343a40;
        border-bottom: 1px solid #495057;
      }

      :deep(.btn-editor) {
        background-color: #495057;
        border-color: rgba(255, 255, 255, 0.1);
        
        &:hover:not(:disabled) {
          background-color: #6c757d;
        }
      }
    }
  }
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 1rem 1.5rem;
  border-top: 1px solid #373b3e;
  background-color: #212529;

  .footer-buttons {
    display: flex;
    gap: 0.75rem;
  }

  .btn {
    padding: 0.5rem 1.5rem;
    border-radius: 4px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    min-width: 100px;

    &:disabled {
      opacity: 0.65;
      cursor: not-allowed;
    }
  }

  .btn-danger {
    background-color: #dc3545;
    color: #fff;

    &:hover:not(:disabled) {
      background-color: #c82333;
    }
  }

  .btn-secondary {
    background-color: #6c757d;
    color: #fff;

    &:hover:not(:disabled) {
      background-color: #5a6268;
    }
  }
}
</style>
