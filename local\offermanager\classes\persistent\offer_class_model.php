<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON>le is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\persistent;

use core\persistent;
use context_offer_class;
use moodle_exception;
use html_writer;

use local_offermanager\constants;
use local_offermanager\event\offer_class_created;
use local_offermanager\event\offer_class_deleted;
use local_offermanager\event\offer_class_activated;
use local_offermanager\event\offer_class_inactivated;
use local_offermanager\persistent\trait\class_accessibility_trait;
use local_offermanager\persistent\trait\class_duplication_trait;
use local_offermanager\persistent\trait\class_availability_trait;
use local_offermanager\persistent\trait\class_course_trait;
use local_offermanager\persistent\trait\class_enrol_instance_trait;
use local_offermanager\persistent\trait\class_operational_cycle_trait;
use local_offermanager\persistent\trait\class_teacher_trait;
use local_offermanager\persistent\trait\class_user_enrol_trait;
use local_offermanager\persistent\trait\class_reenrolment_trait;

defined('MOODLE_INTERNAL') || die();
/**
 * Class offer_class_model
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class offer_class_model extends persistent
{
    use class_accessibility_trait;
    use class_availability_trait;
    use class_duplication_trait;
    use class_course_trait;
    use class_enrol_instance_trait;
    use class_operational_cycle_trait;
    use class_teacher_trait;
    use class_user_enrol_trait;
    use class_reenrolment_trait;

    protected $offer;

    const TABLE = 'local_offermanager_class';

    protected static function define_properties()
    {
        return [
            'offercourseid' => [
                'type' => PARAM_INT,
                'description' => 'ID da relação entre oferta e curso',
                'null' => NULL_NOT_ALLOWED,
            ],
            'enrol' => [
                'type' => PARAM_TEXT,
                'description' => 'Plugin de inscrição',
                'null' => NULL_NOT_ALLOWED,
            ],
            'enrolid' => [
                'type' => PARAM_INT,
                'description' => 'ID da instância de inscrição na tabela enrol',
                'null' => NULL_NOT_ALLOWED,
            ],
            'status' => [
                'type' => PARAM_INT,
                'description' => 'Status da oferta',
                'choice' => [constants::OFFER_STATUS_INACTIVE, constants::OFFER_STATUS_ACTIVE],
                'null' => NULL_NOT_ALLOWED,
                'default' => constants::OFFER_STATUS_ACTIVE
            ],
            'operational_cycle' => [
                'type' => PARAM_INT,
                'description' => 'Ciclo operacional da turma da oferta',
                'choice' => [
                    constants::OFFER_CLASS_OPERATIONAL_CYCLE_NOT_STARTED,
                    constants::OFFER_CLASS_OPERATIONAL_CYCLE_STARTED,
                    constants::OFFER_CLASS_OPERATIONAL_CYCLE_FINISHED
                ],
                'null' => NULL_NOT_ALLOWED,
                'default' => constants::OFFER_CLASS_OPERATIONAL_CYCLE_NOT_STARTED
            ],
            'isaccessible' => [
                'type' => PARAM_INT,
                'description' => 'Marcador se uma turma está acessível',
                'choices' => [constants::OFFER_CLASS_NOT_ACCESSIBLE, constants::OFFER_CLASS_ACCESSIBLE],
                'null' => NULL_NOT_ALLOWED,
                'default' => constants::OFFER_CLASS_NOT_ACCESSIBLE
            ],
            'timeaccessible' => [
                'type' => PARAM_INT,
                'description' => 'A hora que uma turma se tornou acessível',
                'null' => NULL_ALLOWED,
                'default' => null
            ],
            'usercreated' => [
                'type' => PARAM_INT,
                'description' => 'User id do criador',
                'null' => NULL_NOT_ALLOWED,
                'default' => function () {
                    global $USER;
                    return $USER->id;
                }
            ],
            'clone_id' => [
                'type' => PARAM_INT,
                'description' => 'ID da turma original que foi clonada',
                'null' => NULL_ALLOWED,
                'default' => null
            ]
        ];
    }

    /**
     * Valida o campo offercourseid.
     *
     * @param int $value ID da relação entre oferta e curso.
     * @return bool
     */
    protected function validate_offercourseid($value): bool
    {
        return !!offer_course_model::get_record(['id' => $value], MUST_EXIST);
    }

    /**
     * Valida o campo enrolid.
     *
     * @param int $value ID da instância de inscrição.
     * @return bool
     */
    protected function validate_enrolid($value): bool
    {
        global $DB;

        $enrol_instance_exists = $DB->record_exists('enrol', ['id' => $value]);

        if (!$enrol_instance_exists) {
            throw new moodle_exception('error:enrolid_doesnt_exist', 'local_offermanager');
        }

        $params = [
            'enrolid' => $this->get('enrolid')
        ];

        $query = 'enrolid = :enrolid';

        if ($this->get('id')) {

            $params['id'] = $this->get('id');
            $query .= ' AND id <> :id';
        }

        $other_instance = self::record_exists_select($query, $params);

        if ($other_instance) {
            throw new moodle_exception('error:enrolid_already_exists', 'local_offermanager');
        }

        return true;
    }

    protected function validate_operational_cycle($value): bool
    {
        $instance_cycle = $this->get('operational_cycle');

        if ($value != $instance_cycle && $instance_cycle == constants::OFFER_CLASS_OPERATIONAL_CYCLE_FINISHED) {
            throw new moodle_exception('error:offer_class_already_finished', 'local_offermanager');
        }

        return true;
    }

    public function get_name()
    {
        return $this->get_mapped_field('classname');
    }

    public function get_offer()
    {
        if (!$this->offer) {
            $this->offer = ($this->get_offer_course())->get_offer();
        }
        return $this->offer;
    }

    /**
     * Verifica se uma turma está ativa.
     *
     * @return bool
     */
    public function is_active()
    {
        $offercourse = $this->get_offer_course();
        return $this->get('status') == constants::OFFER_STATUS_ACTIVE && $offercourse->is_active();
    }

    /**
     * Ativa a instância.
     *
     * @return bool
     * @throws moodle_exception
     */
    public function activate(): bool
    {
        if ($this->get('status') === constants::OFFER_STATUS_ACTIVE) {
            throw new moodle_exception('error:offer_class_already_active', 'local_offermanager');
        }

        $this->set('status', constants::OFFER_STATUS_ACTIVE);

        $this->save();

        return $this->get('status') == constants::OFFER_STATUS_ACTIVE;
    }

    /**
     * Desativa a instância.
     *
     * @return bool
     * @throws moodle_exception
     */
    public function inactivate(): bool
    {
        if ($this->get('status') === constants::OFFER_STATUS_INACTIVE) {
            throw new moodle_exception('error:offer_class_already_inactive', 'local_offermanager');
        }

        $this->set('status', constants::OFFER_STATUS_INACTIVE);

        $this->save();

        return $this->get('status') == constants::OFFER_STATUS_INACTIVE;
    }

    public function can_delete()
    {
        return !$this->has_user_enrolments();
    }

    public function can_activate()
    {
        return !$this->has_user_enrolments();
    }

    protected function before_create()
    {
        $min_users = (int) $this->get_mapped_field('minusers');
    }

    protected function after_create()
    {
        context_offer_class::instance($this->get('id'));

        $event = offer_class_created::instance($this);
        $event->trigger();
    }

    protected function before_update()
    {
        global $DB;

        $table_status = $DB->get_field(
            self::TABLE,
            'status',
            [
                'id' => $this->get('id')
            ]
        );

        $instance_status = $this->get('status');

        if ($table_status != $instance_status) {

            $event =  $instance_status == constants::OFFER_STATUS_ACTIVE
                ? offer_class_activated::instance($this)
                : offer_class_inactivated::instance($this);

            $event->trigger();
        }
    }

    protected function before_delete()
    {
        if (!$this->can_delete()) {
            throw new \moodle_exception('error:cannot_delete_offer_class', 'local_offermanager');
        }

        $this->remove_related_adhoc_tasks();

        $event = offer_class_deleted::instance($this);
        $event->trigger();
    }

    public function get_description_html(): string
    {
        $description = $this->get_mapped_field('description');

        if(empty($description)){
            return '';
        }

        $description = strlen($description) > 120 ? substr($description, 0, 120). ' ...' : $description;
        
        return html_writer::div(
            html_writer::tag(
                'span',
                get_string('description') . ': ' . $description,
                [
                    'title' => $description
                ]
            ),
            'm-2'
        );
    }
}
