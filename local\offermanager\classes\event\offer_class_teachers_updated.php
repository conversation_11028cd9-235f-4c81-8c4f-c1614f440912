<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\event;

use local_offermanager\persistent\offer_class_model;
use context_system;

/**
 * Evento offer_class_teachers_updated
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class offer_class_teachers_updated extends \core\event\base {

    /**
     * Define propriedades básicas do evento.
     */
    protected function init() {
        $this->data['objecttable'] = 'local_offermanager_class';
        $this->data['crud'] = 'u';
        $this->data['edulevel'] = self::LEVEL_OTHER;
    }

    /**
     * Cria uma instância do evento.
     *
     * @param offer_class_model $offerclass
     * @param array $teacherids Lista de IDs dos professores atualizados
     * @return self
     */
    public static function instance(offer_class_model $offerclass, array $teacherids) {
        $data = [
            'objectid' => $offerclass->get('id'),
            'context' => context_system::instance(),
            'other' => [
                'offercourseid' => $offerclass->get('offercourseid'),
                'enrolid' => $offerclass->get('enrolid'),
                'teacherids' => $teacherids,
            ],
        ];

        $event = self::create($data);
        return $event;
    }

    /**
     * Nome do evento para exibição.
     *
     * @return \lang_string|string
     */
    public static function get_name() {
        return get_string('event:offerclassteachersupdated', 'local_offermanager');
    }

    /**
     * Descrição detalhada do evento.
     *
     * @return string
     */
    public function get_description() {
        $teacherids = implode(', ', $this->other['teacherids']);
        return "O usuário com id '{$this->userid}' atualizou os professores da turma com id '{$this->objectid}'. IDs dos professores: {$teacherids}.";
    }
}