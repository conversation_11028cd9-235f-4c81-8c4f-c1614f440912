<template>
  <modal
    :show="show"
    :title="title"
    :show-default-footer="false"
    :size="size"
    @close="$emit('close')"
  >
    <div class="content-container">
      <slot></slot>
    </div>
    
    <template #footer>
      <div class="custom-footer">
        <slot name="footer">
          <custom-button 
            variant="primary" 
            :label="closeButtonText" 
            @click="$emit('close')" 
          />
        </slot>
      </div>
    </template>
  </modal>
</template>

<script>
import Modal from './Modal.vue';
import CustomButton from './CustomButton.vue';

export default {
  name: 'ContentModal',
  components: {
    Modal,
    CustomButton
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: 'Detalhes'
    },
    closeButtonText: {
      type: String,
      default: 'Fechar'
    },
    size: {
      type: String,
      default: 'lg'
    }
  },
  emits: ['close']
}
</script>

<style lang="scss" scoped>
.content-container {
  min-height: 200px;
}

.custom-footer {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}
</style> 