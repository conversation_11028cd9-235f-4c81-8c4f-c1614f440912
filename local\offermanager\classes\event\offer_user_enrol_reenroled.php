<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\event;

use local_offermanager\persistent\offer_user_enrol_model;
use local_offermanager\persistent\offer_class_model;
use context_offer_class;
/**
 * Event offer_user_enrol_reenroled
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class offer_user_enrol_reenroled extends \core\event\base
{
    /**
     * Set basic properties for the event.
     */
    protected function init()
    {
        $this->data['objecttable'] = 'local_offermanager_ue';
        $this->data['crud'] = 'u';
        $this->data['edulevel'] = self::LEVEL_OTHER;
    }

    /**
     * Returns description of event.
     *
     * @return string
     */
    public function get_description()
    {
        $class = $this->get_class();
        $classname = $class->get_mapped_field('classname');

        return "The enrollment of user '{$this->get_user()->id}' in class '{$classname}' was extended.";
    }

    /**
     * Returns the name of the event.
     *
     * @return string
     */
    public static function get_name()
    {
        return get_string('event:user_enrolment_reenroled', 'local_offermanager');
    }


    /**
     * Get the course related to the event.
     *
     * @return offer_class_model|null The course object.
     */
    public function get_class()
    {
        return offer_class_model::get_record(['id' => $this->objectid]);
    }

    /**
     * Get the user related to the event.
     *
     * @return \stdClass|null The user object.
     */
    public function get_user()
    {
        global $DB;
        return $DB->get_record('user', ['id' => $this->relateduserid]);
    }

    /**
     * Cria uma instância do evento.
     *
     * @param offer_user_enrol_model $offer_user_enrol The enrolment record.
     * @return self
     */
    public static function instance(offer_user_enrol_model $offer_user_enrol): self
    {
        global $USER;
        $offerclass = $offer_user_enrol->get_offer_class();
        $ue = $offer_user_enrol->get_user_enrolment();

        return self::create([
            'context' => context_offer_class::instance($offer_user_enrol->get('offerclassid')),
            'objectid' => $offer_user_enrol->get('id'),
            'relateduserid' => $offer_user_enrol->get('userid'),
            'userid' => $USER->id,
            'other' => [
                'offerclassid' => $offerclass->get('id'),
                'ueid' => $ue->id
            ]
        ]);
    }
}
