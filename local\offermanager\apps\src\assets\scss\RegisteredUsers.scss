.offer-manager {
  margin-bottom: 2rem;
}

.new-offer-container {
  margin-top: 15px;
  margin-bottom: 20px; /* Espaço entre o botão e os filtros */
  display: flex;
  justify-content: flex-start;
}

:deep(.progress-container) {
  width: 100% !important;
  max-width: 100px !important; /* Limitar a largura para melhor visualização */
  height: 20px !important;
  background-color: #f0f0f0 !important;
  border-radius: 10px !important;
  position: relative !important;
  overflow: hidden !important;
  margin: 0 auto !important; /* Centralizar horizontalmente */
}

:deep(.progress-bar) {
  height: 100% !important;
  background-color: var(--primary);
  border-radius: 10px !important;
}

:deep(.progress-text) {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  color: #333 !important;
  font-weight: bold !important;
  font-size: 0.8rem !important;
}


:deep(.status-container) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important; /* Centralizar horizontalmente */
  gap: 10px !important;
  margin: 0 auto !important; /* Centralizar o contêiner na célula */
  width: 100% !important;
}

:deep(.status-actions) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 10px !important;
}

:deep(.status-tag) {
  display: inline-block !important;
  padding: 4px 8px !important;
  border-radius: 4px !important;
  font-size: 0.8rem !important;
  font-weight: bold !important;
  text-align: center !important;
  margin: 0 auto !important; /* Centralizar horizontalmente */
}

:deep(.btn-information),
:deep(.btn-settings) {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 !important;
  margin: 0 !important;
  background: none !important;
  border: none !important;
  outline: none !important;
  cursor: pointer !important;

  &:hover,
  &:focus,
  &:active {
    background: none !important;
    outline: none !important;
    box-shadow: none !important;
  }

  .custom-icon {
    stroke: var(--primary) !important;
    width: 20px !important;
    height: 20px !important;
  }
}

/* Os estilos de .btn-action foram movidos para global.scss */

.btn-activate {
  color: #6c757d;

  &:hover {
    background-color: rgba(108, 117, 125, 0.1);
  }

  i {
    opacity: 0.7;
  }
}

.btn-deactivate {
  color: #fff;
}

.btn-view {
  color: var(--primary);

  &:hover {
    background-color: rgba(110, 168, 254, 0.1);
  }
}

.btn-certificate {
  color: #ffc107;

  &:hover {
    background-color: rgba(255, 193, 7, 0.1);
  }
}

.table-container {
  position: relative;
  margin-bottom: 1rem;
  min-height: 400px;
  background-color: #212529;

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(3px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;

    &:not(:empty) {
      opacity: 1;
      visibility: visible;
    }

    .loading-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 2rem;
      padding: 3rem;
      border-radius: 12px;
      background-color: rgba(33, 37, 41, 0.98);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      min-width: 240px;
    }

    .loading-text {
      color: #fff;
      font-size: 1.125rem;
      font-weight: 500;
      letter-spacing: 0.01em;
      text-align: center;
      margin: 0;
      padding: 0;
    }

    .spinner-border {
      width: 3.5rem;
      height: 3.5rem;
      border: 0.3rem solid rgba(255, 255, 255, 0.2);
      border-top-color: #fff;
      border-radius: 50%;
      animation: spin 0.8s ease-in-out infinite;
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.alert {
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &.alert-danger {
    background-color: #2c0b0e;
    border: 1px solid #842029;
    color: #ea868f;
  }

  i {
    font-size: 1.25rem;
  }
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1050;
}

.modal-content {
  background-color: #212529;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  color: #fff;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #343a40;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-button {
  background: transparent;
  border: none;
  font-size: 1.5rem;
  color: #adb5bd;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1rem 1.5rem;
  border-top: 1px solid #343a40;
}

.enrollment-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.detail-row {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.detail-label {
  font-weight: 600;
  min-width: 150px;
  color: #adb5bd;
}

.detail-value {
  flex: 1;
}

.btn-primary {
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-secondary {
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

/* Estilos para os filtros */
.filter-label {
  font-size: 12px;
  font-weight: 500;
  color: #fff;
  margin-bottom: 5px;
  display: block;
  text-transform: none;
}

.filter-group {
  display: flex;
  flex-direction: column;
  margin-right: 0;
  min-width: 250px;
  max-width: 300px;
  flex: 1;
}

:deep(.filter-section) {
  padding: 0 !important;
  background-color: transparent;
}

/* Estilo personalizado para a seção de filtros */
:deep(.filter-section-custom) {
  padding: 0;
  background-color: transparent;
}

:deep(.filter-section-custom .filter-row) {
  margin-bottom: 0;
  display: flex;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  align-items: flex-end !important;
  gap: 15px !important;
}

/* Estilo para os campos de busca */
:deep(.form-control) {
  background-color: #2c3237 !important;
  border: 1px solid #51585e !important;
  color: #fff !important;
}

:deep(.search-icon) {
  color: #51585e !important;
}

:deep(.form-control::placeholder) {
  color: #6c757d !important;
  opacity: 1 !important;
  font-weight: 600 !important; /* Texto mais grosso */
  font-size: 15px !important; /* Texto maior */
}

:deep(.dropdown-menu) {
  background-color: #2c3237 !important;
  border: 1px solid #51585e !important;
}

:deep(.dropdown-item) {
  color: #fff !important;
}

/* Estilo para o título dos filtros */
.filters-title-container {
  margin-bottom: 5px !important;
  padding: 0 5px !important;
}

.filters-title {
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #fff !important;
  margin: 0 !important;
}

/* Estilo para o container de filtros personalizado */
.custom-filters-container {
  background-color: #212529 !important;
  border: 1px solid #51585e !important;
  border-radius: 4px !important;
  padding: 12px !important;
  margin-bottom: 15px !important;
  width: 100% !important;
  display: block !important;
}

/* Estilos para o select e botão */
:deep(.custom-select-container) {
  width: 100% !important;
}

:deep(.custom-select-container .form-control) {
  background-color: #2c3237 !important;
  border-color: #51585e !important;
  color: #fff !important;
  height: 36px !important;
}

/* Estilos para garantir que o botão fique alinhado com o select */
:deep(.custom-button) {
  height: 36px !important;
  display: flex !important;
  align-items: center !important;
  margin-left: 0 !important;
  margin-top: 0 !important;
}

.filters-wrapper {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  justify-content: space-between !important;
  align-items: flex-start !important;
  gap: 15px !important;
  width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
}

.filter-item {
  flex: 1 !important;
  min-width: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

.filter-input-wrapper {
  position: relative !important;
  width: 100% !important;
}

/* Estilos para a seção de filtros */
.filters-container {
  display: flex;
  flex-direction: row;
  gap: 15px;
  margin-bottom: 20px;
  padding: 0;
  align-items: flex-end;

  /* Estilo específico para os placeholders nos filtros */
  :deep(input::placeholder) {
    color: #6c757d !important;
    opacity: 1 !important;
    font-weight: 600 !important; /* Texto mais grosso */
    font-size: 15px !important; /* Texto maior */
  }
}

.filter-item {
  flex: 1;
  min-width: 0;
  max-width: 300px;
}

.filter-label {
  font-size: 14px;
  color: #fff;
  margin-bottom: 8px;
}

.filter-tags-container {
  margin-top: 10px;
  margin-bottom: 20px;
}

/* Estilo para os botões de filtro */
.filter-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
  height: 42px; /* Mesma altura dos campos de filtro */
}

:deep(.filter-buttons .custom-button) {
  margin-bottom: 0;
  height: 42px !important; /* Mesma altura dos campos de filtro */
  min-width: 100px !important; /* Largura mínima para os botões */
  padding: 0 15px !important; /* Padding horizontal para os botões */
  font-size: 14px !important; /* Tamanho da fonte igual aos labels */
}

/* Estilo para o FilterRow */
:deep(.filter-row) {
  display: flex;
  align-items: flex-end;
  gap: 15px; /* Espaçamento entre os grupos de filtro */
}

/* Estilo para o HierarchicalSelect */
:deep(.hierarchical-select) {
  background-color: #212529 !important;
  color: #fff !important;
  border: 1px solid #495057 !important;
  height: 38px !important;
  font-size: 14px !important;

  option {
    background-color: #212529 !important;
    color: #fff !important;
    padding: 8px 12px !important;
  }

  option:hover,
  option:focus {
    background-color: var(--primary) !important;
    color: #fff !important;
  }

  optgroup {
    font-weight: bold !important;
    background-color: #343a40 !important;
    color: #adb5bd !important;
    padding: 8px 8px !important;
  }

  option.child-option {
    padding-left: 20px !important;
    background-color: #212529 !important;
    color: #fff !important;
  }
}

/* Estilo para as tags de filtro */
:deep(.filter-tags) {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 15px;
}

/* Estilo personalizado para o Autocomplete nos filtros */
:deep(.custom-autocomplete) {
  width: 100% !important;

  .input-wrapper {
    width: 100% !important;
    position: relative !important;
  }

  .form-control {
    width: 100% !important;
    background-color: #212529 !important;
    border: 1px solid #495057 !important;
    border-radius: 4px !important;
    color: #fff !important;
    font-size: 15px !important; /* Tamanho da fonte aumentado para combinar com o placeholder */
    padding: 10px 12px !important; /* Padding ajustado para melhor visualização */
    font-weight: 400 !important;
    max-width: 336px !important; /* Largura máxima padrão do FilterGroup */
  }

  .form-control::placeholder {
    color: #6c757d !important;
    opacity: 1 !important;
    font-weight: 600 !important; /* Texto mais grosso */
    font-size: 15px !important; /* Texto maior */
  }
}

/* Estilos para os checkboxes */
:deep(.checkbox-container) {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  width: 100% !important; /* Ocupar toda a largura da célula */
  height: 100% !important; /* Ocupar toda a altura da célula */
  text-align: center !important;
}

:deep(.custom-checkbox) {
  width: 16px !important;
  height: 16px !important;
  cursor: pointer !important;
  accent-color: var(--primary);
  background-color: #212529;
  border: 1px solid var(--primary);
  border-radius: 3px !important;
  position: relative !important;
  appearance: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  margin: 0 !important; /* Centralizar horizontalmente */

  &:checked {
    background-color: var(--primary) !important;

    &::after {
      content: "" !important;
      position: absolute !important;
      top: 2px !important;
      left: 5px !important;
      width: 5px !important;
      height: 9px !important;
      border: solid white !important;
      border-width: 0 2px 2px 0 !important;
      transform: rotate(45deg) !important;
    }
  }

  &:indeterminate {
    background-color: var(--primary);

    &::after {
      content: "" !important;
      position: absolute !important;
      top: 7px !important;
      left: 3px !important;
      width: 8px !important;
      height: 2px !important;
      background-color: white !important;
    }
  }
}

/* Estilos para a seção de usuários selecionados */
.selected-users-actions {
  display: flex;
  justify-content: flex-start; /* Alterado para flex-start para alinhar à esquerda */
  align-items: center;
  flex-wrap: wrap;
  padding: 10px 0;
  margin-top: 15px;
  margin-bottom: 15px;
}

.selected-users-count {
  font-weight: 500;
  color: #fff;
  margin-right: 1rem;
  margin-bottom: 0.5rem;
}

.bulk-actions-container {
  flex-grow: 1;
  margin-right: 1rem;
  margin-bottom: 0.5rem;
  max-width: 300px;
  @media(max-width: 576px) {
    max-width: 100%;
  }

  label {
    display: block;
    margin-bottom: 0.5rem;
    color: #adb5bd;
    font-size: 14px;
    font-weight: 500;
    font-style: italic;
  }

  select {
    width: 100%;
    padding: 0.5rem;
    border-radius: 4px;
    background-color: #343a40;
    color: #fff;
    border: 1px solid #495057;

    &:focus {
      outline: none;
    }

    option,
    optgroup {
      background-color: #343a40;
      color: #fff;
    }

    optgroup {
      font-weight: bold;
    }
  }

  .bulk-select {
    background-color: #212529 !important;
    border: 1px solid #495057 !important;

    &:focus {
      border-color: #495057 !important;
      box-shadow: none !important;
    }
  }
}

.selected-users-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 0.5rem;
}

@media (max-width: 768px) {
  .selected-users-actions {
    flex-direction: column;
    align-items: flex-start;

    .selected-users-count,
    .bulk-actions-container,
    .selected-users-buttons {
      width: 100%;
      margin-right: 0;
      margin-bottom: 1rem;
    }
  }
}

/* Estilo para o botão de matricular abaixo da paginação */
.bottom-enroll-button {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  margin-bottom: 20px;
  @media(max-width: 576px) {
    justify-content: flex-start;
    .custom-button {
      width: 100%;
    }
  }
}

/* Estilo para o contêiner do nome do usuário com avatar */
:deep(.user-name-container) {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important; /* Centralizar horizontalmente */
  gap: 12px !important; /* Espaçamento entre a imagem e o nome */
  cursor: pointer !important;
  max-width: 100% !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  padding: 4px 0 !important; /* Padding vertical para aumentar a área clicável */
  margin: 0 auto !important; /* Centralizar o contêiner na célula */

  &:hover {
    .user-name-link {
      color: #9ec5ff !important; /* Cor mais clara ao passar o mouse */
      text-decoration: underline !important;
    }
  }
}

:deep(.user-name-link) {
  color: var(
    --primary
  ) !important; /* Azul mais forte e importante para garantir que seja aplicado */
  font-weight: 700 !important; /* Negrito mais forte */
  transition: color 0.2s ease !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  text-decoration: none !important;
  max-width: calc(100% - 48px) !important; /* Considerando o avatar e o gap */
  display: inline-block !important;
  line-height: 36px !important; /* Mesma altura do avatar para alinhamento vertical perfeito */
  vertical-align: middle !important;
  text-align: center !important; /* Centralizar o texto */
}

/* Aplicar estilo diretamente à célula da tabela para evitar quebra de linha */
:deep(td[data-label="NOME/SOBRENOME"]) {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  max-width: 220px !important;
  text-align: center !important; /* Centralizar o conteúdo */
  display: flex !important;
  justify-content: center !important; /* Centralizar horizontalmente */
  align-items: center !important; /* Centralizar verticalmente */
  padding: 10px 8px !important;
}

/* Centralizar todas as células e cabeçalhos da tabela */
:deep(.table th),
:deep(.table td) {
  vertical-align: middle !important;
}

/* Garantir que a tabela tenha overflow visível */
:deep(.table) {
  overflow: visible !important;
}

:deep(.table-responsive) {
  overflow: visible !important;
}

/* Estilo específico para a coluna de papéis */
:deep(td[data-column="roles"]) {
  cursor: pointer !important;
  position: relative !important;
  overflow: visible !important; /* Importante para que o select não seja cortado */
  padding: 0 !important; /* Remover padding para que o conteúdo fique alinhado */
}

/* Garantir que o conteúdo das células também esteja centralizado */
:deep(.table td > *) {
  margin: 0 auto !important;
  text-align: center !important;
  justify-content: flex-start !important;
}

/* Garantir que os checkboxes estejam centralizados */
:deep(.checkbox-container) {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  width: 100% !important;
}

/* Garantir que as imagens de usuário estejam centralizadas */
:deep(.user-avatar) {
  display: flex;
  justify-content: center;
  align-items: center;
}

.page-header-controls {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 10px;
}

.page-view-selector {
  width: 240px;
}

@media (max-width: 576px) {
  .page-header-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .page-view-selector {
    width: 100%;
  }
}