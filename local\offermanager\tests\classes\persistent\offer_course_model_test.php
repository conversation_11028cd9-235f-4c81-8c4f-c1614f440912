<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\classes\persistent;

use advanced_testcase;
use local_offermanager\persistent\offer_course_model;
use local_offermanager\persistent\offer_model;
use local_offermanager\persistent\offer_class_model;
use local_offermanager\event\offer_course_activated;
use local_offermanager\event\offer_course_inactivated;
use local_offermanager\event\offer_course_updated;
use moodle_exception;
use local_offermanager\constants;

/**
 * Tests for Offer Manager
 *
 * @package    local_offermanager
 * @category   test
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
final class offer_course_model_test extends advanced_testcase
{
    /**
     * Test get related offer.
     */
    public function test_get_offer()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(
            0,
            (object)
            [
                'name' => 'Test Offer'
            ]
        );

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $this->assertNotNull($offer_course->get('id'));
        $this->assertEquals($offer->get('id'), $offer_course->get('offerid'));
        $this->assertEquals($course->id, $offer_course->get('courseid'));
        $this->assertEquals(constants::OFFER_STATUS_ACTIVE, $offer_course->get('status'));

        $retrieved_offer = $offer_course->get_offer();

        $this->assertInstanceOf(offer_model::class, $retrieved_offer);
        $this->assertEquals($offer->get('id'), $retrieved_offer->get('id'));
    }

    /**
     * Test deleting an offer course record.
     */
    public function test_delete_offer_course()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) ['name' => 'Test Offer']);
        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $offer_course->delete();

        $this->assertFalse(offer_course_model::record_exists($offer_course->get('id')));
    }

    public function test_course_validation()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) ['name' => 'Test Offer']);
        $offer->save();

        $this->expectExceptionMessage(get_string('error:course_not_found', 'local_offermanager'));
        $offer_course = $offer->add_course(9999);

        $offer_course->save();
    }

    public function test_offer_validation()
    {
        $this->resetAfterTest(true);

        $course = $this->getDataGenerator()->create_course();

        $this->expectExceptionMessage(get_string('error:offer_not_found', 'local_offermanager'));
        $offer_course = new offer_course_model(
            0,
            (object)
            [
                'courseid' => $course->id,
                'offerid' => 9999
            ]
        );

        $offer_course->save();
    }

    /**
     * Test validation of unique combination of offerid and courseid.
     */
    public function test_validate_unique_combination()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) ['name' => 'Test Offer']);
        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer->add_course($course->id);

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:duplicate_offer_course', 'local_offermanager'));
        $offer->add_course($course->id);
    }

    /**
     * Test adding a class to an offer course.
     */
    public function test_add_class()
    {
        global $DB;
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) ['name' => 'Test Offer']);
        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offer_class = $offer_course->add_class($enrol_instance->id);

        $this->assertInstanceOf(offer_class_model::class, $offer_class);
        $this->assertNotNull($offer_class->get('id'));
        $this->assertEquals($offer_course->get('id'), $offer_class->get('offercourseid'));
        $this->assertEquals($enrol_instance->id, $offer_class->get('enrolid'));
    }

    /**
     * Test removing a class from an offer course.
     */
    public function test_remove_class()
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) ['name' => 'Test Offer']);
        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offer_class = $offer_course->add_class($enrol_instance->id);

        $result = $offer_course->remove_class($enrol_instance->id);

        $this->assertTrue($result);
        $this->assertFalse(offer_class_model::record_exists($offer_class->get('id')));
    }

    /**
     * Test checking if an offer course has enrol instances.
     */
    public function test_has_enrol_instances()
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) ['name' => 'Test Offer']);
        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $this->assertFalse($offer_course->has_enrol_instances());

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offer_course->add_class($enrol_instance->id);

        $this->assertTrue($offer_course->has_enrol_instances());
    }

    /**
     * Test checking if an offer course has user enrolments.
     */
    public function test_has_user_enrolments()
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) ['name' => 'Test Offer']);
        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) ['name' => 'Test Offer']);
        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $this->assertFalse($offer_course->has_user_enrolments());

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offer_class = $offer_course->add_class($enrol_instance->id);

        $user = $this->getDataGenerator()->create_user();

        $enrol_plugin = $offer_class->get_plugin();

        $enrol_plugin->enrol_user($enrol_instance, $user->id);

        $this->assertTrue($offer_course->has_user_enrolments());
    }

    /**
     * Test the can_delete method.
     */
    public function test_can_delete()
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) ['name' => 'Test Offer']);
        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $this->assertTrue($offer_course->can_delete());

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offer_class = $offer_course->add_class($enrol_instance->id);

        $this->assertTrue($offer_course->can_delete());

        $user = $this->getDataGenerator()->create_user();

        $enrol_plugin = $offer_class->get_plugin();

        $enrol_plugin->enrol_user($enrol_instance, $user->id);

        $this->assertFalse($offer_course->can_delete());
    }

    public function test_get_user_enrolments()
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) ['name' => 'Test Offer']);
        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offer_class = $offer_course->add_class($enrol_instance->id);

        $enrol_plugin = $offer_class->get_plugin();

        for ($i = 0; $i < 10; $i++) {
            $user = $this->getDataGenerator()->create_user();
            $enrol_plugin->enrol_user($enrol_instance, $user->id);
        }

        $user_enrolments = $offer_course->get_user_enrolments();

        $this->assertCount(10, $user_enrolments);
    }

    /**
     * Test get_user_enrolments with multiple  enrolments.
     */
    public function test_get_user_enrolments_multiple_enrolments()
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) ['name' => 'Test Offer']);
        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $enrolids = [$enrol_instance->id];
 
        $enrol_instance2_id = $DB->insert_record('enrol', $enrol_instance, true);
        $enrol_instance2 =  $DB->get_record('enrol', array('id' => $enrol_instance2_id));

        $enrolids[] = $enrol_instance2_id;

        $enrol_instance3_id = $DB->insert_record('enrol', $enrol_instance, true);
        $enrol_instance3 =  $DB->get_record('enrol', array('id' => $enrol_instance3_id));

        $enrols = [$enrol_instance, $enrol_instance2, $enrol_instance3];

        $enrolids[] = $enrol_instance3_id;

        foreach ($enrolids as $enrolid) {
            $offer_class = $offer_course->add_class($enrolid);
        }

        $users = [];

        $enrol_plugin = $offer_class->get_plugin();

        for ($i = 0; $i < 20; $i++) {
            $user = $this->getDataGenerator()->create_user();
            $users[] = $user;
            $enrol_plugin->enrol_user($enrols[array_rand($enrols)], $user->id);
        }

        $user_enrolments = $offer_course->get_user_enrolments();

        $this->assertCount(20, $user_enrolments);

        $userids = array_map(function ($enrolment) {
            return $enrolment->userid;
        }, $user_enrolments);

        foreach ($users as $user) {
            $this->assertContains($user->id, $userids);
        }
    }

    /**
     * Test get_user_enrolments when no user enrolments exist.
     */
    public function test_get_user_enrolments_no_enrolments()
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) ['name' => 'Test Offer']);
        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $user_enrolments = $offer_course->get_user_enrolments();

        $this->assertEmpty($user_enrolments);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offer_course->add_class($enrol_instance->id);

        $user_enrolments = $offer_course->get_user_enrolments();

        $this->assertEmpty($user_enrolments);
    }

    /**
     * Test the get_enrol_ids method.
     */
    public function test_get_enrol_ids()
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) ['name' => 'Test Offer']);
        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);
        $enrol_instance_id = $enrol_instance->id;
        $enrolids = [$enrol_instance_id];

        unset($enrol_instance->id);

        $enrol_instance2_id = $DB->insert_record('enrol', $enrol_instance, true);

        $enrolids[] = $enrol_instance2_id;

        $enrol_instance3_id = $DB->insert_record('enrol', $enrol_instance, true);

        $enrolids[] = $enrol_instance3_id;

        foreach ($enrolids as $enrolid) {
            $offer_course->add_class($enrolid);
        }

        $retrieved_enrolids = $offer_course->get_enrol_ids();

        $this->assertEqualsCanonicalizing($enrolids, $retrieved_enrolids);
    }

    /**
     * Testa a ativação bem sucedida.
     */
    public function test_activate_success(): void
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $offer_course->inactivate();
        $this->assertTrue($offer_course->activate());
        $this->assertEquals(constants::OFFER_STATUS_ACTIVE, $offer_course->get('status'));
    }

    /**
     * Testa a falha na ativação de uma relação já ativa.
     */
    public function test_activate_failure_already_active(): void
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $this->expectException(\moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:offer_course_already_active', 'local_offermanager'));
        $offer_course->activate();
    }

    /**
     * Testa a inativação bem-sucedida de uma instância.
     */
    public function test_inactivate_success(): void
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $this->assertTrue($offer_course->inactivate());
        $this->assertEquals(constants::OFFER_STATUS_INACTIVE, $offer_course->get('status'));
    }

    /**
     * Testa a falha na inativação de uma instância já inativa.
     */
    public function test_inactivate_failure_because_already_inactive(): void
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $offer_course->inactivate();
    
        $this->expectException(\moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:offer_course_already_inactive', 'local_offermanager'));
        $offer_course->inactivate();
    }

    public function test_status_change_from_active_to_inactive_triggers_offer_course_inactivated()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $sink = $this->redirectEvents();
        $offer_course->inactivate();
        $events = $sink->get_events();
        $sink->close();

        $this->assertCount(1, $events);
        $activate_event = $events[0];
        $this->assertInstanceOf(offer_course_inactivated::class, $activate_event);
    }

    public function test_status_change_from_inactive_to_active_triggers_offer_course_activated()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $offer_course->inactivate();

        $sink = $this->redirectEvents();
        $offer_course->activate();
        $events = $sink->get_events();
        $sink->close();

        $this->assertCount(1, $events);
        $activate_event = $events[0];
        $this->assertInstanceOf(offer_course_activated::class, $activate_event);
    }
}