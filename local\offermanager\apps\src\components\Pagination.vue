<template>
  <div class="pagination-container mt-3">
    <div class="pagination-info">
      <select v-model="perPageModel" class="per-page-select" @change="handlePerPageChange">
        <option v-for="option in perPageOptions" :key="option" :value="option">{{ option }}</option>
      </select>
      <span class="pagination-text">
        Mostrando de {{ from }} até {{ to }} de {{ total }} resultados
      </span>
    </div>

    <div class="pagination-controls">
      <button
        class="page-item"
        :disabled="currentPage <= 1"
        @click="handlePageChange(currentPage - 1)"
      >
        <i class="fas fa-chevron-left"></i>
      </button>

      <button
        v-for="page in visiblePages"
        :key="page"
        class="page-item"
        :class="{ active: page === currentPage }"
        @click="handlePageChange(page)"
      >
        {{ page }}
      </button>

      <button
        class="page-item"
        :disabled="currentPage >= totalPages"
        @click="handlePageChange(currentPage + 1)"
      >
        <i class="fas fa-chevron-right"></i>
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Pagination',

  props: {
    currentPage: {
      type: Number,
      required: true
    },
    perPage: {
      type: Number,
      required: true
    },
    total: {
      type: Number,
      required: true
    },
    perPageOptions: {
      type: Array,
      default: () => [5, 10, 20, 50]
    }
  },

  emits: ['update:currentPage', 'update:perPage'],

  computed: {
    totalPages() {
      return Math.ceil(this.total / this.perPage)
    },

    visiblePages() {
      const maxVisiblePages = 5
      const halfVisible = Math.floor(maxVisiblePages / 2)
      let start = Math.max(1, this.currentPage - halfVisible)
      let end = Math.min(this.totalPages, start + maxVisiblePages - 1)

      if (end - start + 1 < maxVisiblePages) {
        start = Math.max(1, end - maxVisiblePages + 1)
      }

      const pages = []
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }

      return pages
    },

    from() {
      return this.total === 0 ? 0 : (this.currentPage - 1) * this.perPage + 1
    },

    to() {
      return Math.min(this.from + this.perPage - 1, this.total)
    },

    perPageModel: {
      get() {
        return this.perPage
      },
      set(value) {
        this.$emit('update:perPage', value)
      }
    }
  },

  methods: {
    handlePageChange(page) {
      if (page >= 1 && page <= this.totalPages) {
        this.$emit('update:currentPage', page)
      }
    },

    handlePerPageChange() {
      this.$emit('update:currentPage', 1)
    }
  }
}
</script>

<style lang="scss" scoped>
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;

  /* Apenas abaixo de 767px (MD breakpoint) */
  @media (max-width: 767px) {
    flex-direction: column;
    gap: 1rem;
    align-items: center;
  }
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 10px;

  /* Apenas abaixo de 767px (MD breakpoint) */
  @media (max-width: 767px) {
    width: 100%;
    justify-content: center;
  }
}

.per-page-select {
  padding: 5px 10px;
  background-color: #212529;
  border: 1px solid #373b3e;
  border-radius: 4px;
  color: #fff;
}

.pagination-text {
  font-size: 14px;
  color: #6c757d;
}

.pagination-controls {
  display: flex;
  gap: 0.5rem;

  /* Apenas abaixo de 767px (MD breakpoint) */
  @media (max-width: 767px) {
    width: 100%;
    justify-content: center;
  }
}

.page-item {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  padding: 0 0.5rem;
  border-radius: 4px;
  background-color: transparent;
  border: none;
  color: #fff;
  font-size: 14px;
  cursor: pointer;

  &.active {
    background-color: var(--primary);
    color: #fff;
  }

  &:hover:not(.active):not(:disabled) {
    background-color: rgba(255, 255, 255, 0.1);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}
</style>