<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\event;

defined('MOODLE_INTERNAL') || die();

use core\event\base;
use local_offermanager\persistent\offer_class_model;

/**
 * Event triggered when an offer class is duplicated.
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class offer_class_duplicated extends base
{

    /**
     * Init method.
     */
    protected function init()
    {
        $this->data['objecttable'] = offer_class_model::TABLE;
        $this->data['crud'] = 'c';
        $this->data['edulevel'] = self::LEVEL_OTHER;
    }

    /**
     * Returns relevant URL.
     *
     * @return \moodle_url
     */
    public function get_url()
    {
        // Link to the course view page of the new duplicated class's course.
        $newofferclass = offer_class_model::get_record(['id' => $this->objectid]);
        if ($newofferclass) {
            try {
                $course = $newofferclass->get_course();
                return new \moodle_url('/course/view.php', ['id' => $course->id]);
            } catch (\moodle_exception $e) {
                // Fallback if course cannot be retrieved.
            }
        }
        return null;
    }

    /**
     * Returns description of event.
     *
     * @return string
     */
    public function get_description()
    {
        $originalofferclassid = $this->other['original_offer_class_id'] ?? 0;
        return "O usuário com id '{$this->userid}' duplicou a turma de oferta com id '{$originalofferclassid}'. Criando uma nova turma de oferta com id '{$this->objectid}'.";
    }

    /**
     * Returns the context instance.
     *
     * @return \context The context.
     */
    public function get_context()
    {
        if (!isset($this->context)) {
            $this->context = \context_offer_class::instance($this->objectid);
        }
        return $this->context;
    }

    /**
     * Factory method.
     *
     * @param offer_class_model $originalofferclass The original offer class.
     * @param offer_class_model $newofferclass The new duplicated offer class.
     * @return self
     */
    public static function create_from_instances(offer_class_model $originalofferclass, offer_class_model $newofferclass): self
    {
        return self::create([
            'context' => \context_offer_class::instance($newofferclass->get('id')),
            'objectid' => $newofferclass->get('id'),
            'relateduserid' => $newofferclass->get('usercreated'),
            'other' => [
                'original_offer_class_id' => $originalofferclass->get('id'),
                'new_offer_class_id' => $newofferclass->get('id')
            ]
        ]);
    }
}
