<template>
  <div
    ref="selectContainer"
    class="custom-select-container"
    :style="customWidth"
  >
    <div v-if="label" class="select-label" :class="{ disabled: disabled }">
      {{ label }}
    </div>
    <div class="select-wrapper">
      <select
        :value="modelValue"
        @change="handleChange"
        @blur="handleBlur"
        class="custom-select"
        :class="{ error: hasError }"
        :disabled="disabled"
      >
        <option
          v-for="option in options"
          :key="option.value"
          :value="option.value"
        >
          {{ option.label }}
        </option>
      </select>
      <div class="select-arrow" :class="{ disabled: disabled }"></div>
    </div>
    <div v-if="hasError && errorMessage" class="error-message">
      {{ errorMessage }}
    </div>
  </div>
</template>

<script>
export default {
  name: "CustomSelect",

  props: {
    modelValue: {
      type: [Number, String],
      default: "",
    },
    options: {
      type: Array,
      required: true,
    },
    label: {
      type: String,
      default: "",
    },
    width: {
      type: [String, Number],
      default: null,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    hasError: {
      type: Boolean,
      default: false,
    },
    errorMessage: {
      type: String,
      default: "",
    },
    required: {
      type: Boolean,
      default: false,
    },
  },

  computed: {
    customWidth() {
      return this.width
        ? {
            width:
              typeof this.width === "number" ? `${this.width}px` : this.width,
          }
        : {};
    },
  },

  methods: {
    handleChange(event) {
      this.$emit("update:modelValue", event.target.value);

      // Se o campo estava com erro e agora tem valor, emitir evento de validação
      if (this.hasError && event.target.value) {
        this.$emit("validate");
      }
    },

    handleBlur(event) {
      // Quando o campo perde o foco, emitir evento de validação
      if (this.required) {
        this.$emit("validate");
      }
    },
  },

  emits: ["update:modelValue", "validate"],
};
</script>

<style lang="scss" scoped>
.custom-select-container {
  width: 100%;

  @media (max-width: 768px) {
    width: 100% !important;
  }
}

.select-label {
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #fff;

  &.disabled {
    opacity: 0.65;
  }
}

.select-wrapper {
  position: relative;
  width: 100%;
}

.custom-select {
  width: 100%;
  max-width: 500px;
  padding: 8px 10px;
  padding-right: 2rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border: 1px solid #495057;
  border-radius: 4px;
  background-color: #212529;
  color: #fff;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  cursor: pointer;
  height: 38px;
  background-image: none;

  &:focus {
    outline: 0;
    border-color: #2991fc;
    box-shadow: 0 0 0 0.15rem rgba(41, 145, 252, 0.25);
  }

  &:disabled {
    opacity: 0.65;
    cursor: not-allowed;
  }

  &.error {
    border-color: #dc3545 !important;
  }
}

.error-message {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.select-arrow {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 10px;
  height: 10px;
  pointer-events: none;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 8px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
  }

  &.disabled {
    opacity: 0.65;
  }
}

// Estilos para o v-select (migrados do OfferManager.scss)
:deep(.v-select) {
  padding-top: 0;

  .vs__selected {
    height: fit-content !important;
    color: #fff !important;
    background-color: #0f6cbf !important;
  }

  .vs__deselect {
    fill: #fff !important;
  }

  .vs__dropdown-toggle {
    height: inherit !important;
    border: 0 !important;
  }

  .vs__selected-options {
    overflow: hidden !important;
  }

  .vs__dropdown-menu {
    background: #212529 !important;
  }

  .vs__dropdown-option--highlight {
    color: #282c34 !important;
    background-color: #42b983 !important;
  }

  .vs__dropdown-option.vs__dropdown-option--selected {
    border-top: 1px solid #495057;
    background-color: #42b983 !important;
  }
}
</style>
