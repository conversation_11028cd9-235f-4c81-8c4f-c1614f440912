<template>
  <div class="user-avatar" :style="avatarStyle">
    <template v-if="hasImage">
      <img :src="imageUrl" alt="Foto de perfil" class="avatar-image" />
    </template>
    <template v-else>
      <div class="avatar-initials" :style="{ backgroundColor: backgroundColor }">
        {{ initials }}
      </div>
    </template>
  </div>
</template>

<script>
export default {
  name: 'UserAvatar',

  props: {
    // URL da imagem do usuário (opcional)
    imageUrl: {
      type: String,
      default: ''
    },
    // Nome completo do usuário (para gerar iniciais)
    fullName: {
      type: String,
      required: true
    },
    // Tamanho do avatar em pixels
    size: {
      type: Number,
      default: 32
    }
  },

  computed: {
    // Verifica se há uma imagem válida
    hasImage() {
      return !!this.imageUrl;
    },

    // Gera as iniciais do nome do usuário
    initials() {
      if (!this.fullName) return '';

      // Dividir o nome em partes
      const nameParts = this.fullName.split(' ').filter(part => part.length > 0);

      if (nameParts.length === 0) return '';

      if (nameParts.length === 1) {
        // Se houver apenas uma parte, usar as duas primeiras letras
        return nameParts[0].substring(0, 2).toUpperCase();
      }

      // Usar a primeira letra do primeiro nome e a primeira letra do último nome
      const firstInitial = nameParts[0].charAt(0);
      const lastInitial = nameParts[nameParts.length - 1].charAt(0);

      return (firstInitial + lastInitial).toUpperCase();
    },

    // Gera uma cor de fundo baseada no nome
    backgroundColor() {
      // Lista de cores para os avatares
      const colors = [
        '#1976D2', // Azul
        '#388E3C', // Verde
        '#D32F2F', // Vermelho
        '#7B1FA2', // Roxo
        '#FFA000', // Âmbar
        '#0097A7', // Ciano
        '#E64A19', // Laranja profundo
        '#5D4037', // Marrom
        '#455A64', // Azul cinza
        '#616161'  // Cinza
      ];

      // Usar o nome para gerar um índice consistente
      let hash = 0;
      for (let i = 0; i < this.fullName.length; i++) {
        hash = this.fullName.charCodeAt(i) + ((hash << 5) - hash);
      }

      // Usar o hash para selecionar uma cor
      const index = Math.abs(hash) % colors.length;
      return colors[index];
    },

    // Estilo do avatar
    avatarStyle() {
      return {
        width: `${this.size}px`,
        height: `${this.size}px`,
        minWidth: `${this.size}px`,
        minHeight: `${this.size}px`
      };
    }
  }
}
</script>

<style lang="scss" scoped>
.user-avatar {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0; /* Impede que o avatar encolha */
  vertical-align: middle; /* Alinha verticalmente com o texto */

  .avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .avatar-initials {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: calc(40% + 0.3vw);
  }
}
</style>
