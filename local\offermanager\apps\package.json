{"name": "offermanager", "version": "1.0.0", "private": true, "scripts": {"dev": "nodemon", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"axios": "^1.5.0", "lodash": "^4.17.21", "path": "^0.12.7", "pinia": "^2.1.6", "vue": "^3.3.4", "vue-router": "^4.2.4"}, "devDependencies": {"@vitejs/plugin-vue": "^4.6.2", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "prettier": "^3.0.3", "nodemon": "^3.1.3", "sass": "^1.72.0", "vite": "^4.4.9"}, "sass": {"functions": false, "quietDeps": true}}