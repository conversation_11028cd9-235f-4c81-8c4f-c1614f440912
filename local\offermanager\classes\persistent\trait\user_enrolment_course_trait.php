<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\persistent\trait;

use completion_info;
use grade_item;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir . '/gradelib.php');
require_once($CFG->libdir . '/completionlib.php');

/**
 * Trait user_enrolment_course_trait
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
trait user_enrolment_course_trait
{

    public function get_course()
    {
        $offerclass = $this->get_offer_class();

        return $offerclass->get_course();
    }

    public function update_grade_and_progress()
    {
        $progress = $this->get_course_progress();

        if ($progress !== null) {
            $this->set('progress', $progress);
        }

        $grade = $this->get_course_grade();
        if ($grade !== null) {
            $this->set('grade', $grade);
        }
        if ($progress !== null || $grade !== null) {
            $this->save();
        }
    }

    /**
     * Retorna o progresso do usuário no curso.
     *
     * @return float|null Progresso em porcentagem (0-100) ou null se não disponível.
     */
    public function get_course_progress(): ?float
    {
        if (!$this->has_situation_to_do_the_course()) {
            return $this->get('progress');
        }

        $course = $this->get_course();
        $userid = $this->get('userid');

        if (!$userid || !$course || !isset($course->id) || !$course->id) {
            return null;
        }

        return $this->calculate_course_progress($userid, $course);
    }

    public static function calculate_course_progress($userid, $course) {
        $completion = new completion_info($course);

        // First, let's make sure completion is enabled.
        if (!$completion->is_enabled()) {
            return null;
        }


        // Before we check how many modules have been completed see if the course has.
        if ($completion->is_course_complete($userid)) {
            return 100;
        }

        // Get the number of modules that support completion.
        $modules = $completion->get_activities();
        $count = count($modules);
        if (!$count) {
            return null;
        }

        // Get the number of modules that have been completed.
        $completed = 0;
        foreach ($modules as $module) {
            $data = $completion->get_data($module, true, $userid);
            if (($data->completionstate == COMPLETION_INCOMPLETE) || ($data->completionstate == COMPLETION_COMPLETE_FAIL)) {
                $completed += 0;
            } else {
                $completed += 1;
            };
        }

        $progress = ($completed / $count) * 100;

        return round($progress);
    }

    /**
     * Retorna a nota final do usuário no curso.
     *
     * @return float|null Nota final do curso ou null se não aplicável/disponível.
     */
    public function get_course_grade(): ?float
    {
        if (!$this->has_situation_to_do_the_course()) {
            return $this->get('grade');
        }

        $course = $this->get_course();

        if (!$course) {
            return null;
        }

        $grade_items = \grade_item::fetch_all([
            'courseid' => $course->id,
            'itemtype' => 'course'
        ]);

        if (!$grade_items) {
            return null;
        }

        $course_grade_item = reset($grade_items);

        if (!$course_grade_item) {
            return null;
        }

        if ($grades = \grade_grade::fetch_all(['userid' => $this->get('userid'), 'itemid' => $course_grade_item->id])) {
            foreach ($grades as $grade) {
                if ($grade->finalgrade) {
                    return $grade->finalgrade;
                }
            }
        }

        return null;
    }


    /**
     * Verifica se o usuário possui registros de sessões na atividade de presença com nota e
     * associada ao curso da turma desta inscrição.
     *
     * @return bool True se o usuário possui sessões, false caso contrário ou se a atividade não existir.
     */
    public function has_graded_attendance_session_log(): bool
    {
        global $DB;

        $attendance_cm = $this->get_course_attendance_mod();

        if (!$attendance_cm) {
            return false;
        }

        $userid = $this->get('userid');
        $attendanceid = $attendance_cm->instance;

        return $DB->record_exists_sql(
            "SELECT 1
            FROM {attendance_log} al
                JOIN {attendance_sessions} ats ON (al.sessionid = ats.id)
                JOIN {attendance_statuses} ast ON (al.statusid = ast.id)
            WHERE al.studentid = :userid
                AND ats.attendanceid = :attendanceid
                AND ast.grade > 0
            ",
            [
                'userid' => $userid,
                'attendanceid' => $attendanceid
            ]
        );
    }

    public function get_course_attendance_mod()
    {
        $offerclass = $this->get_offer_class();

        if (!$this->attendance_cm) {
            $this->attendance_cm = $offerclass->get_attendance_activity();
        }

        return $this->attendance_cm;
    }

    /**
     * Verifica se o curso da turma desta inscrição possui atividades do tipo presença.
     *
     * @return bool True se o curso possui atividade do tipo presença, false caso contrário.
     */
    public function course_has_attendance_module()
    {
        return !!$this->get_course_attendance_mod();
    }

    /**
     * Verifica se o curso da turma desta inscrição possui atividades com critério de conclusão por nota.
     *
     * @return bool True se o curso possui atividades com critério de conclusão por nota, false caso contrário.
     */
    public function course_has_modules_with_completion_by_grade()
    {
        $offerclass = $this->get_offer_class();
        return $offerclass->has_modules_with_completion_by_grade();
    }

    /**
     * Retrieves the offer user enrolment record based on user ID and course ID.
     * Optionally filters by situation(s).
     *
     *
     * @param int $userid The ID of the user.
     * @param int $courseid The ID of the course.
     * @param array|int|null $situations Optional. Situation(s) to filter by. Can be an array of situations, a single situation constant, or null for all situations.
     * @return array|false An instance of offer_user_enrol_model if found, false otherwise.
     */
    public static function get_offer_enrolments_by_user_and_course(
        int $userid,
        int $courseid,
        array|int|null $situations = null
    ): array|false {
        global $DB;

        $params = [
            $userid,
            $courseid
        ];

        $sql = '';

        if (is_integer($situations)) {
            $situations = [$situations];
        }

        if ($situations) {
            list($insql, $inparams) = $DB->get_in_or_equal($situations);
            $params = array_merge($params, $inparams);
            $sql = " AND situation $insql";
        }

        return self::get_records_select(
            "userid = ? AND courseid = ?" . $sql,
            $params
        );
    }
}
