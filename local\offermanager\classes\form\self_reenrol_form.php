<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\form;

use moodleform;
use html_writer;
use local_offermanager\persistent\offer_user_enrol_model;

defined('MOODLE_INTERNAL') || die();

/**
 * Class self_reenrol_form
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class self_reenrol_form extends moodleform
{
    /**
     * Overriding this function to get unique form id for multiple self enrolments.
     *
     * @return string form identifier
     */
    protected function get_form_identifier()
    {
        $formid = $this->_customdata . '_' . get_class($this);
        return $formid;
    }

    public function definition()
    {
        $mform = $this->_form;
        $offeruserenrolid = $this->_customdata;

        $offer_user_enrol = offer_user_enrol_model::get_record(['id' => $offeruserenrolid]);

        $offerclass = $offer_user_enrol->get_offer_class();
        $description = $offerclass->get_description_html();
        $course = $offer_user_enrol->get_course();
        $classname = $offerclass->get_mapped_field('classname');

        $mform->addElement('hidden', 'id');
        $mform->setType('id', PARAM_INT);
        $mform->setDefault('id', $course->id);

        $mform->addElement('hidden', 'offeruserenrolid');
        $mform->setType('offeruserenrolid', PARAM_INT);
        $mform->setDefault('offeruserenrolid', $offeruserenrolid);

        $mform->addElement('header', 'reenrol', get_string('reenrolme', 'local_offermanager', (object)['classname' => $classname]));

        if ($description) {
            $mform->addElement(
                'static',
                'description',
                get_string('description'),
                $description
            );
        }

        $startdate = $offerclass->get_mapped_field('startdate');
        $startdate = userdate($startdate, get_string('strftimedate', 'langconfig'));
        $startdate_str = get_string('class_format_start', 'local_offermanager', ['startdate' => $startdate]);

        $mform->addElement(
            'static',
            'enrolperiod',
            get_string('startdate', 'local_offermanager'),
            $startdate_str
        );

        $enableenrolperiod = $offerclass->get_mapped_field('enableenrolperiod');

        if ($enableenrolperiod) {
            $enrolperiod = (int) ($offerclass->get_mapped_field('enrolperiod') / DAYSECS);

            if($enrolperiod) {
                $day_string =  $enrolperiod . ' ' . ( $enrolperiod > 1 ? get_string('days') : get_string('day'));

                $enrolperiod_str = get_string('enrolperiod', 'local_offermanager') . ': ' . $day_string;
                $mform->addElement(
                    'static',
                    'enrolperiod',
                    get_string('enrolperiod', 'local_offermanager'),
                    $enrolperiod_str
                );
            }
        }

        $mform->addElement(
            'html',
            html_writer::div(
                html_writer::tag('div', get_string('reenrol_info', 'local_offermanager'), ['class' => 'p-2 badge-warning']),
                'my-2 px-4'
            )
        );

        $this->add_action_buttons(false, get_string('reenrolme_button', 'local_offermanager'));
    }

    public function validation($data, $files)
    {
        $errors = parent::validation($data, $files);

        return $errors;
    }
}
