<template>
  <div class="checkbox-container" :class="{ 'disabled': disabled }">
    <input
      type="checkbox"
      :id="id"
      :checked="modelValue"
      @change="$emit('update:modelValue', $event.target.checked)"
      class="custom-checkbox"
      :disabled="disabled"
    >
    <label :for="id" class="checkbox-label" :class="{ 'disabled': disabled }">
      <slot>{{ label }}</slot>
    </label>
  </div>
</template>

<script>
export default {
  name: 'CustomCheckbox',

  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    label: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },

  emits: ['update:modelValue']
}
</script>

<style lang="scss" scoped>
.checkbox-container {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff !important;
  height: 38px;
  width: auto !important; /* Garante que o container tenha apenas a largura necessária */
  min-width: auto !important; /* Evita que seja afetado pela largura mínima do Autocomplete */

  &.disabled {
    opacity: 0.65;
  }
}

.custom-checkbox {
  background-color: #212529 !important;
  border: 1px solid #495057 !important;
  border-radius: 4px;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 16px;
  height: 16px;
  position: relative;
  cursor: pointer;
  padding: 0 !important;
  box-sizing: border-box;

  &:checked {
    background-color: var(--primary) !important;
    border-color: var(--primary) !important;

    &::after {
      content: '\2714';
      font-size: 12px;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: white;
    }
  }

  &:focus {
    outline: 0;
    border-color: var(--primary) !important;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25) !important;
  }

  &:disabled {
    opacity: 0.65;
    cursor: not-allowed;
  }
}

.checkbox-label {
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  margin: 0;
  user-select: none;

  &.disabled {
    opacity: 0.65;
    cursor: not-allowed;
  }
}
</style>