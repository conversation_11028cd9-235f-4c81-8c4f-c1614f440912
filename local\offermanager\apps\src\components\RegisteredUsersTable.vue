<template>
  <div class="table-responsive">
    <table class="table">
      <thead>
        <tr>
          <th
            v-for="header in headers"
            :key="header.value"
            @click="header.sortable ? handleSort(header.value) : null"
            :class="{ sortable: header.sortable }"
          >
            <!-- Renderização especial para a coluna de checkbox -->
            <template v-if="header.value === 'checkbox'">
              <div class="checkbox-container">
                <input
                  type="checkbox"
                  class="custom-checkbox"
                  :checked="selectAll"
                  @change="toggleSelectAll"
                  title="Selecionar todos"
                />
              </div>
            </template>

            <!-- Renderização padrão para outras colunas -->
            <template v-else>
              {{ header.text }}
              <span v-if="header.sortable" class="sort-icon">
                <i class="fas" :class="{
                  'fa-sort': sortBy !== header.value,
                  'fa-sort-up': sortBy === header.value && !sortDesc,
                  'fa-sort-down': sortBy === header.value && sortDesc
                }"></i>
              </span>
            </template>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="item in items" :key="item.id">
          <td v-for="header in headers" :key="header.value">
            <!-- Renderização especial para a coluna de checkbox -->
            <template v-if="header.value === 'checkbox'">
              <div class="checkbox-container">
                <input
                  type="checkbox"
                  class="custom-checkbox"
                  :checked="isSelected(item.id)"
                  @change="toggleSelectUser(item.id)"
                />
              </div>
            </template>

            <!-- Renderização personalizada para outras colunas -->
            <template v-else>
              <slot :name="'item-' + header.value" :item="item">
                {{ item[header.value] }}
              </slot>
            </template>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
export default {
  name: 'RegisteredUsersTable',

  props: {
    headers: {
      type: Array,
      required: true
    },
    items: {
      type: Array,
      required: true
    },
    sortBy: {
      type: String,
      default: ''
    },
    sortDesc: {
      type: Boolean,
      default: false
    },
    selectedUsers: {
      type: Array,
      default: () => []
    },
    selectAll: {
      type: Boolean,
      default: false
    }
  },

  methods: {
    handleSort(value) {
      this.$emit('sort', {
        sortBy: value,
        sortDesc: this.sortBy === value ? !this.sortDesc : false
      })
    },

    toggleSelectAll() {
      this.$emit('toggle-select-all');
    },

    toggleSelectUser(userId) {
      this.$emit('toggle-select-user', userId);
    },

    isSelected(userId) {
      return this.selectedUsers.includes(userId);
    }
  }
}
</script>

<style lang="scss" scoped>
.table-responsive {
  width: 100%;
  overflow-x: auto;
  max-width: 100%;
}

.table {
  width: 100%;
  min-width: 1200px; /* Garante que a tabela tenha uma largura mínima */
  border-collapse: collapse;
  background-color: #212529 !important;

  thead {
    tr {
      border-bottom: 3px solid var(--primary) !important;
    }
  }

  th, td {
    padding: 10px 8px;
    text-align: left;
    border-bottom: 1px solid #373b3e !important;
    color: #fff !important;
    height: 50px;
  }

  th {
    font-weight: 700;
    text-transform: uppercase;
    font-size: 16px; /* Mantendo o tamanho original */
    color: var(--primary) !important;
    cursor: pointer;
    border: none;

    /* Apenas para a tabela de usuários matriculados */
    &.sortable {
      white-space: nowrap;
      font-size: 12px; /* Tamanho reduzido para caber em uma linha */
    }

    .sort-icon {
      margin-left: 5px;
      color: #ced4da;
    }
  }

  tbody tr:nth-child(odd) {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }

  td {
    font-size: 16px;
    font-weight: 400;
  }
}

.sortable {
  cursor: pointer;
}

.status-active {
  color: inherit;
}

.status-inactive {
  color: inherit;
}

tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.15) !important;
}

/* Estilos para os checkboxes */
.checkbox-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.custom-checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: var(--primary); /* Cor do checkbox quando marcado */
}
</style>
