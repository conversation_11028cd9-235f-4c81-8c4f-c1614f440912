<template>
  <modal
    :show="show"
    :confirm-button-text="confirmButtonText"
    :cancel-button-text="cancelButtonText"
    :confirm-disabled="confirmDisabled"
    size="sm"
    :show-default-footer="false"
    @close="$emit('close')"
    @confirm="$emit('confirm')"
  >
    <div class="confirmation-content" :class="{ 'has-list': hasListContent }">
      <div v-if="icon" class="icon-container">
        <i :class="iconClass"></i>
      </div>
      
      <h3 class="modal-custom-title">{{ title }}</h3>
      
      <div v-if="hasListContent" class="message-list">
        <p v-if="listTitle" class="list-title">{{ listTitle }}</p>
        <ul>
          <li v-for="(item, index) in listItems" :key="index">{{ item }}</li>
        </ul>
      </div>
      <div v-else class="message">
        {{ message }}
      </div>
      
      <div class="modal-custom-footer">
        <button class="btn-cancel" @click="$emit('close')">{{ cancelButtonText }}</button>
        <button 
          class="btn-danger" 
          :disabled="confirmDisabled" 
          @click="$emit('confirm')"
        >
          {{ confirmButtonText }}
        </button>
      </div>
    </div>
  </modal>
</template>

<script>
import Modal from './Modal.vue';

export default {
  name: 'ConfirmationModal',
  components: {
    Modal
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: 'Confirmação'
    },
    message: {
      type: String,
      default: ''
    },
    listTitle: {
      type: String,
      default: ''
    },
    listItems: {
      type: Array,
      default: () => []
    },
    confirmButtonText: {
      type: String,
      default: 'Confirmar'
    },
    cancelButtonText: {
      type: String,
      default: 'Cancelar'
    },
    confirmDisabled: {
      type: Boolean,
      default: false
    },
    icon: {
      type: String,
      default: 'warning',
      validator: (value) => ['warning', 'info', 'error', 'success', 'question', ''].includes(value)
    }
  },
  emits: ['close', 'confirm'],
  computed: {
    iconClass() {
      const iconMap = {
        warning: 'fas fa-exclamation-triangle text-warning',
        info: 'fas fa-info-circle text-info',
        error: 'fas fa-times-circle text-danger',
        success: 'fas fa-check-circle text-success',
        question: 'fas fa-question-circle text-primary'
      };
      
      return iconMap[this.icon] || '';
    },
    hasListContent() {
      return this.listItems && this.listItems.length > 0;
    }
  }
}
</script>

<style lang="scss" scoped>
.confirmation-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100%;
  
  &.has-list {
    margin: 0 auto;
  }
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  
  i {
    font-size: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &.text-warning {
      color: #ffc107;
    }
    
    &.text-info {
      color: #17a2b8;
    }
    
    &.text-danger {
      color: #dc3545;
    }
    
    &.text-success {
      color: #28a745;
    }
    
    &.text-primary {
      color: var(--primary);
    }
  }
}

.modal-custom-title {
  font-size: 14px;
  font-weight: bold;
  line-height: 1.5;
  color: #adb5bd;
  margin-bottom: 16px;
  text-align: center;
}

.message {
  font-size: 12px;
  color: #adb5bd;
  margin-bottom: 20px;
  text-align: left;
}

.message-list {
  text-align: left;
  width: 100%;
  margin-bottom: 20px;
  
  .list-title {
    font-size: 12px;
    color: #adb5bd;
    margin-bottom: 8px;
  }
  
  ul {
    list-style-type: disc;
    padding-left: 20px;
    margin: 0;
    
    li {
      font-size: 12px;
      color: #adb5bd;
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.modal-custom-footer {
  display: flex;
  width: 100%;
  gap: 8px;
  
  button {
    flex: 1;
    padding: 8px 16px;
    border-radius: 4px;
    border: none;
    font-weight: 500;
    cursor: pointer;
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
  
  .btn-cancel {
    color: #fff !important;
    background-color: #343a40 !important;
    border-color: #343a40 !important;
    
    &:hover:not(:disabled) {
      background-color: #6c757d !important;
      border-color: #6c757d !important;
    }
  }
  
  .btn-danger {
    background-color: #dc3545;
    color: #fff;
    
    &:hover:not(:disabled) {
      background-color: #bb2d3b;
    }
  }
}
</style> 