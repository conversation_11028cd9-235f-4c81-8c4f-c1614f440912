<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\classes\external;

use local_offermanager\persistent\offer_model;
use local_offermanager\external\offer_course_external;
use core_external\external_api;
use local_offermanager\constants;
use moodle_exception;

/**
 * Tests for Offer Manager
 *
 * @package    local_offermanager
 * @category   test
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
final class offer_course_external_test extends \advanced_testcase
{
    public function test_add_courses_success()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Test Offer'
        ]);
        $offer->save();

        $course1 = $this->getDataGenerator()->create_course();
        $course2 = $this->getDataGenerator()->create_course();

        $result = offer_course_external::add(
            $offer->get('id'),
            [$course1->id, $course2->id]
        );

        $result = external_api::clean_returnvalue(
            offer_course_external::add_returns(),
            $result
        );

        $this->assertCount(2, $result);
        $this->assertTrue($result[0]['succeed']);
        $this->assertTrue($result[1]['succeed']);
    }

    public function test_add_courses_failure()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Test Offer'
        ]);
        $offer->save();

        $course1 = $this->getDataGenerator()->create_course();

        $result = offer_course_external::add(
            $offer->get('id'),
            [$course1->id, $course1->id]
        );

        $result = external_api::clean_returnvalue(
            offer_course_external::add_returns(),
            $result
        );

        $this->assertCount(2, $result);
        $this->assertTrue($result[0]['succeed']);
        $this->assertFalse($result[1]['succeed']);
    }

    public function test_delete_course_succeed()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Test Offer'
        ]);
        $offer->save();

        $course1 = $this->getDataGenerator()->create_course();
        $offer_course = $offer->add_course($course1->id);

        $result = offer_course_external::delete($offer_course->get('id'));

        $result = external_api::clean_returnvalue(
            offer_course_external::delete_returns(),
            $result
        );

        $this->assertTrue($result);
    }

    public function test_delete_course_failure()
    {
        $this->resetAfterTest(true);

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:offer_not_found', 'local_offermanager'));

        offer_course_external::delete(
            999
        );
    }

    public function test_get_categories()
    {
        global $DB;
        $this->resetAfterTest(true);

        $DB->delete_records_select('course_categories', 'id > 0');

        $offer = new offer_model(0, (object) [
            'name' => 'Test Offer'
        ]);
        $offer->save();

        $category1 = $this->getDataGenerator()->create_category(['name' => 'Categoria 1']);
        $category2 = $this->getDataGenerator()->create_category(['name' => 'Categoria 2']);
        $course1 = $this->getDataGenerator()->create_course(['category' => $category1->id]);
        $course2 = $this->getDataGenerator()->create_course(['category' => $category2->id]);

        $offer->add_course($course1->id);
        $offer->add_course($course2->id);

        $result = offer_course_external::get_categories(
            $offer->get('id'),
            ''
        );

        $result = external_api::clean_returnvalue(
            offer_course_external::get_categories_returns(),
            $result
        );

        $this->assertCount(2, $result);
        $this->assertEquals($category1->name, $result[0]['name']);
        $this->assertEquals($category2->name, $result[1]['name']);
    }

    public function test_get_categories_search()
    {
        global $DB;

        $this->resetAfterTest(true);

        $DB->delete_records_select('course_categories', 'id > 0');

        $category1 = $this->getDataGenerator()->create_category(['name' => 'Matemática']);
        $category2 = $this->getDataGenerator()->create_category(['name' => 'Física']);

        $result = offer_course_external::get_categories(
            0,
            'mat'
        );

        $result = external_api::clean_returnvalue(
            offer_course_external::get_categories_returns(),
            $result
        );

        $this->assertCount(1, $result);
        $this->assertEquals($category1->name, $result[0]['name']);
    }

    public function test_get_categories_all()
    {
        global $DB;
        $this->resetAfterTest(true);

        $DB->delete_records_select('course_categories', 'id > 0');

        $category1 = $this->getDataGenerator()->create_category(['name' => 'Categoria 1']);
        $category2 = $this->getDataGenerator()->create_category(['name' => 'Categoria 2']);

        $result = offer_course_external::get_categories(
            0,
            ''
        );

        $result = external_api::clean_returnvalue(
            offer_course_external::get_categories_returns(),
            $result
        );

        $this->assertCount(2, $result);
        $this->assertEquals($category1->name, $result[0]['name']);
        $this->assertEquals($category2->name, $result[1]['name']);
    }

    public function test_get_categories_only_visible()
    {
        global $DB;
        $this->resetAfterTest(true);

        $DB->delete_records_select('course_categories', 'id > 0');

        $category1 = $this->getDataGenerator()->create_category(['name' => 'Categoria 1']);
        $category2 = $this->getDataGenerator()->create_category([
            'name' => 'Categoria 2',
            'visible' => 0
        ]);

        $result = offer_course_external::get_categories(
            0,
            ''
        );

        $result = external_api::clean_returnvalue(
            offer_course_external::get_categories_returns(),
            $result
        );

        $this->assertCount(1, $result);
        $this->assertEquals($category1->name, $result[0]['name']);
    }

    public function test_get_categories_with_offer()
    {
        global $DB;
        $this->resetAfterTest(true);

        $DB->delete_records_select('course_categories', 'id > 0');

        $offer = new offer_model(0, (object) [
            'name' => 'Test Offer'
        ]);
        $offer->save();

        $category1 = $this->getDataGenerator()->create_category(['name' => 'Categoria 1']);
        $category2 = $this->getDataGenerator()->create_category(['name' => 'Categoria 2']);
        $course1 = $this->getDataGenerator()->create_course(['category' => $category1->id]);
        $course2 = $this->getDataGenerator()->create_course(['category' => $category2->id]);

        // Vincula os cursos à oferta.
        $offer->add_course($course1->id);
        $offer->add_course($course2->id);

        $result = offer_course_external::get_categories(
            $offer->get('id'),
            'cat'
        );

        $result = external_api::clean_returnvalue(
            offer_course_external::get_categories_returns(),
            $result
        );

        $this->assertCount(2, $result);
        $this->assertEquals($category1->name, $result[0]['name']);
        $this->assertEquals($category2->name, $result[1]['name']);
    }

    public function test_fetch_current_courses_with_valid_parameters()
    {
        $this->resetAfterTest(true);

        $category = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 1']);
        $course2 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 2']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();
        $offer->add_course($course1->id);
        $offer->add_course($course2->id);

        $result = offer_course_external::fetch_current_courses($offer->get('id'), $category->id, 'Teste', []);

        $result = external_api::clean_returnvalue(
            offer_course_external::fetch_current_courses_returns(),
            $result
        );

        $this->assertCount(2, $result);
        $this->assertEquals($course1->id, $result[0]['id']);
        $this->assertEquals($course2->id, $result[1]['id']);
    }

    public function test_fetch_current_courses_with_nonexistent_offer()
    {
        $this->resetAfterTest(true);

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:offer_not_found', 'local_offermanager'));

        offer_course_external::fetch_current_courses(999, 0, '', []);
    }

    public function test_fetch_current_courses_with_nonexistent_category()
    {
        $this->resetAfterTest(true);

        $category = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 1']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();
        $offer->add_course($course1->id);

        $result = offer_course_external::fetch_current_courses($offer->get('id'), 999, '', []);

        $result = external_api::clean_returnvalue(
            offer_course_external::fetch_current_courses_returns(),
            $result
        );

        $this->assertEmpty($result);
    }

    public function test_fetch_current_courses_with_nonexistent_search_string()
    {
        $this->resetAfterTest(true);

        $category = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 1']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();
        $offer->add_course($course1->id);

        $result = offer_course_external::fetch_current_courses($offer->get('id'), $category->id, 'Nenhum Curso', []);

        $result = external_api::clean_returnvalue(
            offer_course_external::fetch_current_courses_returns(),
            $result
        );

        $this->assertEmpty($result);
    }

    public function test_fetch_current_courses_with_excluded_courses()
    {
        $this->resetAfterTest(true);

        $category = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 1']);
        $course2 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 2']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();
        $offer->add_course($course1->id);
        $offer->add_course($course2->id);

        $result = offer_course_external::fetch_current_courses($offer->get('id'), $category->id, '', [$course1->id]);

        $result = external_api::clean_returnvalue(
            offer_course_external::fetch_current_courses_returns(),
            $result
        );

        $this->assertCount(1, $result);
        $this->assertEquals($course2->id, $result[0]['id']);
    }

    public function test_fetch_current_courses_with_empty_exclude_list()
    {
        $this->resetAfterTest(true);

        $category = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 1']);
        $course2 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 2']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();
        $offer->add_course($course1->id);
        $offer->add_course($course2->id);

        $result = offer_course_external::fetch_current_courses($offer->get('id'), $category->id, '', []);

        $result = external_api::clean_returnvalue(
            offer_course_external::fetch_current_courses_returns(),
            $result
        );

        $this->assertCount(2, $result);
        $this->assertEquals($course1->id, $result[0]['id']);
        $this->assertEquals($course2->id, $result[1]['id']);
    }

    public function test_fetch_current_courses_with_invalid_exclude_list()
    {
        $this->resetAfterTest(true);

        $category = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 1']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();
        $offer->add_course($course1->id);

        $result = offer_course_external::fetch_current_courses($offer->get('id'), $category->id, '', [999, 1000]);

        $result = external_api::clean_returnvalue(
            offer_course_external::fetch_current_courses_returns(),
            $result
        );

        $this->assertCount(1, $result);
        $this->assertEquals($course1->id, $result[0]['id']);
    }

    public function test_fetch_current_courses_with_default_parameters()
    {
        $this->resetAfterTest(true);

        $category = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 1']);
        $course2 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 2']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();
        $offer->add_course($course1->id);
        $offer->add_course($course2->id);

        $result = offer_course_external::fetch_current_courses($offer->get('id'), 0, '', []);

        $result = external_api::clean_returnvalue(
            offer_course_external::fetch_current_courses_returns(),
            $result
        );

        $this->assertCount(2, $result);
        $this->assertEquals($course1->id, $result[0]['id']);
        $this->assertEquals($course2->id, $result[1]['id']);
    }

    public function test_get_current_courses_with_no_filters()
    {
        $this->resetAfterTest(true);

        $category = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 1']);
        $course2 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 2']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();
        $offer->add_course($course1->id);
        $offer->add_course($course2->id);

        $result = offer_course_external::get_current_courses($offer->get('id'), false, [], 1, 5);

        $result = external_api::clean_returnvalue(
            offer_course_external::get_current_courses_returns(),
            $result
        );

        $this->assertCount(2, $result);
        $this->assertEquals($course1->id, $result[0]['courseid']);
        $this->assertEquals($course2->id, $result[1]['courseid']);
    }

    public function test_get_current_courses_with_only_active()
    {
        $this->resetAfterTest(true);

        $category = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 1']);
        $course2 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 2']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();
        $offer->add_course($course1->id);
        $offer_course = $offer->add_course($course2->id);

        $offer_course->inactivate();

        $result = offer_course_external::get_current_courses($offer->get('id'), true, [], 1, 5);

        $result = external_api::clean_returnvalue(
            offer_course_external::get_current_courses_returns(),
            $result
        );

        $this->assertCount(1, $result);
        $this->assertEquals($course1->id, $result[0]['courseid']);
    }

    public function test_get_current_courses_with_specific_courseids()
    {
        $this->resetAfterTest(true);

        $category = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 1']);
        $course2 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 2']);
        $course3 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 3']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();
        $offer->add_course($course1->id);
        $offer->add_course($course2->id);
        $offer->add_course($course3->id);

        $result = offer_course_external::get_current_courses($offer->get('id'), false, [$course1->id, $course3->id], 1, 5);

        $result = external_api::clean_returnvalue(
            offer_course_external::get_current_courses_returns(),
            $result
        );

        $this->assertCount(2, $result);
        $this->assertEquals($course1->id, $result[0]['courseid']);
        $this->assertEquals($course3->id, $result[1]['courseid']);
    }

    public function test_get_current_courses_with_pagination()
    {
        $this->resetAfterTest(true);

        $category = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 1']);
        $course2 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 2']);
        $course3 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 3']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();
        $offer->add_course($course1->id);
        $offer->add_course($course2->id);
        $offer->add_course($course3->id);

        $result_page1 = offer_course_external::get_current_courses($offer->get('id'), false, [], 1, 2);
        $result_page2 = offer_course_external::get_current_courses($offer->get('id'), false, [], 2, 2);

        $result_page1 = external_api::clean_returnvalue(
            offer_course_external::get_current_courses_returns(),
            $result_page1
        );

        $result_page2 = external_api::clean_returnvalue(
            offer_course_external::get_current_courses_returns(),
            $result_page2
        );

        $this->assertCount(2, $result_page1);
        $this->assertEquals($course1->id, $result_page1[0]['courseid']);
        $this->assertEquals($course2->id, $result_page1[1]['courseid']);

        $this->assertCount(1, $result_page2);
        $this->assertEquals($course3->id, $result_page2[0]['courseid']);
    }

    public function test_get_current_courses_with_empty_courseids()
    {
        $this->resetAfterTest(true);

        $category = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 1']);
        $course2 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 2']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();
        $offer->add_course($course1->id);
        $offer->add_course($course2->id);

        $result = offer_course_external::get_current_courses($offer->get('id'), false, [], 1, 5);

        $result = external_api::clean_returnvalue(
            offer_course_external::get_current_courses_returns(),
            $result
        );

        $this->assertCount(2, $result);
        $this->assertEquals($course1->id, $result[0]['courseid']);
        $this->assertEquals($course2->id, $result[1]['courseid']);
    }

    public function test_get_current_courses_with_invalid_courseids()
    {
        $this->resetAfterTest(true);

        $category = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 1']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();
        $offer->add_course($course1->id);

        $result = offer_course_external::get_current_courses($offer->get('id'), false, [999, 1000], 1, 5);

        $result = external_api::clean_returnvalue(
            offer_course_external::get_current_courses_returns(),
            $result
        );

        $this->assertEmpty($result);
    }

    public function test_get_current_courses_with_pagination_and_insufficient_courses()
    {
        $this->resetAfterTest(true);

        $category = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 1']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();
        $offer->add_course($course1->id);

        $result = offer_course_external::get_current_courses($offer->get('id'), false, [], 2, 2);

        $result = external_api::clean_returnvalue(
            offer_course_external::get_current_courses_returns(),
            $result
        );

        $this->assertEmpty($result);
    }

    public function test_get_current_courses_with_per_page_zero()
    {
        $this->resetAfterTest(true);

        $category = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 1']);
        $course2 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 2']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();
        $offer->add_course($course1->id);
        $offer->add_course($course2->id);

        $result = offer_course_external::get_current_courses($offer->get('id'), false, [], 1, 0);

        $result = external_api::clean_returnvalue(
            offer_course_external::get_current_courses_returns(),
            $result
        );

        $this->assertCount(2, $result);
        $this->assertEquals($course1->id, $result[0]['courseid']);
        $this->assertEquals($course2->id, $result[1]['courseid']);
    }

    public function test_fetch_potential_courses_with_no_filters()
    {
        $this->resetAfterTest(true);

        $category = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 1']);
        $course2 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 2']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();

        $result = offer_course_external::fetch_potential_courses($offer->get('id'), 0, '', []);

        $result = external_api::clean_returnvalue(
            offer_course_external::fetch_potential_courses_returns(),
            $result
        );

        $this->assertCount(2, $result);
        $this->assertEquals($course1->id, $result[0]['id']);
        $this->assertEquals($course2->id, $result[1]['id']);
    }

    public function test_fetch_potential_courses_with_category_filter()
    {
        $this->resetAfterTest(true);

        $category1 = $this->getDataGenerator()->create_category();
        $category2 = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category1->id, 'fullname' => 'Curso de Teste 1']);
        $course2 = $this->getDataGenerator()->create_course(['category' => $category2->id, 'fullname' => 'Curso de Teste 2']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();

        $result = offer_course_external::fetch_potential_courses($offer->get('id'), $category1->id, '', []);

        $result = external_api::clean_returnvalue(
            offer_course_external::fetch_potential_courses_returns(),
            $result
        );

        $this->assertCount(1, $result);
        $this->assertEquals($course1->id, $result[0]['id']);
    }

    public function test_fetch_potential_courses_with_search_string()
    {
        $this->resetAfterTest(true);

        $category = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 1']);
        $course2 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Outro Curso']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();

        $result = offer_course_external::fetch_potential_courses($offer->get('id'), 0, 'Teste', []);

        $result = external_api::clean_returnvalue(
            offer_course_external::fetch_potential_courses_returns(),
            $result
        );

        $this->assertCount(1, $result);
        $this->assertEquals($course1->id, $result[0]['id']);
    }

    public function test_fetch_potential_courses_without_exclusion()
    {
        $this->resetAfterTest(true);

        $course1 = $this->getDataGenerator()->create_course(['fullname' => 'Matemática Avançada']);
        $course2 = $this->getDataGenerator()->create_course(['fullname' => 'MATEMATICA BASICA']);

        $course3 = $this->getDataGenerator()->create_course(['fullname' => 'MATEMATICA Intermediária']);
        $course4 = $this->getDataGenerator()->create_course(['fullname' => 'MATEMATICA AVANçADA']);

        $offer = new offer_model(0, (object) [
            'name' => 'Test Offer'
        ]);
        $offer->save();

        $offer->add_course($course3->id);
        $offer->add_course($course4->id);

        $result = offer_course_external::fetch_potential_courses(
            $offer->get('id'),
            0,
            'Matemática',
            []
        );

        $result = external_api::clean_returnvalue(
            offer_course_external::fetch_potential_courses_returns(),
            $result
        );

        $this->assertCount(2, $result);
    }

    public function test_fetch_potential_courses_with_exclusion()
    {
        $this->resetAfterTest(true);

        $course1 = $this->getDataGenerator()->create_course(['fullname' => 'Matemática Avançada']);
        $course2 = $this->getDataGenerator()->create_course(['fullname' => 'MATEMATICA BASICA']);

        $offer = new offer_model(0, (object) [
            'name' => 'Test Offer'
        ]);
        $offer->save();

        $result = offer_course_external::fetch_potential_courses(
            $offer->get('id'),
            0,
            'Matemática',
            [3, 5]
        );

        $result = external_api::clean_returnvalue(
            offer_course_external::fetch_potential_courses_returns(),
            $result
        );

        $this->assertCount(2, $result);

        $result = offer_course_external::fetch_potential_courses(
            $offer->get('id'),
            0,
            'Matemática',
            [3, 5, $course1->id]
        );

        $result = external_api::clean_returnvalue(
            offer_course_external::fetch_potential_courses_returns(),
            $result
        );

        $this->assertCount(1, $result);
    }

    public function test_fetch_potential_courses_with_invalid_exclude_list()
    {
        $this->resetAfterTest(true);

        $category = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 1']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();

        $result = offer_course_external::fetch_potential_courses($offer->get('id'), 0, '', [999, 1000]);

        $result = external_api::clean_returnvalue(
            offer_course_external::fetch_potential_courses_returns(),
            $result
        );

        $this->assertCount(1, $result);
        $this->assertEquals($course1->id, $result[0]['id']);
    }

    public function test_fetch_potential_courses_with_nonexistent_offer()
    {
        $this->resetAfterTest(true);

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:offer_not_found', 'local_offermanager'));

        offer_course_external::fetch_potential_courses(999, 0, '', []);
    }

    public function test_set_status_activate()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();

        $course = $this->getDataGenerator()->create_course();
        $offer_course = $offer->add_course($course->id);
        $offer_course->inactivate();

        $result = offer_course_external::set_status($offer_course->get('id'), true);

        $result = external_api::clean_returnvalue(
            offer_course_external::set_status_returns(),
            $result
        );

        $this->assertTrue($result['status']);
        $this->assertEquals(get_string('event:offercourseactivated', 'local_offermanager'), $result['message']);

        $updated_offer_course = $offer_course->read();
        $this->assertEquals(constants::OFFER_STATUS_ACTIVE, $updated_offer_course->get('status'));
    }

    public function test_set_status_inactivate()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $result = offer_course_external::set_status($offer_course->get('id'), false);

        $result = external_api::clean_returnvalue(
            offer_course_external::set_status_returns(),
            $result
        );

        $this->assertFalse($result['status']);
        $this->assertEquals(get_string('event:offercourseinactivated', 'local_offermanager'), $result['message']);

        $updated_offer_course = $offer_course->read();
        $this->assertEquals(constants::OFFER_STATUS_INACTIVE, $updated_offer_course->get('status'));
    }

    public function test_set_status_activate_already_active()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();

        $course = $this->getDataGenerator()->create_course();
        $offer_course = $offer->add_course($course->id);

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:offer_course_already_active', 'local_offermanager'));

        offer_course_external::set_status($offer_course->get('id'), true);
    }

    public function test_set_status_inactivate_already_inactive()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);
        $offer_course->inactivate();

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:offer_course_already_inactive', 'local_offermanager'));

        offer_course_external::set_status($offer_course->get('id'), false);
    }

    public function test_set_status_nonexistent_relation()
    {
        $this->resetAfterTest(true);

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:offer_not_found', 'local_offermanager'));

        offer_course_external::set_status(999, true);
    }

    public function test_set_status_return_structure()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();

        $course = $this->getDataGenerator()->create_course();
        $offer_course = $offer->add_course($course->id);
        $offer_course->inactivate();

        $result = offer_course_external::set_status($offer_course->get('id'), true);

        $result = external_api::clean_returnvalue(
            offer_course_external::set_status_returns(),
            $result
        );

        $this->assertArrayHasKey('status', $result);
        $this->assertArrayHasKey('message', $result);
        $this->assertIsBool($result['status']);
        $this->assertIsString($result['message']);
    }
}
