<template>
  <div class="action-buttons">
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'ActionButtons'
}
</script>

<style lang="scss" scoped>
.action-buttons {
  display: flex;
  gap: 5px;

  img {
    width: 16px;
    height: 16px;
    display: block;
  }
}

/* Estilos específicos que sobrescrevem o global.scss */
.btn-action {
  padding: 5px;
  margin: 0 2px;
  display: inline-flex;
}
</style>