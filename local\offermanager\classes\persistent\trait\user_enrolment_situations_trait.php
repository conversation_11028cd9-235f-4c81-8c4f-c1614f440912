<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\persistent\trait;

use local_offermanager\constants;
use local_offermanager\persistent\offer_cancel_reason_model;
use local_offermanager\event\offer_user_enrol_enroled;
use local_offermanager\event\offer_user_enrol_in_progress;
use local_offermanager\event\offer_user_enrol_canceled;
use local_offermanager\event\offer_user_enrol_approved;
use local_offermanager\event\offer_user_enrol_completed;
use local_offermanager\event\offer_user_enrol_failed;
use local_offermanager\event\offer_user_enrol_not_completed;
use local_offermanager\event\offer_user_enrol_abandoned;
use core\session\manager;
use context_offer_class;

use stdClass;
use moodle_exception;

defined('MOODLE_INTERNAL') || die();

/**
 * Trait user_enrolment_situations_trait
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
trait user_enrolment_situations_trait
{
    /**
     * Sets the enrolment situation to ENROLED.
     *
     * @return bool Returns true if the situation was successfully updated.
     */
    public function set_enroled(): bool
    {
        $this->set('situation', constants::OFFER_USER_ENROL_SITUATION_ENROLED);
        return $this->save();
    }

    /**
     * Sets the enrolment situation to IN_PROGRESS.
     *
     * @return bool Returns true if the situation was successfully updated.
     */
    public function set_in_progress(): bool
    {
        $this->set('situation', constants::OFFER_USER_ENROL_SITUATION_IN_PROGRESS);
        return $this->save();
    }

    /**
     * Sets the enrolment situation to APPROVED.
     *
     * @return bool Returns true if the situation was successfully updated.
     */
    public function set_approved(): bool
    {
        $this->set('situation', constants::OFFER_USER_ENROL_SITUATION_APPROVED);
        return $this->save();
    }

    /**
     * Sets the enrolment situation to COMPLETED.
     *
     * @return bool Returns true if the situation was successfully updated.
     */
    public function set_completed(): bool
    {
        $this->set('situation', constants::OFFER_USER_ENROL_SITUATION_COMPLETED);
        return $this->save();
    }

    /**
     * Registers the cancellation of an enrolment.
     *
     * @param string|null $reason for cancellation (optional).
     * @return bool
     * @throws moodle_exception If validation fails.
     */
    public function set_canceled(?string $reason = null): bool
    {
        global $USER;
        
        $this->update_grade_and_progress();
        $userid = $this->get('userid');
        $self_canceled = $USER->id == $userid;
        $this->set('situation', $self_canceled ? constants::OFFER_USER_ENROL_SITUATION_USER_CANCELED : constants::OFFER_USER_ENROL_SITUATION_ADMIN_CANCELED);
        $this->set('self_canceled', (int) $self_canceled);
        $this->set('usercanceled', $USER->id);
        $this->set('timecanceled', time());

        $this->save();

        $offerclass = $this->get_offer_class();

        
        if ($reason) {
            $this->register_cancellation_reason($reason);
        }
        
        $offerclass->suspend_user_enrol($userid);

        if($this->is_admin_canceled()){

            manager::kill_user_sessions($this->get('userid'));
        }

        $event = offer_user_enrol_canceled::instance($this);

        $event->trigger();

        return !!$this->get('timecanceled');
    }

    /**
     * Sets the enrolment situation to FAILED.
     *
     * @return bool Returns true if the situation was successfully updated.
     */
    public function set_failed(): bool
    {
        $this->set('situation', constants::OFFER_USER_ENROL_SITUATION_FAILED);
        return $this->save();
    }

    /**
     * Sets the enrolment situation to NOT_COMPLETED.
     *
     * @return bool Returns true if the situation was successfully updated.
     */
    public function set_not_completed(): bool
    {
        $this->set('situation', constants::OFFER_USER_ENROL_SITUATION_NOT_COMPLETED);
        return $this->save();
    }

    /**
     * Sets the enrolment situation to ABANDONED.
     *
     * @return bool Returns true if the situation was successfully updated.
     */
    public function set_abandoned(): bool
    {
        $this->set('situation', constants::OFFER_USER_ENROL_SITUATION_ABANDONED);
        return $this->save();
    }

    /**
     * Checks if the current situation is ENROLED.
     *
     * @return bool
     */
    public function is_enroled(): bool
    {
        return $this->get('situation') == constants::OFFER_USER_ENROL_SITUATION_ENROLED;
    }

    /**
     * Checks if the current situation is IN_PROGRESS.
     *
     * @return bool
     */
    public function is_in_progress(): bool
    {
        return $this->get('situation') == constants::OFFER_USER_ENROL_SITUATION_IN_PROGRESS;
    }

    /**
     * Checks if the current situation is APPROVED.
     *
     * @return bool
     */
    public function is_approved(): bool
    {
        return $this->get('situation') == constants::OFFER_USER_ENROL_SITUATION_APPROVED;
    }

    /**
     * Checks if the current situation is COMPLETED.
     *
     * @return bool
     */
    public function is_completed(): bool
    {
        return $this->get('situation') == constants::OFFER_USER_ENROL_SITUATION_COMPLETED;
    }

    /**
     * Checks if the enrolment has been canceled, whoever canceled it.
     *
     * @return bool True if the enrolment is canceled, false otherwise.
     */
    public function is_canceled(): bool
    {
        return !!$this->get('timecanceled');
    }

    /**
     * Checks if the current situation is USER_CANCELED.
     *
     * @return bool
     */
    public function is_user_canceled(): bool
    {
        return $this->get('situation') == constants::OFFER_USER_ENROL_SITUATION_USER_CANCELED;
    }

    /**
     * Checks if the current situation is ADMIN_CANCELED.
     *
     * @return bool
     */
    public function is_admin_canceled(): bool
    {
        return $this->get('situation') == constants::OFFER_USER_ENROL_SITUATION_ADMIN_CANCELED;
    }

    /**
     * Checks if the current situation is FAILED.
     *
     * @return bool
     */
    public function is_failed(): bool
    {
        return $this->get('situation') == constants::OFFER_USER_ENROL_SITUATION_FAILED;
    }

    /**
     * Checks if the current situation is NOT_COMPLETED.
     *
     * @return bool
     */
    public function is_not_completed(): bool
    {
        return $this->get('situation') == constants::OFFER_USER_ENROL_SITUATION_NOT_COMPLETED;
    }

    /**
     * Checks if the current situation is ABANDONED.
     *
     * @return bool
     */
    public function is_abandoned(): bool
    {
        return $this->get('situation') == constants::OFFER_USER_ENROL_SITUATION_ABANDONED;
    }

    public function has_situation_to_do_the_course()
    {
        return $this->is_enroled() || $this->is_in_progress();
    }

    /**
     * Checks if the enrolment is active (not canceled, failed or abandoned).
     *
     * @return bool
     */
    public function is_active(): bool
    {
        $inactive_states = [
            constants::OFFER_USER_ENROL_SITUATION_USER_CANCELED,
            constants::OFFER_USER_ENROL_SITUATION_ADMIN_CANCELED,
            constants::OFFER_USER_ENROL_SITUATION_FAILED,
            constants::OFFER_USER_ENROL_SITUATION_ABANDONED
        ];

        return !in_array($this->get('situation'), $inactive_states);
    }

    /**
     * Gets the human-readable name of the current situation.
     *
     * @return string
     */
    public function get_situation_name(): string
    {
        $situation = $this->get('situation');

        $names = constants::get_situation_list();

        return $names[$situation] ?? get_string('situation:unknown', 'local_offermanager');
    }

    /**
     * Validates if a transition between two situations is allowed.
     *
     * @param int $current_situation
     * @param int $new_situation
     * @return bool
     */
    public static function is_transition_allowed(int $current_situation, int $new_situation): bool
    {
        $allowed_transitions = constants::ALLOWED_SITUATION_TRANSITIONS;

        if (!isset($allowed_transitions[$current_situation])) {
            return false;
        }

        return in_array($new_situation, $allowed_transitions[$current_situation]);
    }

    /**
     * Safely updates the situation with transition validation and triggers the corresponding event.
     *
     * @param int $new_situation
     * @param string|null $reason Optional reason for the transition (especially for cancellations)
     * @return bool
     * @throws moodle_exception If transition is not allowed
     */
    public function update_situation(int $new_situation, ?string $reason = null): bool
    {
        $current_situation = $this->get('situation');

        if ($current_situation === $new_situation) {
            return true;
        }

        if (!self::is_transition_allowed($current_situation, $new_situation)) {
            throw new moodle_exception(
                'error:invalid_situation_transition',
                'local_offermanager',
                '',
                (object)[
                    'current' => $this->get_situation_name(),
                    'new' => (new self())->set('situation', $new_situation)->get_situation_name()
                ]
            );
        }

        if (in_array($new_situation, [
            constants::OFFER_USER_ENROL_SITUATION_USER_CANCELED,
            constants::OFFER_USER_ENROL_SITUATION_ADMIN_CANCELED
        ])) {
            return $this->set_canceled($reason);
        }

        $this->set('situation', $new_situation);
        $this->save();

        $saved = $this->get('situation') == $new_situation;


        $suspended = false;

        $suspended = $this->is_situation_to_suspend($new_situation);

        if ($suspended) {
            $offerclass = $this->get_offer_class();

            $suspended = $offerclass->suspend_user_enrol($this->get('userid'));

            if ($suspended) {
                $this->fetch_user_enrolment();
            }
        }

        if ($saved) {
            switch ($new_situation) {
                case constants::OFFER_USER_ENROL_SITUATION_ENROLED:
                    offer_user_enrol_enroled::instance($this)->trigger();
                    break;
                case constants::OFFER_USER_ENROL_SITUATION_IN_PROGRESS:
                    offer_user_enrol_in_progress::instance($this)->trigger();
                    break;
                case constants::OFFER_USER_ENROL_SITUATION_APPROVED:
                    offer_user_enrol_approved::instance($this)->trigger();
                    break;
                case constants::OFFER_USER_ENROL_SITUATION_COMPLETED:
                    offer_user_enrol_completed::instance($this)->trigger();
                    break;
                case constants::OFFER_USER_ENROL_SITUATION_FAILED:
                    offer_user_enrol_failed::instance($this)->trigger();
                    break;
                case constants::OFFER_USER_ENROL_SITUATION_NOT_COMPLETED:
                    offer_user_enrol_not_completed::instance($this)->trigger();
                    break;
                case constants::OFFER_USER_ENROL_SITUATION_ABANDONED:
                    offer_user_enrol_abandoned::instance($this)->trigger();
                    break;
                    // Eventos cancelados são tratados em set_canceled().
            }
        }

        return $saved && $suspended;
    }

    public function is_situation_to_suspend($new_situation)
    {
        return in_array($new_situation, constants::get_to_suspend_situations_list());
    }

    /**
     * Returns the cancellation details.
     *
     * @return stdClass An object containing cancellation details.
     */
    public function get_cancellation_details(): stdClass
    {
        $timecanceled = $this->get('timecanceled');

        if (!$timecanceled) {
            throw new moodle_exception('error:user_enrol_not_canceled', 'local_offermanager');
        }
        return (object)[
            'self_canceled' => $this->get('self_canceled'),
            'reason' => $this->get_cancellation_reason(),
            'usercanceled' => $this->get('usercanceled'),
            'timecanceled' => $this->get('timecanceled'),
        ];
    }

    /**
     * Retrieves the cancellation reason associated with this enrolment.
     *
     * @return string Cancellation reason.
     */
    public function get_cancellation_reason(): string
    {
        $reason = offer_cancel_reason_model::get_record(
            [
                'offeruserenrolid' => $this->get('id')
            ]
        );

        return $reason ? $reason->get('reason') : '';
    }

    /**
     * Registers the cancellation reason for the enrolment.
     *
     * @param string $reason The reason for cancellation.
     * @return bool
     */
    public function register_cancellation_reason(string $reason): bool
    {
        if (empty($reason)) {
            throw new moodle_exception('error:cancel_reason_cannot_be_empty', 'local_offermanager');
        }
        $cancel_reason = new offer_cancel_reason_model();
        $cancel_reason->set('offeruserenrolid', $this->get('id'));
        $cancel_reason->set('ueid', $this->get('ueid'));
        $cancel_reason->set('reason', $reason);
        $cancel_reason->save();

        return !!$cancel_reason->get('id');
    }

    /**
     * Processa a expiração do matrícula e atualiza a situação do usuário conforme necessário.
     *
     * @return bool Retorna true se a situação foi atualizada com sucesso.
     */
    public function process_expiration(): bool
    {
        $this->update_grade_and_progress();

        if ($this->is_enroled() || ($this->course_has_attendance_module() && !$this->has_graded_attendance_session_log())) {
            return $this->update_situation(constants::OFFER_USER_ENROL_SITUATION_ABANDONED);
        }
        elseif ($this->course_has_modules_with_completion_by_grade()) {
            return $this->update_situation(constants::OFFER_USER_ENROL_SITUATION_FAILED);
        }
        else {
            return $this->update_situation(constants::OFFER_USER_ENROL_SITUATION_NOT_COMPLETED);
        }

        return false;
    }

    public function can_cancel()
    {
        if (!$this->is_active()) {
            return false;
        }

        $offerclass = $this->get_offer_class();
        $pluginname = $offerclass->get('enrol');

        $offerclass = $this->get_offer_class();
        $offerclass_context = context_offer_class::instance($offerclass->get('id'));

        return has_capability("enrol/{$pluginname}:unenrolself", $offerclass_context);
    }
}
