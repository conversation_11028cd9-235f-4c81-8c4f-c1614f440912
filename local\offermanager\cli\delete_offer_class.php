<?php
define('CLI_SCRIPT', true);

require(__DIR__ . '/../../../config.php');
require_once($CFG->libdir.'/clilib.php');

use local_offermanager\external\offer_class_external;

// Parâmetros aceitos
$longoptions = [
    'offerclassid' => false,
    'help' => false
];

$shortoptions = [
    'i' => 'offerclassid',
    'h' => 'help'
];

list($options, $unrecognized) = cli_get_params($longoptions, $shortoptions);

if (!empty($options['help'])) {
    echo "Exclui uma turma vinculada a uma oferta de curso\n";
    echo "Uso: php delete_offer_class.php [opções]\n";
    echo "Parâmetros obrigatórios:\n";
    echo "  -i --offerclassid=ID     ID da turma a ser excluída\n";
    echo "Parâmetros opcionais:\n";
    echo "  -h --help                Exibe esta ajuda\n";
    echo "Exemplo:\n";
    echo "  php delete_offer_class.php --offerclassid=10\n";
    exit(0);
}

if (empty($options['offerclassid'])) {
    cli_error("Parâmetro obrigatório ausente: --offerclassid");
}

$offerclassid = (int)$options['offerclassid'];

try {
    $result = offer_class_external::delete($offerclassid);
    cli_writeln("Turma excluída com sucesso: $result");
} catch (Exception $e) {
    cli_error('Erro ao excluir turma: ' . $e->getMessage());
}