<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\persistent\trait;

use local_offermanager\persistent\offer_course_model;
use context_course;
use moodle_exception;
use course_modinfo;
use completion_info;
use stdClass;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir . '/completionlib.php');

/**
 * Trait class_course_trait
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
trait class_course_trait
{
    /** @var stdClass|null Curso associado */
    protected ?stdClass $course = null;

    /** @var course_modinfo|null Informações de módulos do curso associado */
    protected ?course_modinfo $modinfo = null;

    /** @var completion_info|null Informações de conclusão do curso associado */
    protected ?completion_info $completion_info = null;

    /**
     * Retorna a relação entre oferta e curso associada a este registro.
     *
     * @return offer_course_model
     */
    public function get_offer_course()
    {
        return new offer_course_model($this->get('offercourseid'));
    }

    /**
     * Retorna a lista de papéis disponíveis no curso.
     *
     * @return array Lista de papéis com id e nome
     */
    public function get_course_roles(): array
    {
        $offercourse = $this->get_offer_course();

        return $offercourse->get_course_roles();
    }

    /**
     * Verifica se o usuário tem uma atribuição de papel específica no contexto do curso.
     * 
     * @param int $userid ID do usuário
     * @param int $roleid ID do papel
     * @return bool
     */
    public function has_role_assignment($userid, $roleid)
    {
        $course = $this->get_course();
        $context = context_course::instance($course->id);

        return user_has_role_assignment($userid, $roleid, $context->id);
    }

    /**
     * Retorna o curso associado.
     *
     * @return stdClass
     * @throws moodle_exception
     */
    public function get_course(): stdClass
    {
        if (is_null($this->course)) {
            $offer_course = $this->get_offer_course();
            $courseid = $offer_course->get('courseid');

            if (!$courseid) {
                throw new moodle_exception('error:course_not_found', 'local_offermanager');
            }
            $this->course = get_course($courseid);

            if (!$this->course) {
                throw new moodle_exception('error:course_not_found', 'local_offermanager');
            }
        }

        return $this->course;
    }

    /**
     * Retorna as informações dos módulos do curso.
     *
     * @return course_modinfo|null
     * @throws moodle_exception
     */
    public function get_modinfo(): course_modinfo|null
    {
        if (is_null($this->modinfo)) {
            $course = $this->get_course();
            $this->modinfo = get_fast_modinfo($course->id);
        }

        return $this->modinfo;
    }

    /**
     * Retorna as informações de conclusão do curso.
     *
     * @return completion_info
     * @throws moodle_exception
     */
    public function get_completion_info(): completion_info
    {
        if (is_null($this->completion_info)) {
            $course = $this->get_course();
            $this->completion_info = new completion_info($course);
        }

        return $this->completion_info;
    }

    /**
     * Verifica se o curso possui uma atividade do tipo attendance e a retorna.
     *
     * @return \cm_info|false A instância da atividade attendance (cm_info) ou false se não encontrada.
     * @throws \moodle_exception
     */
    public function get_attendance_activity()
    {
        $modinfo = $this->get_modinfo();

        if(!$modinfo) {
            return false;
        }

        $attendance_instances = $modinfo->get_instances_of('attendance');

        if (empty($attendance_instances)) {
            return false;
        }

        return reset($attendance_instances);
    }

    /**
     * Verifica se o curso possui atividades com critério de conclusão que requer nota de aprovação.
     * 
     * @return bool True se existir pelo menos uma atividade com critério de conclusão por nota
     * @throws moodle_exception
     */
    public function has_modules_with_completion_by_grade(): bool
    {
        $completion = $this->get_completion_info();

        $modinfo = $this->get_modinfo();

        if(!$modinfo) {
            return false;
        }

        foreach ($modinfo->get_cms() as $cm) {
            foreach ($modinfo->get_cms() as $cm) {
                if ($completion->is_enabled($cm) && !empty($cm->completionpassgrade)) {
                    return true;
                }
            }
        }

        return false;
    }
}
