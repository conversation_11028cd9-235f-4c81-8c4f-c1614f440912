<template>
  <button class="btn-back" @click="goBack">
    <i class="fas fa-angle-left"></i> {{ label }}
  </button>
</template>

<script>
export default {
  name: "BackButton",
  props: {
    label: {
      type: String,
      default: "Voltar",
    },
    route: {
      type: String,
      default: "/local/offermanager/",
    },
  },
  methods: {
    goBack() {
      // Apenas emite o evento click para o componente pai tratar a navegação
      this.$emit("click");
    },
  },
};
</script>

<style lang="scss" scoped>
.btn-back {
  background: transparent;
  border: none;
  color: var(--primary);
  cursor: pointer;
  font-size: 1rem;
  padding: 0.5rem 0;
  display: inline-flex;
  align-items: center;

  i {
    margin-right: 8px;
    font-size: 1.5rem;
  }

  @media (max-width: 768px) {
    align-self: flex-start;
  }
}
</style>
