<template>
  <div class="filter-section">
    <h2 v-if="title">{{ title }}</h2>

    <div class="filter-content">
      <slot></slot>
    </div>

    <!-- Tags de filtro -->
    <div class="filter-tags" v-if="hasActiveTags">
      <slot name="tags"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FilterSection',

  props: {
    title: {
      type: String,
      default: 'FILTRO'
    },
    hasActiveTags: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-section {
  background-color: #212529;
  border-radius: 8px;

  h2 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 1.5rem;
    color: #fff;
  }
}

.filter-content {
  margin-bottom: 1rem;
}
</style>