<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * External functions and service declaration for Offer Manager
 *
 * Documentation: {@link https://moodledev.io/docs/apis/subsystems/external/description}
 *
 * @package    local_offermanager
 * @category   webservice
 * @copyright  2025 2015 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$functions = [
    'local_offermanager_fetch' => [
        'classname'   => 'local_offermanager\external\offer_external',
        'methodname'  => 'fetch',
        'description' => 'Retorna ofertas filtradas com base nos parâmetros informados.',
        'type'        => 'read',
        'ajax'        => true,
        'capabilities' => '',
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],

    'local_offermanager_get' => [
        'classname'   => 'local_offermanager\external\offer_external',
        'methodname'  => 'get',
        'description' => 'Retorna os detalhes de uma oferta pelo ID',
        'type'        => 'read',
        'ajax'        => true,
        'capabilities' => '',
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],

    'local_offermanager_get_types' => [
        'classname'   => 'local_offermanager\external\offer_external',
        'methodname'  => 'get_types',
        'description' => 'Retorna os tipos das ofertas. Recebe uma string como parâmetro para filtrar o nome',
        'type'        => 'read',
        'ajax'        => true,
        'capabilities' => '',
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_get_type_options' => [
        'classname' => 'local_offermanager\external\offer_external',
        'methodname' => 'get_type_options',
        'description' => 'Retorna os tipos de oferta e o valor padrão.',
        'type' => 'read',
        'ajax' => true,
        'capabilities' => '',
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
        'loginrequired' => true,
    ],
    'local_offermanager_save' => [
        'classname'   => 'local_offermanager\external\offer_external',
        'methodname'  => 'save',
        'description' => 'Cria ou atualiza uma oferta',
        'type'        => 'write',
        'ajax'        => true,
        'capabilities' => 'local/offermanager:manage',
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_delete' => [
        'classname'   => 'local_offermanager\external\offer_external',
        'methodname'  => 'delete',
        'description' => 'Exclui uma oferta pelo ID',
        'type'        => 'write',
        'ajax'        => true,
        'capabilities' => 'local/offermanager:manage',
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_set_status' => [
        'classname'   => 'local_offermanager\external\offer_external',
        'methodname'  => 'set_status',
        'description' => 'Altera o status de uma oferta',
        'type'        => 'write',
        'ajax'        => true,
        'capabilities' => 'local/offermanager:manage',
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_get_audiences' => [
        'classname' => 'local_offermanager\external\offer_audience_external',
        'methodname' => 'get_audiences',
        'classpath' => '',
        'description' => 'Retorna todos os públicos-alvo e os IDs dos públicos-alvo atuais da oferta.',
        'type' => 'read',
        'ajax' => true,
        'loginrequired' => true,
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_update_audiences' => [
        'classname' => 'local_offermanager\external\offer_audience_external',
        'methodname' => 'update_audiences',
        'classpath' => '',
        'description' => 'Atualiza os públicos-alvo de uma oferta.',
        'type' => 'write',
        'ajax' => true,
        'capabilities' => 'local/offermanager:manage',
        'loginrequired' => true,
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_get_categories' => [
        'classname' => 'local_offermanager\external\offer_course_external',
        'methodname' => 'get_categories',
        'classpath' => '',
        'description' => 'Retorna todos os cursos que potencialmente podem ser adicionados a oferta.',
        'type' => 'read',
        'ajax' => true,
        'loginrequired' => true,
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_fetch_current_courses' => [
        'classname' => 'local_offermanager\external\offer_course_external',
        'methodname' => 'fetch_current_courses',
        'classpath' => '',
        'description' => 'Retorna os cursos de uma oferta, baseados nos parâmetros de entrada.',
        'type' => 'read',
        'ajax' => true,
        'loginrequired' => true,
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_get_current_courses' => [
        'classname' => 'local_offermanager\external\offer_course_external',
        'methodname' => 'get_current_courses',
        'classpath' => '',
        'description' => 'Retorna os dados dos cursos de uma oferta.',
        'type' => 'read',
        'ajax' => true,
        'loginrequired' => true,
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_fetch_potential_courses' => [
        'classname' => 'local_offermanager\external\offer_course_external',
        'methodname' => 'fetch_potential_courses',
        'classpath' => '',
        'description' => 'Retorna todos os cursos que potencialmente podem ser adicionados a oferta.',
        'type' => 'read',
        'ajax' => true,
        'loginrequired' => true,
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
        'local_offermanager_get_course' => [
        'classname' => 'local_offermanager\external\offer_course_external',
        'methodname' => 'get',
        'classpath' => '',
        'description' => 'Retorna dados do cursos da oferta.',
        'type' => 'read',
        'ajax' => true,
        'loginrequired' => true,
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_add_courses' => [
        'classname' => 'local_offermanager\external\offer_course_external',
        'methodname' => 'add',
        'classpath' => '',
        'description' => 'Adiciona cursos a uma oferta.',
        'type' => 'write',
        'ajax' => true,
        'capabilities' => 'local/offermanager:manage',
        'loginrequired' => true,
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_delete_course' => [
        'classname' => 'local_offermanager\external\offer_course_external',
        'methodname' => 'delete',
        'classpath' => '',
        'description' => 'Remove um curso da uma oferta.',
        'type' => 'write',
        'ajax' => true,
        'capabilities' => 'local/offermanager:manage',
        'loginrequired' => true,
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_set_course_status' => [
        'classname'   => 'local_offermanager\external\offer_course_external',
        'methodname'  => 'set_status',
        'description' => 'Altera o status de uma oferta',
        'type'        => 'write',
        'ajax'        => true,
        'capabilities' => 'local/offermanager:manage',
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_get_class_methods' => [
        'classname'   => 'local_offermanager\external\offer_external',
        'methodname'  => 'get_class_methods',
        'description' => 'Retorna os métodos de inscrição disponíveis para criar turmas',
        'type'        => 'read',
        'ajax'        => true,
        'capabilities' => '',
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_get_classes' => [
        'classname' => 'local_offermanager\external\offer_course_external',
        'methodname' => 'get_classes',
        'classpath' => '',
        'description' => 'Retorna as turmas de um curso específico de uma oferta.',
        'type' => 'read',
        'ajax' => true,
        'loginrequired' => true,
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_add_class' => [
        'classname'   => 'local_offermanager\external\offer_class_external',
        'methodname'  => 'add',
        'description' => 'Cria uma instância de inscrição e sua relação para formar a turma',
        'type'        => 'write',
        'ajax'        => true,
        'capabilities' => 'local/offermanager:manage',
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_update_class' => [
        'classname' => 'local_offermanager\external\offer_class_external',
        'methodname' => 'update',
        'description' => 'Atualiza os dados de uma turma de oferta.',
        'type'        => 'write',
        'ajax'        => true,
        'capabilities' => 'local/offermanager:manage',
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_delete_class' => [
        'classname' => 'local_offermanager\external\offer_class_external',
        'methodname' => 'delete',
        'description' => 'Exclúi os dados de uma turma de oferta.',
        'type'        => 'write',
        'ajax'        => true,
        'capabilities' => 'local/offermanager:manage',
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_get_class' => [
        'classname' => 'local_offermanager\external\offer_class_external',
        'methodname' => 'get',
        'description' => 'Retorna os dados de uma turma de oferta.',
        'type'        => 'read',
        'ajax'        => true,
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_get_duplication_courses' => [
        'classname' => 'local_offermanager\external\offer_class_external',
        'methodname' => 'get_potential_duplication_courses',
        'description' => 'Retorna a lista de cursos que uma turma pode ser duplicado.',
        'type'        => 'read',
        'ajax'        => true,
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_duplicate_class' => [
        'classname' => 'local_offermanager\external\offer_class_external',
        'methodname' => 'duplicate',
        'description' => 'Duplica uma turma em um curso específico.',
        'type'        => 'write',
        'ajax'        => true,
        'capabilities' => 'local/offermanager:manage',
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_get_potential_teachers' => [
        'classname' => 'local_offermanager\external\offer_class_external',
        'methodname' => 'get_potential_teachers',
        'description' => 'Retorna a lista dos potenciais professores para uma turma de oferta.',
        'type'        => 'read',
        'ajax'        => true,
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_set_class_status' => [
        'classname'   => 'local_offermanager\external\offer_class_external',
        'methodname'  => 'set_status',
        'description' => 'Altera o status de uma turma',
        'type'        => 'write',
        'ajax'        => true,
        'capabilities' => 'local/offermanager:manage',
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_get_enroled_users' => [
        'classname' => 'local_offermanager\external\offer_class_external',
        'methodname' => 'get_enroled_users',
        'description' => 'Retorna a lista filtrada de usuários matriculados em uma turma.',
        'type'        => 'read',
        'ajax'        => true,
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_fetch_enrolments' => [
        'classname' => 'local_offermanager\external\offer_class_external',
        'methodname' => 'fetch_enrolments',
        'description' => 'Retorna os dados de matrículas em uma turma para construir a tabela.',
        'type'        => 'read',
        'ajax'        => true,
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_enrol_users' => [
        'classname'   => 'local_offermanager\external\offer_class_external',
        'methodname'  => 'enrol_users',
        'description' => 'Inscreve os usuários em uma determinada turma',
        'type'        => 'write',
        'ajax'        => true,
        'capabilities' => 'local/offermanager:manage',
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_edit_offer_user_enrol' => [
        'classname'   => 'local_offermanager\external\offer_user_enrol_external',
        'methodname'  => 'edit_enrolment',
        'description' => 'Edita a inscrição do usuário',
        'type'        => 'write',
        'ajax'        => true,
        'capabilities' => 'local/offermanager:manage',
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_edit_offer_user_enrol_bulk' => [
        'classname'   => 'local_offermanager\external\offer_user_enrol_external',
        'methodname'  => 'edit_enrolment_bulk',
        'description' => 'Edita em lote inscrições de usuário',
        'type'        => 'write',
        'ajax'        => true,
        'capabilities' => 'local/offermanager:manage',
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_delete_offer_user_enrol' => [
        'classname'   => 'local_offermanager\external\offer_user_enrol_external',
        'methodname'  => 'delete_enrolment',
        'description' => 'Exclui a inscrição do usuário',
        'type'        => 'write',
        'ajax'        => true,
        'capabilities' => 'local/offermanager:manage',
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_delete_offer_user_enrol_bulk' => [
        'classname'   => 'local_offermanager\external\offer_user_enrol_external',
        'methodname'  => 'delete_enrolment_bulk',
        'description' => 'Exclui em lote inscrições de usuário',
        'type'        => 'write',
        'ajax'        => true,
        'capabilities' => 'local/offermanager:manage',
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_get_situation_list' => [
        'classname'   => 'local_offermanager\external\offer_user_enrol_external',
        'methodname'  => 'get_situation_list',
        'description' => 'Retorna a lista de situações de inscrição possíveis.',
        'type'        => 'read',
        'ajax'        => true,
        'capabilities' => '',
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_get_course_roles' => [
        'classname'   => 'local_offermanager\external\offer_course_external',
        'methodname'  => 'get_course_roles',
        'description' => 'Retorna a lista de papéis disponíveis no curso associado à relação oferta-curso.',
        'type'        => 'read',
        'ajax'        => true,
        'capabilities' => '',
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_get_filter_options' => [
        'classname'   => 'local_offermanager\external\offer_class_external',
        'methodname'  => 'get_filter_options',
        'description' => 'Retorna opções para os filtros de usuários matriculados em uma turma.',
        'type'        => 'read',
        'ajax'        => true,
        'capabilities' => '',
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_get_potential_users_to_enrol' => [
        'classname'   => 'local_offermanager\external\offer_class_external',
        'methodname'  => 'get_potential_users_to_enrol',
        'description' => 'Retorna lista de usuários potenciais para matrícula em uma turma.',
        'type'        => 'read',
        'ajax'        => true,
        'capabilities' => '',
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_get_all_roles' => [
        'classname'   => 'local_offermanager\external\offer_class_external',
        'methodname'  => 'get_all_roles',
        'description' => 'Retorna todos os papéis disponíveis no sistema.',
        'type'        => 'read',
        'ajax'        => true,
        'capabilities' => '',
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_get_roles' => [
        'classname' => 'local_offermanager\external\offer_user_enrol_external',
        'methodname' => 'get_roles',
        'classpath' => '',
        'description' => 'Retorna os papéis formatados para a inscrição do usuário na turma.',
        'type'        => 'read',
        'ajax'        => true,
        'capabilities' => '',
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_update_roles' => [
        'classname' => 'local_offermanager\external\offer_user_enrol_external',
        'methodname' => 'update_roles',
        'classpath' => '',
        'description' => 'Atualiza os papéis de um usuário em uma turma.',
        'type'        => 'write',
        'ajax'        => true,
        'capabilities' => 'local/offermanager:manage',
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
    'local_offermanager_cancel_enrolment' => [
        'classname' => 'local_offermanager\external\offer_user_enrol_external',
        'methodname' => 'cancel',
        'classpath' => '',
        'description' => 'Cancela a inscrição de um usuário em uma turma.',
        'type'        => 'write',
        'ajax'        => true,
        'capabilities' => 'local/offermanager:manage',
        'services'    => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
];

$services = [
    'Offer Manager Service' => [
        'functions' => [
            'local_offermanager_fetch',
            'local_offermanager_get',
            'local_offermanager_get_types',
            'local_offermanager_get_type_options',
            'local_offermanager_save',
            'local_offermanager_delete',
            'local_offermanager_set_status',
            'local_offermanager_get_audiences',
            'local_offermanager_update_audiences',
            'local_offermanager_get_categories',
            'local_offermanager_fetch_current_courses',
            'local_offermanager_fetch_potential_courses',
            'local_offermanager_get_current_courses',
            'local_offermanager_add_courses',
            'local_offermanager_get_course',
            'local_offermanager_delete_course',
            'local_offermanager_set_course_status',
            'local_offermanager_get_class_methods',
            'local_offermanager_get_classes',
            'local_offermanager_get_class',
            'local_offermanager_add_class',
            'local_offermanager_update_class',
            'local_offermanager_delete_class',
            'local_offermanager_duplicate_class',
            'local_offermanager_get_potential_teachers',
            'local_offermanager_enrol_users',
            'local_offermanager_edit_offer_user_enrol',
            'local_offermanager_edit_offer_user_enrol_bulk',
            'local_offermanager_delete_offer_user_enrol',
            'local_offermanager_delete_offer_user_enrol_bulk',
            'local_offermanager_set_class_status',
            'local_offermanager_get_enroled_users',
            'local_offermanager_fetch_enrolments',
            'local_offermanager_get_potential_duplication_courses',
            'local_offermanager_get_situation_list',
            'local_offermanager_get_course_roles',
            'local_offermanager_get_filter_options',
            'local_offermanager_get_potential_users_to_enrol',
            'local_offermanager_get_all_roles',
            'local_offermanager_get_roles',
            'local_offermanager_update_roles',
            'local_offermanager_cancel_enrolment',
        ],
        'restrictedusers' => 0,
        'enabled' => 1,
    ],
];
