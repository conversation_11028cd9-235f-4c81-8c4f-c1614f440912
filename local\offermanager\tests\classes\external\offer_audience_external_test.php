<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\classes\external;

use advanced_testcase;
use core_external\external_api;
use local_offermanager\external\offer_audience_external;
use local_offermanager\persistent\offer_model;
use local_offermanager\persistent\offer_audience_model;
use local_offermanager\constants;
use moodle_exception;

/**
 * Tests for Offer Manager
 *
 * @package    local_offermanager
 * @category   test
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
final class offer_audience_external_test extends \advanced_testcase {
    
    /**
     * Testa o método get_audiences.
     */
    public function test_get_audiences(): void
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta de Teste'
        ]);
        $offer->save();

        $audienceid_1 = $DB->insert_record('local_audience_audiences', [
            'name' => 'Público-alvo 1',
        ]);
        $audienceid_2 = $DB->insert_record('local_audience_audiences', [
            'name' => 'Público-alvo 2',
        ]);

        $offer->add_audience($audienceid_1);

        $result = offer_audience_external::get_audiences($offer->get('id'));

        $result = external_api::clean_returnvalue(
            offer_audience_external::get_audiences_returns(),
            $result
        );

        $this->assertArrayHasKey('all_audiences', $result);
        $this->assertArrayHasKey('current_audience_ids', $result);

        $this->assertCount(2, $result['all_audiences']);
        $this->assertEquals($audienceid_1, $result['all_audiences'][0]['id']);
        $this->assertEquals('Público-alvo 1', $result['all_audiences'][0]['name']);
        $this->assertEquals($audienceid_2, $result['all_audiences'][1]['id']);
        $this->assertEquals('Público-alvo 2', $result['all_audiences'][1]['name']);

        $this->assertCount(1, $result['current_audience_ids']);
        $this->assertContains($audienceid_1, $result['current_audience_ids']);
    }

    /**
     * Testa o método get_audiences com uma oferta inexistente.
     */
    public function test_get_audiences_offer_not_found(): void
    {
        $this->resetAfterTest(true);

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:offer_not_found',' local_offermanager'));
        offer_audience_external::get_audiences(999); // ID inválido.
    }

    /**
     * Testa o método update_audiences.
     */
    public function test_update_audiences(): void
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta de Teste'
        ]);
        $offer->save();

        $audienceid_1 = $DB->insert_record('local_audience_audiences', [
            'name' => 'Público-alvo 1',
        ]);
        $audienceid_2 = $DB->insert_record('local_audience_audiences', [
            'name' => 'Público-alvo 2',
        ]);

        $offer->add_audience($audienceid_1);

        $audienceIds = $offer->get_audience_ids();
        $this->assertCount(1, $audienceIds);
        $this->assertContains($audienceid_1, $audienceIds);
        $this->assertNotContains($audienceid_2, $audienceIds);

        $result = offer_audience_external::update_audiences($offer->get('id'), [$audienceid_2]);

        $result = external_api::clean_returnvalue(
            offer_audience_external::update_audiences_returns(),
            $result
        );

        $this->assertTrue($result);

        $updatedAudienceIds = $offer->get_audience_ids();
        $this->assertCount(1, $updatedAudienceIds);
        $this->assertNotContains($audienceid_1, $updatedAudienceIds);
        $this->assertContains($audienceid_2, $updatedAudienceIds);
    }

    /**
     * Testa o método update_audiences com uma oferta inexistente.
     */
    public function test_update_audiences_offer_not_found(): void
    {
        $this->resetAfterTest(true);

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:offer_not_found',' local_offermanager'));
        offer_audience_external::update_audiences(999, [1]);
    }

    /**
     * Testa o método update_audiences sem alterações.
     */
    public function test_update_audiences_no_changes(): void
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta de Teste'
        ]);
        $offer->save();

        $audienceid_1 = $DB->insert_record('local_audience_audiences', [
            'name' => 'Público-alvo 1',
        ]);

        $offer->add_audience($audienceid_1);

        $result = offer_audience_external::update_audiences($offer->get('id'), [$audienceid_1]);

        $result = external_api::clean_returnvalue(
            offer_audience_external::update_audiences_returns(),
            $result
        );

        $this->assertFalse($result);

        $updatedAudienceIds = $offer->get_audience_ids();
        $this->assertCount(1, $updatedAudienceIds);
        $this->assertContains($audienceid_1, $updatedAudienceIds);
    }
}
