<template>
  <modal
    :show="show"
    :title="title"
    :confirm-button-text="confirmButtonText"
    :cancel-button-text="cancelButtonText"
    :confirm-disabled="confirmDisabled"
    :size="size"
    @close="$emit('close')"
    @confirm="handleConfirm"
  >
    <form @submit.prevent="handleConfirm" class="form-container">
      <slot></slot>
    </form>
  </modal>
</template>

<script>
import Modal from './Modal.vue';

export default {
  name: 'FormModal',
  components: {
    Modal
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: 'Formulário'
    },
    confirmButtonText: {
      type: String,
      default: 'Salvar'
    },
    cancelButtonText: {
      type: String,
      default: 'Cancelar'
    },
    confirmDisabled: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'md'
    }
  },
  emits: ['close', 'confirm'],
  methods: {
    handleConfirm() {
      this.$emit('confirm');
    }
  }
}
</script>

<style lang="scss" scoped>
.form-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
</style> 