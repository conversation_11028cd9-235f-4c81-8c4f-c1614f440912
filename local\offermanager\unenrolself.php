<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

require_once(__DIR__ . '/../../config.php');
require_once($CFG->dirroot . '/local/offermanager/lib.php'); // Includes the form class definition

use local_offermanager\persistent\offer_user_enrol_model;

$id = required_param('id', PARAM_INT); 

require_login();
if (isgues<PERSON>er()) {
    redirect(new moodle_url('/my/'));
}

global $DB, $USER, $PAGE, $OUTPUT;

$offeruserenrol = offer_user_enrol_model::get_record(['id' => $id, 'userid' => $USER->id]);

if (!$offeruserenrol || !$offeruserenrol->is_active()) {
    throw new \moodle_exception('error:invalid_offer_user_enrol_for_cancel', 'local_offermanager');
}

$offerclass = $offeruserenrol->get_offer_class();

if (!$offerclass) {
     throw new \moodle_exception('error:offer_class_not_found', 'local_offermanager');
}

$context = \context_offer_class::instance($offerclass->get('id'));

$pluginname = $offerclass->get('enrol');
require_capability("enrol/{$pluginname}:unenrolself", $context);

$url = new moodle_url('/local/offermanager/unenrolself.php', ['id' => $id]);
$classname = $offerclass->get_name();
$PAGE->set_url($url);
$PAGE->set_context($context);
$PAGE->set_pagelayout('incourse');
$PAGE->set_title(get_string('page:self_unenrol_title', 'local_offermanager'));
$PAGE->set_heading(get_string('page:self_unenrol_heading', 'local_offermanager', $classname));
$PAGE->navbar->add(get_string('page:self_unenrol_heading', 'local_offermanager', $classname), new moodle_url($url));

$form = new \local_offermanager_self_unenrol_form(null, ['id' => $id, 'userid' => $USER->id]);

if ($form->is_cancelled()) {
    redirect(new moodle_url('/my/courses.php'));
} else if ($data = $form->get_data()) {
    $reason = $data->reason['text'] ?? null;

    $success = $offeruserenrol->set_canceled($reason);

    if ($success) {
        redirect(new moodle_url('/'), get_string('message:self_unenrol_success', 'local_offermanager'), \core\output\notification::NOTIFY_SUCCESS);
    } else {
        redirect($url, get_string('error:self_unenrol_failed', 'local_offermanager'), \core\output\notification::NOTIFY_ERROR);
    }

} else {

    $formdata = new stdClass();
    $formdata->id = $id;
    $formdata->userid = $USER->id;
    $form->set_data($formdata);
    $form->add_action_buttons(true, get_string('confirm'));
    echo $OUTPUT->header();
    $form->display();
    echo $OUTPUT->footer();
}