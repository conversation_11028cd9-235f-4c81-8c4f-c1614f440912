<template>
  <div class="role-selector">
    <!-- Display mode: show comma-separated role names -->
    <div v-if="!isEditing" class="role-display" @click.stop="startEditing">
      <span>{{ displayRoleNames }}</span>
      <i class="fas fa-pencil-alt edit-icon" aria-hidden="true"></i>
    </div>

    <!-- Edit mode: multi-select -->
    <div v-else class="role-edit-wrapper">
      <div class="role-edit-container">
        <div class="select-wrapper">
          <select
            v-model="selectedRoles"
            class="role-select"
            ref="roleSelect"
            multiple
            @click.stop
            :style="{ height: Math.max(4, roles.length) * 25 + 'px' }"
          >
            <option v-for="role in roles" :key="role.id" :value="role.id">
              {{ role.name }}
            </option>
          </select>
        </div>
        <div class="role-actions">
          <button class="btn-save" @click.stop="saveRoles" title="Salvar">
            <i class="fas fa-check"></i>
          </button>
          <button class="btn-cancel" @click.stop="cancelEdit" title="Cancelar">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Loading overlay apenas durante edição/salvamento -->
    <div v-if="loading && isEditing" class="loading-overlay">
      <div class="spinner"></div>
    </div>
  </div>
</template>

<script>
import { getClass } from "@/services/offer";
import { getCourseRoles } from "@/services/offer";
import { getUserRoles, updateUserRoles } from "@/services/enrolment";

export default {
  name: "RoleSelector",
  props: {
    userId: { type: [Number, String], required: true },
    offeruserenrolid: { type: [Number, String], required: true },
    currentRole: { type: [String, Array], required: true },
    offerclassid: { type: [Number, String], required: true },
  },
  data() {
    return {
      isEditing: false,
      selectedRoles: [],
      roles: [],
      loading: false,
      initialLoading: true,
    };
  },
  computed: {
    displayRoleNames() {
      if (Array.isArray(this.currentRole)) {
        return this.currentRole.join(", ");
      }
      return String(this.currentRole || "");
    },
  },
  mounted() {
    this.loadRoles();
  },
  methods: {
    async loadRoles() {
      // Não mostrar loading durante carregamento inicial
      if (!this.initialLoading) {
        this.loading = true;
      }
      try {
        const classDetails = await getClass(parseInt(this.offerclassid));
        const offercourseid =
          classDetails?.data?.offercourseid || classDetails.offercourseid;

        if (!offercourseid) throw new Error("offercourseid não encontrado");

        const courseRoles = await getCourseRoles(offercourseid);

        this.roles = Array.isArray(courseRoles?.data)
          ? courseRoles.data
          : Array.isArray(courseRoles)
            ? courseRoles
            : [];

        // Initialize selectedRoles
        const userRoles = await getUserRoles(this.offeruserenrolid);

        if (Array.isArray(userRoles) && userRoles.length) {
          this.selectedRoles = userRoles.map((r) => r.id);
        } else if (Array.isArray(this.currentRole)) {
          this.selectedRoles = this.roles
            .filter((r) => this.currentRole.includes(r.name))
            .map((r) => r.id);
        } else if (this.currentRole) {
          const match = this.roles.find(
            (r) =>
              r.name.toLowerCase() === String(this.currentRole).toLowerCase()
          );

          if (match) this.selectedRoles = [match.id];
        }
      } catch (e) {
        this.$emit("error", "Não foi possível carregar papéis.");
      } finally {
        this.loading = false;
        this.initialLoading = false;
      }
    },
    startEditing() {
      this.isEditing = true;
      this.$nextTick(() => this.$refs.roleSelect?.focus());
    },
    cancelEdit() {
      this.isEditing = false;
    },
    close() {
      if (this.isEditing) this.isEditing = false;
    },
    async saveRoles() {
      if (!this.selectedRoles.length) {
        this.$emit("error", "Selecione ao menos um papel.");
        return;
      }
      this.loading = true;
      try {
        const success = await updateUserRoles(
          this.offeruserenrolid,
          this.selectedRoles.map((id) => parseInt(id))
        );

        // Verificar diferentes formatos de resposta de sucesso
        if (
          success === true ||
          (success && success.error === false) ||
          (success && success.success === true)
        ) {
          // emit role names
          const names = this.roles
            .filter((r) => this.selectedRoles.includes(r.id))
            .map((r) => r.name);

          // Emitir evento de sucesso com os novos papéis
          this.$emit("success", {
            userId: this.userId,
            offeruserenrolid: this.offeruserenrolid,
            roleids: this.selectedRoles,
            roleNames: names,
          });

          this.isEditing = false;

          // Recarregar a tabela após salvar os papéis
          this.$emit("reload-table");
        } else {
          throw new Error(
            "Resposta inesperada do servidor: " + JSON.stringify(success)
          );
        }
      } catch (error) {
        console.error("Erro ao salvar papéis:", error);
        this.$emit("error", "Não foi possível salvar papéis.");
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.role-selector {
  position: relative;
  width: 100%;
  min-height: 24px;
  display: flex;
  justify-content: center;
}

.role-display {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 0;
}

.edit-icon {
  margin-left: 6px;
  color: #007bff;
}

.role-edit-wrapper {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.select-wrapper {
  flex: 1;
  position: relative;
}

.role-select {
  width: 100%;
  min-width: 190px;
  padding: 8px;
  border-radius: 4px;
  background: #2c3034;
  color: #fff;
  border: 1px solid #007bff;
}

.role-select:focus {
  outline: none;
}

.role-actions {
  display: flex;
  gap: 8px;
}

.btn-save,
.btn-cancel {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  border: 1px solid;
  background: rgba(255, 255, 255, 0.1);
}

.btn-save {
  border-color: #28a745;
  color: #28a745;
}

.btn-cancel {
  border-color: #dc3545;
  color: #dc3545;
}

.loading-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
