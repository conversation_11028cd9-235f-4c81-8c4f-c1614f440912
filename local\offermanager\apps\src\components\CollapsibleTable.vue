<template>
  <div class="table-responsive">
    <table class="table">
      <thead>
        <tr>
          <th v-if="expandable" class="expand-column"></th>
          <th
            v-for="header in headers"
            :key="header.value"
            @click="header.sortable ? handleSort(header.value) : null"
            :class="{ sortable: header.sortable }"
            :data-value="header.value"
          >
            {{ header.text }}
            <span v-if="header.sortable" class="sort-icon">
              <i
                class="fas"
                :class="{
                  'fa-sort': sortBy !== header.value,
                  'fa-sort-up': sortBy === header.value && !sortDesc,
                  'fa-sort-down': sortBy === header.value && sortDesc,
                }"
              ></i>
            </span>
          </th>
        </tr>
      </thead>
      <tbody v-if="items.length > 0">
        <template v-for="(item, index) in items" :key="item.id">
          <tr :class="{ expanded: expandedRows.includes(item.id) }">
            <td v-if="expandable" class="expand-column">
              <button
                class="btn-expand"
                @click="toggleExpand(item.id)"
                :title="
                  expandedRows.includes(item.id) ? 'Recolher' : 'Expandir'
                "
              >
                <div
                  class="icon-container"
                  :class="{ 'is-expanded': expandedRows.includes(item.id) }"
                >
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    class="expand-icon"
                  >
                    <rect
                      x="5"
                      y="11"
                      width="14"
                      height="2"
                      fill="var(--primary)"
                    />
                    <rect
                      x="11"
                      y="5"
                      width="2"
                      height="14"
                      fill="var(--primary)"
                      class="vertical-line"
                    />
                  </svg>
                </div>
              </button>
            </td>
            <td v-for="header in headers" :key="`${item.id}-${header.value}`">
              <slot :name="'item-' + header.value" :item="item">
                {{ item[header.value] }}
              </slot>
            </td>
          </tr>
          <tr
            v-if="expandable"
            class="expanded-row"
            :class="{ 'is-visible': expandedRows.includes(item.id) }"
          >
            <td :colspan="headers.length + 1">
              <div class="expanded-content">
                <slot name="expanded-content" :item="item"></slot>
              </div>
            </td>
          </tr>
        </template>
      </tbody>
      <tbody v-else>
        <tr>
          <td :colspan="headers.length + (expandable ? 1 : 0)">
            <slot name="empty-state">
              <div class="empty-state">
                <span>Não existem registros</span>
              </div>
            </slot>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
export default {
  name: "CollapsibleTable",

  props: {
    headers: {
      type: Array,
      required: true,
    },
    items: {
      type: Array,
      required: true,
    },
    sortBy: {
      type: String,
      default: "",
    },
    sortDesc: {
      type: Boolean,
      default: false,
    },
    expandable: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      expandedRows: [],
    };
  },

  methods: {
    handleSort(value) {
      this.$emit("sort", {
        sortBy: value,
        sortDesc: this.sortBy === value ? !this.sortDesc : false,
      });
    },

    toggleExpand(itemId) {
      const index = this.expandedRows.indexOf(itemId);
      if (index === -1) {
        this.expandedRows.push(itemId);
      } else {
        this.expandedRows.splice(index, 1);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.table-responsive {
  width: 100%;
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
  background-color: #212529 !important;

  thead {
    tr {
      border-bottom: 3px solid var(--primary) !important;
    }
  }

  th,
  td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #373b3e !important;
    color: #fff !important;
    height: 50px;
    white-space: nowrap;
    vertical-align: middle;
  }

  th {
    font-weight: 700;
    text-transform: uppercase;
    font-size: 16px;
    color: var(--primary) !important;
    cursor: pointer;
    border: none;

    &:nth-child(n + 3) {
      text-align: center;
    }

    .sort-icon {
      margin-left: 5px;
      color: #ced4da;
    }
  }

  td {
    font-size: 16px;
    font-weight: 400;
    &:nth-child(n + 3) {
      text-align: center;
    }
  }

  tbody {
    background-color: #212529;
    tr:not(.expanded-row) td:nth-child(2) {
      color: var(--primary) !important;
    }

    tr:not(.expanded-row):nth-child(4n+1) {
      background-color: rgba(255,255,255,.1);
    }
  }
}

.sortable {
  cursor: pointer;
}

.expand-column {
  width: 40px;
  text-align: center;
  padding: 0.5rem !important;
}

.btn-expand {
  background: transparent;
  border: none;
  color: #fff;
  cursor: pointer;
  padding: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    .expand-icon rect {
      fill: var(--primary);
    }
  }
}

.icon-container {
  position: relative;
  transition: transform 0.3s ease;

  &.is-expanded {
    .vertical-line {
      transform: scaleY(0);
    }
  }
}

.expand-icon {
  border: 1px solid var(--primary);
  border-radius: 2px;
  background-color: transparent;

  .vertical-line {
    transform-origin: center;
    transition: transform 0.3s ease;
  }
}

tr.expanded {
  border-bottom: solid 2px var(--primary);
}

.expanded-row {
  background-color: #343a40 !important;
  transition: visibility 0.3s ease;

  &:not(.is-visible) {
    display: table-row;
    visibility: collapse;

    .expanded-content {
      max-height: 0;
      padding-top: 0;
      padding-bottom: 0;
      margin-top: 0;
      margin-bottom: 0;
      overflow: hidden;
      opacity: 0;
      transform: translateY(-10px);
      transition:
        max-height 0.4s cubic-bezier(0.55, 0.085, 0.68, 0.53),
        opacity 0.3s ease,
        transform 0.3s ease,
        padding 0.3s ease;
    }
  }

  &.is-visible {
    display: table-row;
    visibility: visible;

    .expanded-content {
      max-height: 1000px;
      transition:
        max-height 0.4s cubic-bezier(0.215, 0.61, 0.355, 1),
        opacity 0.3s ease 0.1s,
        transform 0.3s ease 0.1s;
    }
  }

  td {
    padding: 0 !important;
    border-bottom: 1px solid #373b3e !important;
    transition: padding 0.3s ease;
  }
}

.expanded-content {
  padding: 0;
  opacity: 0;
  transform: translateY(-10px);
  transition:
    max-height 0.3s cubic-bezier(0.55, 0.055, 0.675, 0.19),
    opacity 0.2s ease,
    transform 0.2s ease,
    padding 0.3s ease;

  .is-visible & {
    opacity: 1;
    transform: translateY(0);
  }
}

.empty-state {
  padding: 2rem;
  text-align: center;

  span {
    font-size: 0.875rem;
    color: #6c757d;
  }
}
</style>
