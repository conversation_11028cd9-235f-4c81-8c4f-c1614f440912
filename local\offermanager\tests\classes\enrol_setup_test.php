<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\classes;

use advanced_testcase;
use local_offermanager\enrol_setup;
use dml_exception;
use local_offermanager\constants;
use core\plugininfo\enrol;

/**
 * Tests for Offer Manager
 *
 * @package    local_offermanager
 * @category   test
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
final class enrol_setup_test extends advanced_testcase
{
    public function test_disable_defaultenrol_for_other_plugins()
    {
        $this->resetAfterTest(true);

        $this->assertEquals(1, get_config('enrol_manual',  'defaultenrol'));
        $this->assertEquals(1, get_config('enrol_self',  'defaultenrol'));
        $this->assertEquals(0, get_config('enrol_offer_automatic',  'defaultenrol'));

        enrol_setup::disable_defaultenrol_for_other_plugins();

        $this->assertEquals(0, get_config('enrol_manual',  'defaultenrol'));
        $this->assertEquals(0, get_config('enrol_self',  'defaultenrol'));
        $this->assertEquals(0, get_config('enrol_offer_automatic',  'defaultenrol'));
    }

    public function test_disable_enrol_plugins()
    {
        $this->resetAfterTest(true);

        $enabled = enrol_get_plugins(true);

        $dependent_enrol_plugins = enrol_setup::get_dependent_enrol_plugins();

        $this->assertTrue(count($dependent_enrol_plugins) <=  count($enabled));

        enrol_setup::disable_enrol_plugins();

        $enabled = enrol_get_plugins(true);

        $this->assertTrue(count($dependent_enrol_plugins) >=  count($enabled));
    }

    public function test_get_dependent_enrol_plugins()
    {
        $this->resetAfterTest(true);

        $result = enrol_setup::get_dependent_enrol_plugins();

        $this->assertNotEmpty($result);

        $this->assertCount(3, $result);

        $result = enrol_setup::get_dependent_enrol_plugins(false);

        $this->assertNotEmpty($result);

        $this->assertCount(3, $result);

        enrol::enable_plugin('offer_automatic', 0);
        enrol::enable_plugin('offer_self', 0);
        enrol::enable_plugin('offer_manual', 0);

        $result = enrol_setup::get_dependent_enrol_plugins();

        $this->assertEmpty($result);

        $result = enrol_setup::get_dependent_enrol_plugins(false);

        $this->assertNotEmpty($result);

        $this->assertCount(3, $result);
    }

    public function test_has_enrol_plugins_to_disable()
    {
        $this->resetAfterTest(true);

        $result = enrol_setup::has_enrol_plugins_to_disable();
        $this->assertTrue($result);
    }

    public function test_handle_enrol_plugins()
    {
        $this->resetAfterTest(true);

        set_config('enableplugin', 1, 'local_offermanager');
        set_config('enableplatformenrol', 0, 'local_offermanager');

        $pluginname = 'enrol_manual';

        enrol_setup::handle_enrol_plugins();

        $pluginmanager = \core_plugin_manager::instance();
        $plugin = $pluginmanager->get_plugin_info($pluginname);
        $this->assertFalse($plugin->is_enabled());
    }

    public function test_handle_disable_plugins()
    {
        $this->resetAfterTest(true);

        set_config('enableplugin', 1, 'local_offermanager');
        set_config('enableplatformenrol', 0, 'local_offermanager');

        $pluginname = 'enrol_manual';
        set_config('defaultenrol', 1, $pluginname);

        enrol_setup::handle_disable_plugins();

        $pluginmanager = \core_plugin_manager::instance();
        $plugin = $pluginmanager->get_plugin_info($pluginname);
        $this->assertFalse($plugin->is_enabled());
    }
}
