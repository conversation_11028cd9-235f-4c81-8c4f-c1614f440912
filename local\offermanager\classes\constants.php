<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mood<PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager;

defined('MOODLE_INTERNAL') || die();

/**
 * Class constants
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
abstract class constants
{
    const OFFER_STATUS_INACTIVE = 0;
    const OFFER_STATUS_ACTIVE = 1;

    const OFFER_CLASS_OPERATIONAL_CYCLE_NOT_STARTED = 0;
    const OFFER_CLASS_OPERATIONAL_CYCLE_STARTED = 1;
    const OFFER_CLASS_OPERATIONAL_CYCLE_FINISHED = 2;

    const OFFER_CLASS_NOT_ACCESSIBLE = 0;
    const OFFER_CLASS_ACCESSIBLE = 1;

    const OFFER_USER_ENROL_SITUATION_ENROLED = 0;
    const OFFER_USER_ENROL_SITUATION_IN_PROGRESS = 1;
    const OFFER_USER_ENROL_SITUATION_APPROVED = 2;
    const OFFER_USER_ENROL_SITUATION_COMPLETED = 3;
    const OFFER_USER_ENROL_SITUATION_USER_CANCELED = 4;
    const OFFER_USER_ENROL_SITUATION_ADMIN_CANCELED = 5;
    const OFFER_USER_ENROL_SITUATION_FAILED = 6;
    const OFFER_USER_ENROL_SITUATION_NOT_COMPLETED = 7;
    const OFFER_USER_ENROL_SITUATION_ABANDONED = 8;

    const OFFER_NAME_DEFAULT = '';
    const OFFER_DESCRIPTION_DEFAULT = '';
    const OFFER_TYPE_DEFAULT = '';
    const OFFER_STATUS_DEFAULT = 0;
    const OFFER_TEACHER_ROLE_DEFAULT = 'teacher';

    const ALLOWED_SITUATION_TRANSITIONS = [
        self::OFFER_USER_ENROL_SITUATION_ENROLED => [
            self::OFFER_USER_ENROL_SITUATION_IN_PROGRESS,
            self::OFFER_USER_ENROL_SITUATION_APPROVED,
            self::OFFER_USER_ENROL_SITUATION_COMPLETED,
            self::OFFER_USER_ENROL_SITUATION_FAILED,
            self::OFFER_USER_ENROL_SITUATION_NOT_COMPLETED,
            self::OFFER_USER_ENROL_SITUATION_ABANDONED,
            self::OFFER_USER_ENROL_SITUATION_USER_CANCELED,
            self::OFFER_USER_ENROL_SITUATION_ADMIN_CANCELED,
        ],
        self::OFFER_USER_ENROL_SITUATION_IN_PROGRESS => [
            self::OFFER_USER_ENROL_SITUATION_APPROVED,
            self::OFFER_USER_ENROL_SITUATION_COMPLETED,
            self::OFFER_USER_ENROL_SITUATION_FAILED,
            self::OFFER_USER_ENROL_SITUATION_NOT_COMPLETED,
            self::OFFER_USER_ENROL_SITUATION_ABANDONED,
            self::OFFER_USER_ENROL_SITUATION_USER_CANCELED,
            self::OFFER_USER_ENROL_SITUATION_ADMIN_CANCELED,
        ],
        self::OFFER_USER_ENROL_SITUATION_COMPLETED => [
            // Geralmente, COMPLETED é um estado final
        ],
        self::OFFER_USER_ENROL_SITUATION_USER_CANCELED => [
            // Cancelamento pelo usuário geralmente é final
        ],
        self::OFFER_USER_ENROL_SITUATION_ADMIN_CANCELED => [
            // Cancelamento pelo admin geralmente é final
        ],
        self::OFFER_USER_ENROL_SITUATION_FAILED => [
            // FAILED geralmente é um estado final
        ],
        self::OFFER_USER_ENROL_SITUATION_NOT_COMPLETED => [
            // NOT_COMPLETED geralmente é um estado final
        ],
        self::OFFER_USER_ENROL_SITUATION_ABANDONED => [
            // ABANDONED geralmente é um estado final
        ],
    ];

    public static function get_situations()
    {
        return [
            self::OFFER_USER_ENROL_SITUATION_ENROLED,
            self::OFFER_USER_ENROL_SITUATION_IN_PROGRESS,
            self::OFFER_USER_ENROL_SITUATION_APPROVED,
            self::OFFER_USER_ENROL_SITUATION_COMPLETED,
            self::OFFER_USER_ENROL_SITUATION_USER_CANCELED,
            self::OFFER_USER_ENROL_SITUATION_ADMIN_CANCELED,
            self::OFFER_USER_ENROL_SITUATION_FAILED,
            self::OFFER_USER_ENROL_SITUATION_NOT_COMPLETED,
            self::OFFER_USER_ENROL_SITUATION_ABANDONED
        ];
    }

    public static function get_situation_list()
    {
        return [
            self::OFFER_USER_ENROL_SITUATION_ENROLED => get_string('situation:enroled', 'local_offermanager'),
            self::OFFER_USER_ENROL_SITUATION_IN_PROGRESS => get_string('situation:in_progress', 'local_offermanager'),
            self::OFFER_USER_ENROL_SITUATION_APPROVED => get_string('situation:approved', 'local_offermanager'),
            self::OFFER_USER_ENROL_SITUATION_COMPLETED => get_string('situation:completed', 'local_offermanager'),
            self::OFFER_USER_ENROL_SITUATION_USER_CANCELED => get_string('situation:user_canceled', 'local_offermanager'),
            self::OFFER_USER_ENROL_SITUATION_ADMIN_CANCELED => get_string('situation:admin_canceled', 'local_offermanager'),
            self::OFFER_USER_ENROL_SITUATION_FAILED => get_string('situation:failed', 'local_offermanager'),
            self::OFFER_USER_ENROL_SITUATION_NOT_COMPLETED => get_string('situation:not_completed', 'local_offermanager'),
            self::OFFER_USER_ENROL_SITUATION_ABANDONED => get_string('situation:abandoned', 'local_offermanager'),
        ];
    }

    public static function get_to_suspend_situations_list()
    {
        return [
            self::OFFER_USER_ENROL_SITUATION_APPROVED,
            self::OFFER_USER_ENROL_SITUATION_COMPLETED,
            self::OFFER_USER_ENROL_SITUATION_FAILED,
            self::OFFER_USER_ENROL_SITUATION_NOT_COMPLETED,
            self::OFFER_USER_ENROL_SITUATION_ABANDONED
        ];
    }
}
