<template>
  <Teleport to="body">
    <transition name="toast">
      <div v-if="show" class="toast" :class="type">
        <div class="toast-content">
          <i :class="icon"></i>
          <span>{{ message }}</span>
        </div>
        <div class="toast-progress" :style="progressStyle"></div>
      </div>
    </transition>
  </Teleport>
</template>

<script>
export default {
  name: 'Toast',
  props: {
    show: {
      type: Boolean,
      required: true
    },
    message: {
      type: String,
      required: true
    },
    type: {
      type: String,
      default: 'success',
      validator: function(value) {
        return ['success', 'error', 'warning', 'info'].includes(value)
      }
    },
    duration: {
      type: Number,
      default: 3000
    }
  },
  computed: {
    icon() {
      const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
      }
      return icons[this.type]
    },
    progressStyle() {
      return {
        animation: `progress ${this.duration}ms linear`
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.toast {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  position: fixed !important; // Adicionar !important para garantir
  z-index: 99999 !important; // Adicionar !important para garantir
  top: 20px !important; // Adicionar !important para garantir
  right: 20px !important; // Adicionar !important para garantir
  min-width: 300px;
  max-width: 400px;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  opacity: 1 !important; // Forçar opacidade
  transform: translateX(0) !important; // Forçar posição horizontal
  visibility: visible !important; // Forçar visibilidade

  &.success {
    background-color: #051b11;
    border: 1px solid #0f5132;
    color: #75b798;
  }

  &.error {
    background-color: #2c0b0e;
    border: 1px solid #842029;
    color: #ea868f;
  }

  &.warning {
    background-color: #332701;
    border: 1px solid #997404;
    color: #ffda6a;
  }

  &.info {
    background-color: #031633;
    border: 1px solid #084298;
    color: var(--primary);
  }
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;

  i {
    font-size: 1.25rem;
  }

  span {
    flex: 1;
    font-size: 0.875rem;
  }
}

.toast-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: currentColor;
  opacity: 0.2;
  transform-origin: left;
}

@keyframes progress {
  from {
    transform: scaleX(1);
  }
  to {
    transform: scaleX(0);
  }
}

// Animações de entrada e saída
.toast-enter-active, .toast-leave-active {
  transition: all 0.3s ease;
}

.toast-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.toast-leave-to {
  transform: translateX(100%);
  opacity: 0;
}
</style>