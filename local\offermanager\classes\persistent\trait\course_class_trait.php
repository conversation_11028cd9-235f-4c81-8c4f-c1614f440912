<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\persistent\trait;

use local_offermanager\persistent\offer_class_model;
use moodle_exception;

defined('MOODLE_INTERNAL') || die();

/**
 * Trait course_class_trait
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
trait course_class_trait
{
    /**
     * Adiciona uma Turma.
     *
     * @param int $enrolid ID da instância de inscrição.
     * @return offer_class_model
     */
    public function add_class($enrolid)
    {
        global $DB;

        $enrol_instance = $DB->get_record('enrol', ['id' => $enrolid]);

        if (!$enrol_instance) {
            throw new moodle_exception('error:enrolid_doesnt_exist', 'local_offermanager');
        }

        $offer_class = new offer_class_model(0, (object) [
            'offercourseid' => $this->get('id'),
            'enrol' => $enrol_instance->enrol,
            'enrolid' => $enrol_instance->id,
        ]);

        $offer_class->save();

        return $offer_class;
    }

    /**
     * Remove uma Turma.
     *
     * @param int $enrolid ID da instância de inscrição.
     * @return bool
     */
    public function remove_class($enrolid)
    {
        $offer_class = $this->get_enrol_class($enrolid);

        if (!$offer_class) {
            throw new moodle_exception('error:class_not_found', 'local_offermanager');
        }

        return $offer_class->delete();
    }

    /**
     * Retorna todas as turmas associadas a este registro.
     *
     * @return array
     */
    public function get_classes()
    {
        return offer_class_model::get_records(
            [
                'offercourseid' => $this->get('id')
            ],
            'id'
        );
    }

    /**
     * Conta o número de turmas associadas a este registro.
     *
     * @return int
     */
    public function count_classes()
    {
        return offer_class_model::count_records(
            [
                'offercourseid' => $this->get('id')
            ],
            'id'
        );
    }

    /**
     * Verifica se existe alguma turma associada a este registro.
     *
     * @return bool
     */
    public function has_classes(): bool
    {
        return offer_class_model::record_exists(
            [
                'offercourseid' => $this->get('id')
            ],
            'id'
        );
    }

    /**
     * Retorna a turma associada associadas a um enrolid.
     *
     * @param int $enrolid
     * @return offer_class_model
     */
    public function get_enrol_class(int $enrolid): offer_class_model
    {
        return offer_class_model::get_record([
            'offercourseid' => $this->get('id'),
            'enrolid' => $enrolid,
        ]);
    }
}
