<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\classes\persistent;

use enrol_offer_manual_plugin;
use local_offermanager\persistent\offer_model;
use moodle_exception;
use local_offermanager\constants;
use local_offermanager\event\offer_class_activated;
use local_offermanager\event\offer_class_inactivated;
use local_offermanager\event\offer_class_deleted;
use local_offermanager\event\offer_class_updated;
use local_offermanager\enrol_setup;
use local_offermanager\event\offer_class_created;
use local_offermanager\persistent\offer_class_model;
use local_offermanager\persistent\offer_course_model;

/**
 * Tests for Offer Manager
 *
 * @package    local_offermanager
 * @category   test
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
final class offer_class_test extends \advanced_testcase
{
    public function test_validate_enrolid_with_noexisting_enrolid()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:enrolid_doesnt_exist', 'local_offermanager'));
        $offer_course->add_class(999);
    }

    public function test_validate_enrolid_with_duplicated_enrolid()
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offer_course->add_class($enrol_instance->id);

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:enrolid_already_exists', 'local_offermanager'));
        $offer_course->add_class($enrol_instance->id);
    }

    public function test_activate_success()
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offer_class = $offer_course->add_class($enrol_instance->id);

        $offer_class->inactivate();

        $this->assertEquals(constants::OFFER_STATUS_INACTIVE, $offer_class->get('status'));

        $sink = $this->redirectEvents();
        $this->assertTrue($offer_class->activate());
        $events = $sink->get_events();
        $sink->close();

        $this->assertEquals(constants::OFFER_STATUS_ACTIVE, $offer_class->get('status'));
        $this->assertCount(1, $events);
        $activate_event = $events[0];
        $this->assertInstanceOf(offer_class_activated::class, $activate_event);
    }

    public function test_activate_fail_by_already_activated_instance()
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offer_class = $offer_course->add_class($enrol_instance->id);

        $offer_class->inactivate();

        $offer_class->activate();

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:offer_class_already_active', 'local_offermanager'));
        $offer_class->activate();
    }

    public function test_inactivate()
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offer_class = $offer_course->add_class($enrol_instance->id);

        $this->assertEquals(constants::OFFER_STATUS_ACTIVE, $offer_class->get('status'));

        $sink = $this->redirectEvents();
        $this->assertTrue($offer_class->inactivate());
        $events = $sink->get_events();
        $sink->close();

        $this->assertEquals(constants::OFFER_STATUS_INACTIVE, $offer_class->get('status'));
        $this->assertCount(1, $events);
        $activate_event = $events[0];
        $this->assertInstanceOf(offer_class_inactivated::class, $activate_event);
    }

    public function test_inctivate_fail_by_already_inactivated_instance()
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offer_class = $offer_course->add_class($enrol_instance->id);

        $offer_class->inactivate();

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:offer_class_already_inactive', 'local_offermanager'));
        $offer_class->inactivate();
    }

    public function test_has_user_enrolments()
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offer_class = $offer_course->add_class($enrol_instance->id);

        $this->assertFalse($offer_class->has_user_enrolments());

        $user = $this->getDataGenerator()->create_user();
        $this->getDataGenerator()->enrol_user($user->id, $course->id, null, 'manual', 0, 0, $offer_class->get('enrolid'));

        $this->assertTrue($offer_class->has_user_enrolments());
    }

    public function test_can_delete()
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offer_class = $offer_course->add_class($enrol_instance->id);

        $this->assertTrue($offer_class->can_delete());

        $user = $this->getDataGenerator()->create_user();
        $this->getDataGenerator()->enrol_user($user->id, $course->id, null, 'manual', 0, 0, $offer_class->get('enrolid'));

        $this->assertFalse($offer_class->can_delete());
    }

    public function test_after_create_triggers_event()
    {
        $this->resetAfterTest(true);

        global $DB;

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $sink = $this->redirectEvents();
        $offer_class = $offer_course->add_class($enrol_instance->id);
        $events = $sink->get_events();
        $sink->close();

        $this->assertCount(1, $events);
        $this->assertInstanceOf(offer_class_created::class, $events[0]);

        $context = \context_offer_class::instance($offer_class->get('id'));
        $this->assertInstanceOf(\context_offer_class::class, $context);
    }

    public function test_before_delete_triggers_event()
    {
        $this->resetAfterTest(true);

        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offer_class = $offer_course->add_class($enrol_instance->id);
        $sink = $this->redirectEvents();
        $offer_class->delete();
        $events = $sink->get_events();
        $sink->close();

        $this->assertCount(1, $events);
        $this->assertInstanceOf(offer_class_deleted::class, $events[0]);
    }

    public function test_get_offer_course_and_get_course_methods()
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offer_class = $offer_course->add_class($enrol_instance->id);

        $this->assertEquals($offer_course->get('id'), $offer_class->get_offer_course()->get('id'));

        $this->assertEquals($course->id, $offer_class->get_course()->id);
    }

    public function test_get_enrol_and_get_enrol_plugin_methods()
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offer_class = $offer_course->add_class($enrol_instance->id);

        $enrol = $offer_class->get_enrol_instance();
        $this->assertIsObject($enrol);
        $this->assertEquals($enrol_instance->id, $enrol->id);

        $enrol_plugin = $offer_class->get_plugin();

        $this->assertInstanceOf("enrol_{$enrol_instance->enrol}_plugin", $enrol_plugin);
    }

    public function test_get_user_enrolments()
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offer_class = $offer_course->add_class($enrol_instance->id);

        $this->assertFalse($offer_class->has_user_enrolments());

        $user = $this->getDataGenerator()->create_user();
        $this->getDataGenerator()->enrol_user($user->id, $course->id, null, 'manual', 0, 0, $offer_class->get('enrolid'));

        $user_enrolments = $offer_class->get_user_enrolments();
        $this->assertIsArray($user_enrolments);
        $this->assertCount(1, $user_enrolments);
    }

    /**
     * Testa a atualização de professores quando adicionando novos professores
     */
    public function test_update_teachers_adding_new_teachers()
    {
        $this->resetAfterTest();

        $teacher1 = $this->getDataGenerator()->create_user();
        $teacher2 = $this->getDataGenerator()->create_user();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $plugin = new enrol_offer_manual_plugin();

        $plugin->add_instance($course, [
            'offercourseid' => $offer_course->get('id'),
            'classname' => 'Turma A',
            'startdate' => time(),
        ]);

        $classes = $offer_course->get_classes();

        $offer_class = $classes[0];

        $result = $offer_class->update_teachers([$teacher1->id]);

        $this->assertTrue($result);

        $this->assertTrue($offer_class->user_has_enrolment($teacher1->id));

        $result = $offer_class->update_teachers([$teacher1->id, $teacher2->id]);
        $this->assertTrue($result);

        $this->assertTrue($offer_class->user_has_enrolment($teacher1->id));
        $this->assertTrue($offer_class->user_has_enrolment($teacher2->id));

        $offer_class = new offer_class_model($offer_class->get('id'));
        $offer_class->get_enrol_instance();

        $current_teachers = $offer_class->get_teachers();
        $this->assertEquals([$teacher1->id, $teacher2->id], $current_teachers);
    }

    /**
     * Testa a atualização de professores quando removendo professores
     */
    public function test_update_teachers_removing_teachers()
    {
        $this->resetAfterTest();

        $teacher1 = $this->getDataGenerator()->create_user();
        $teacher2 = $this->getDataGenerator()->create_user();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $plugin = new enrol_offer_manual_plugin();

        $plugin->add_instance($course, [
            'offercourseid' => $offer_course->get('id'),
            'classname' => 'Turma A',
            'startdate' => time(),
        ]);

        $classes = $offer_course->get_classes();

        $offer_class = $classes[0];

        $result = $offer_class->update_teachers([$teacher1->id, $teacher2->id]);
        $this->assertTrue($result);

        $result = $offer_class->update_teachers([$teacher1->id]);
        $this->assertTrue($result);

        $this->assertTrue($offer_class->user_has_enrolment($teacher1->id));
        $this->assertFalse($offer_class->user_has_enrolment($teacher2->id));

        $current_teachers = $offer_class->get_teachers();
        $this->assertEquals([$teacher1->id], $current_teachers);
    }

    /**
     * Testa a duplicação bem-sucedida de uma turma
     */
    public function test_duplicate_success()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) ['name' => 'Oferta 1']);
        $offer->save();

        $course1 = $this->getDataGenerator()->create_course();
        $course2 = $this->getDataGenerator()->create_course();

        $offer_course1 = $offer->add_course($course1->id);
        $offer_course2 = $offer->add_course($course2->id);

        $enrol_instance_id = $this->create_enrol_instance($offer_course1->get('id'));

        $original_class = offer_class_model::get_by_enrolid($enrol_instance_id);

        $teacher = $this->getDataGenerator()->create_user();

        $return = $original_class->update_teachers([$teacher->id]);
        $this->assertTrue($original_class->is_teacher($teacher->id));
        $this->assertTrue($return);

        $new_class = $original_class->duplicate($offer_course2);

        $original_enrol = $original_class->get_enrol_instance();
        $new_enrol = $new_class->get_enrol_instance();

        $this->assertNotEquals($original_class->get('id'), $new_class->get('id'));
        $this->assertEquals($offer_course2->get('id'), $new_class->get('offercourseid'));

        $this->assertEquals($course2->id, $new_enrol->courseid);
        $this->assertEquals($original_class->get_mapped_field('classname'), $new_class->get_mapped_field('classname'));

        $this->assertTrue($new_class->is_teacher($teacher->id));
    }

    /**
     * Testa exceção ao duplicar para oferta diferente
     */
    public function test_duplicate_different_offer_exception()
    {
        $this->resetAfterTest(true);

        $offer1 = new offer_model(0, (object) ['name' => 'Oferta 1']);
        $offer1->save();

        $offer2 = new offer_model(0, (object) ['name' => 'Oferta 2']);
        $offer2->save();

        // Criar cursos
        $course1 = $this->getDataGenerator()->create_course();
        $course2 = $this->getDataGenerator()->create_course();

        $offer_course1 = $offer1->add_course($course1->id);
        $offer_course2 = $offer2->add_course($course2->id);

        $enrol_instance_id = $this->create_enrol_instance($offer_course1->get('id'));

        $original_class = offer_class_model::get_by_enrolid($enrol_instance_id);
        
        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:duplicate_different_offer', 'local_offermanager'));
        $original_class->duplicate($offer_course2);
    }

    /**
     * Testa exceção ao duplicar para o mesmo curso
     */
    public function test_duplicate_same_course_exception()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) ['name' => 'Oferta Teste']);
        $offer->save();

        $course = $this->getDataGenerator()->create_course();
        $offer_course = $offer->add_course($course->id);

        $enrol_instance_id = $this->create_enrol_instance($offer_course->get('id'));

        $original_class = offer_class_model::get_by_enrolid($enrol_instance_id);

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:duplicate_same_course', 'local_offermanager'));
        $original_class->duplicate($offer_course);
    }

    /**
     * Testa quando não há alterações na lista de professores
     */
    public function test_update_teachers_no_changes()
    {
        $this->resetAfterTest();

        $offer = new offer_model(0, (object) ['name' => 'Oferta Teste']);
        $offer->save();

        $teacher = $this->getDataGenerator()->create_user();

        $course = $this->getDataGenerator()->create_course();
        $offer_course = $offer->add_course($course->id);

        $enrol_instance_id = $this->create_enrol_instance($offer_course->get('id'));

        $offer_class = offer_class_model::get_by_enrolid($enrol_instance_id);

        $result = $offer_class->update_teachers([$teacher->id]);
        $this->assertTrue($result);

        $result = $offer_class->update_teachers([$teacher->id]);
        $this->assertFalse($result);
    }

    /**
     * Cria uma instância de inscrição para testes
     */
    private function create_enrol_instance($offercourseid): int
    {
        $plugin = new enrol_offer_manual_plugin();
        $offer_course = offer_course_model::get_record(['id' => $offercourseid]);

        if(!$offer_course){
            throw new moodle_exception('error:offer_course_not_found', 'local_offermanager');
        }

        $course = $offer_course->get_course();
    
        return $plugin->add_instance($course, [
            'offercourseid' => $offercourseid,
            'classname' => 'Turma Teste',
            'status' => ENROL_INSTANCE_ENABLED,
            'startdate' => time() + 86400 // 1 dia
        ]);
    }


}
