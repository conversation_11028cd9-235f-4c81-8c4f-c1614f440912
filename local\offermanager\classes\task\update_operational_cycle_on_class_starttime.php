<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\task;

use local_offermanager\persistent\offer_class_model;
use core\task\adhoc_task;
use moodle_exception;

defined('MOODLE_INTERNAL') || die();
/**
 * Atualiza o ciclo operacional da turma após o início, com mecanismo de retry em caso de falha temporária.
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class update_operational_cycle_on_class_starttime extends adhoc_task
{
    /**
     * Executa a task de atualização do ciclo operacional da turma.
     * Tenta novamente em caso de falha temporária, até o limite de tentativas.
     *
     * @throws \moodle_exception Se falhar definitivamente após todas as tentativas.
     * @return void
     */
    public function execute(): void
    {
        $customdata = (array)$this->get_custom_data();
        $offerclassid = $customdata['offerclassid'];

        mtrace(get_string('task:operational_cycle_start', 'local_offermanager', [
            'id' => $offerclassid
        ]));

        $offerclass = offer_class_model::get_record(['id' => $offerclassid]);

        if (!$offerclass) {
            mtrace(get_string('error:operational_cycle_notfound', 'local_offermanager', $offerclassid));
            return;
        }

        if($offerclass->has_started()){
            mtrace(get_string('error:offer_class_already_started', 'local_offermanager', $offerclassid));
            return;
        }

        $return = $offerclass->update_operational_cycle();

        if ($return) {
            $offerclass->update_accessability();
            mtrace(get_string('task:operational_cycle_success', 'local_offermanager'));
        } else {
            throw new moodle_exception('error:operational_cycle_fail', 'local_offermanager');            
        }
    }
}
