<template>
  <div class="filter-actions">
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'FilterActions'
}
</script>

<style lang="scss" scoped>
.filter-actions {
  display: flex;
  gap: 10px;
  align-items: flex-end;
  height: 100%;
  
  @media (max-width: 768px) {
    width: 100%;
    margin-right: 0;
  }
}

:deep(.btn-filter), 
:deep(.btn-clear) {
  padding: 8px 15px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 16px;
  font-weight: 400;
  height: 38px;
}

:deep(.btn-filter) {
  background-color: var(--primary) !important;
  color: #fff !important;
}

:deep(.btn-clear) {
  background-color: #343A40 !important;
  border: 1px solid #343A40 !important;
  color: #fff !important;
}
</style> 