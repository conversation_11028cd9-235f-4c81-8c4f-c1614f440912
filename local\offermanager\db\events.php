<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON>le is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Event observers for Offer Manager
 *
 * @package    local_offermanager
 * @category   event
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$observers = [
    [
        'eventname' => '\core\event\course_completed',
        'callback' => '\local_offermanager\observer::update_situations_on_course_completion',
        'includefile' => '/local/offermanager/classes/observer.php',
        'internal' => false,
        'priority' => 0,
    ],
    [
        'eventname' => '\core\event\course_module_completion_updated',
        'callback' => '\local_offermanager\observer::update_enrol_situation_on_module_completion',
        'includefile' => '/local/offermanager/classes/observer.php',
        'internal' => false,
        'priority' => 0,
    ],
    [
        'eventname' => '\core\event\enrol_instance_created',
        'callback' => '\local_offermanager\observer::enrol_instance_created',
        'includefile' => '/local/offermanager/classes/observer.php',
        'internal' => false,
        'priority' => 0,
    ],
    [
        'eventname' => '\core\event\enrol_instance_updated',
        'callback' => '\local_offermanager\observer::enrol_instance_updated',
        'includefile' => '/local/offermanager/classes/observer.php',
        'internal' => false,
        'priority' => 0,
    ],
    [
        'eventname' => '\core\event\enrol_instance_deleted',
        'callback' => '\local_offermanager\observer::enrol_instance_deleted',
        'includefile' => '/local/offermanager/classes/observer.php',
        'internal' => false,
        'priority' => 0,
    ],
    [
        'eventname' => '\local_offermanager\event\offer_user_enrol_created',
        'callback' => '\local_offermanager\observer::update_class_accessibility_on_enrol_created',
        'includefile' => '/local/offermanager/classes/observer.php',
        'internal' => false,
        'priority' => 0,
    ],
    [
        'eventname' => '\local_offermanager\event\offer_user_enrol_deleted',
        'callback' => '\local_offermanager\observer::offer_user_enrol_deleted',
        'includefile' => '/local/offermanager/classes/observer.php',
        'internal' => false,
        'priority' => 0,
    ]
];
