<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON>le is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\task;

use core\task\scheduled_task;
use local_offermanager\persistent\offer_class_model;
use local_offermanager\constants;

defined('MOODLE_INTERNAL') || die();

/**
 * Scheduled task para tornar turmas acessíveis automaticamente.
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class turn_classes_accessible extends scheduled_task
{

    /**
     * Retorna o nome da task.
     *
     * @return string
     */
    public function get_name()
    {
        return get_string('task:turnclassesaccessible', 'local_offermanager');
    }

    /**
     * Executa a task.
     */
    public function execute()
    {
        global $DB;

        $rs = $DB->get_recordset(
            'local_offermanager_class',
            [
                'operational_cycle' => constants::OFFER_CLASS_OPERATIONAL_CYCLE_STARTED,
                'isaccessible' => constants::OFFER_CLASS_NOT_ACCESSIBLE
            ]
        );

        while ($record = $rs->next()) {
            $offerclass = new offer_class_model($record->id);

            if ($offerclass->check_if_accessible()) {
                $offerclass->set_accessible();
            }
        }
        $rs->close();
    }
}
