<?php
// Este arquivo faz parte do plugin local_offermanager - Moodle.
// Tela intermediária de acesso negado por inscrições mínimas não atingidas.

require('../../config.php');

$offerclassid = required_param('offerclassid', PARAM_INT);
$backurl = optional_param('backurl', '', PARAM_LOCALURL);

if(strpos($backurl, '/course/view.php?id=')){
    $backurl = new moodle_url('/blocks/mycourses/index.php');
}

require_login();

$offerclass = \local_offermanager\persistent\offer_class_model::get_record(['id' => $offerclassid]);

if(!$offerclass || $offerclass->is_accessible()){
    redirect($backurl);
}

$context = context_offer_class::instance($offerclassid);

$PAGE->set_context($context);
$PAGE->set_url(new moodle_url('/local/offermanager/notaccessible.php', ['offerclassid' => $offerclassid, 'backurl' => $backurl]));
$PAGE->set_pagelayout('standard');
$PAGE->set_title(get_string('notaccessible_title', 'local_offermanager'));
$PAGE->set_heading(get_string('notaccessible_heading', 'local_offermanager'));

echo $OUTPUT->header();

$backlink = $backurl ?: ($_SERVER['HTTP_REFERER'] ?? new moodle_url('/'));

$modalattributes = [
    'role'=>'alertdialog',
    'aria-labelledby'=>'modal-header',
    'aria-describedby'=>'modal-body',
    'aria-modal'=>'true'
];

echo $OUTPUT->box_start('generalbox modal modal-dialog modal-in-page show', 'notice', $modalattributes);
echo $OUTPUT->box_start('modal-content', 'modal-content');
echo $OUTPUT->box_start('modal-header px-3', 'modal-header');
echo html_writer::tag('h4', get_string('notaccessible_title', 'local_offermanager'), ['id' => 'modal-header']);
echo $OUTPUT->box_end();
$bodyattributes = [
    'role'=>'alert',
    'data-aria-autofocus'=>'true',
    'id' => 'modal-body'
];
echo $OUTPUT->box_start('modal-body', 'modal-body', $bodyattributes);
echo html_writer::tag('p', get_string('notaccessible_message', 'local_offermanager', [
    'classname' => $offerclass->get_mapped_field('classname'),
    'minrequired' => $offerclass->get_mapped_field('minusers')
]));
echo $OUTPUT->box_end();
echo $OUTPUT->box_start('modal-footer', 'modal-footer');
echo html_writer::tag('div',
    html_writer::link($backlink, get_string('back'), ['class' => 'btn btn-primary']),
    ['class' => 'buttons']
);
echo $OUTPUT->box_end();
echo $OUTPUT->box_end();
echo $OUTPUT->box_end();

echo $OUTPUT->footer();