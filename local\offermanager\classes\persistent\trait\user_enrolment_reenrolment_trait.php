<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\persistent\trait;

use local_offermanager\persistent\offer_user_enrol_history_model;
use local_offermanager\event\offer_user_enrol_reenroled;
use moodle_exception;
use stdClass;

defined('MOODLE_INTERNAL') || die();

/**
 * Trait for handling the re-enrolment of user enrolments.
 *
 * This trait should be used by classes that represent a record
 * in the local_offermanager_ue table.
 *
 * @package    local_offermanager
 * @copyright  2025 Your Name/Organisation
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 *
 * @property int $id
 * @property int $ueid
 * @property int $history
 * @method stdClass get_user_enrolment()
 * @method mixed get(string $property)
 * @method void set(string $property, mixed $value)
 * @method void update()
 */
trait user_enrolment_reenrolment_trait
{

    /**
     * Archives the current user enrolment record into the history table.
     *
     * @return bool True on success, throws exception on failure or if already archived.
     * @throws moodle_exception If the enrolment is already archived or user not found.
     */
    public function archive_enrolment(): bool
    {
        global $DB;

        $userid = $this->get('userid');

        if ($this->get('history') == 1) {
            throw new moodle_exception('error:enrol_already_archived', 'local_offermanager');
        }

        if (!$DB->record_exists('user', ['id' => $userid])) {
            throw new moodle_exception('error:user_not_found', 'local_offermanager');
        }

        $user_enrolment = $this->get_user_enrolment();

        $historyid = $this->insert_in_history_table($user_enrolment);

        if (!$historyid) {
            throw new moodle_exception('error:cannot_create_history_record', 'local_offermanager');
        }

        $DB->delete_records(
            'user_enrolments',
            [
                'id' => $user_enrolment->id
            ]
        );

        $this->set('history', 1);
        $this->set('userhistory', $userid);
        $this->set('timehistory', time());
        $this->update();

        return true;
    }

    /**
     * Inserts a user enrolment record into the history table using direct SQL execution.
     *
     * @param stdClass $user_enrolment The user enrolment object (from user_enrolments table).
     * @return int|false The original enrolment ID on success, false on failure.
     */
    protected function insert_in_history_table(\stdClass $user_enrolment): int|false
    {
        global $DB;

        $tablename = offer_user_enrol_history_model::TABLE;
        $sql = "INSERT INTO {{$tablename}} (id, status, enrolid, userid, timestart, timeend, modifierid, timecreated, timemodified)
                VALUES (:id, :status, :enrolid, :userid, :timestart, :timeend, :modifierid, :timecreated, :timemodified)";

        $params = [
            'id'           => $user_enrolment->id,
            'status'       => $user_enrolment->status,
            'enrolid'      => $user_enrolment->enrolid,
            'userid'       => $user_enrolment->userid,
            'timestart'    => $user_enrolment->timestart,
            'timeend'      => $user_enrolment->timeend,
            'modifierid'   => $user_enrolment->modifierid,
            'timecreated'  => $user_enrolment->timecreated,
            'timemodified' => $user_enrolment->timemodified,
        ];

        $DB->execute($sql, $params);

        return $DB->get_field(
            offer_user_enrol_history_model::TABLE,
            'id',
            [
                'id' => $user_enrolment->id
            ]
        );
    }

    /**
     * Checks if the enrolment is currently archived.
     *
     * @return bool True if archived, false otherwise.
     */
    public function is_archived(): bool
    {
        return $this->get('history') == 1;
    }

    /**
     * Checks if the user enrolment is eligible for re-enrolment based on offer class settings and enrolment status.
     *
     * @return bool True if the enrolment can be re-enrolled, false otherwise.
     * @throws moodle_exception
     */
    public function can_reenrol(): bool
    {
        $offerclass = $this->get_offer_class();

        if (!$offerclass) {
            throw new moodle_exception('error:offer_class_not_found', 'local_offermanager');
        }
  
        if (!$offerclass->allow_reenrol()) {
            return false;
        }
        
        $userenrolment = $this->get_user_enrolment();
        
        if ($userenrolment->status != ENROL_USER_SUSPENDED) {
            return false;
        }

        $now = time();
        if ($userenrolment->timestart > $now) {
            return false;
        }

        $classenableenddate = $offerclass->get_mapped_field('enableenddate');
        $classenddate = $offerclass->get_mapped_field('enddate');

        if ($classenableenddate && $classenddate && $classenddate <= $now) {
            return false;
        }

        $allowed_situations = $offerclass->get_allowed_reenrol_situations() ?? []; 
        $current_situation = $this->get('situation');

        if (!in_array($current_situation, $allowed_situations)) {
            return false;
        }

        return true;
    }

    /**
     * Processes the re-enrolment of a user.
     *
     * This method performs the full re-enrolment flow: checks eligibility,
     * archives the old enrolment, unenrols the user, enrols the user again,
     * and triggers a re-enrolment event.
     *
     * @return bool True on successful re-enrolment.
     * @throws moodle_exception If re-enrolment is not possible or any step fails.
     */
    public function process_reenrolment(): bool
    {
        if (!$this->can_reenrol()) {
            throw new moodle_exception('error:cannot_reenrol', 'local_offermanager');
        }

        $offerclass = $this->get_offer_class();

        if (!$offerclass) {
            throw new moodle_exception('error:offer_class_not_found', 'local_offermanager');
        }

        $userid = $this->get('userid');

        $archived = $this->archive_enrolment();

        if($archived) {
            $offerclass->enrol_user($userid);
            $event = offer_user_enrol_reenroled::instance($this);
            $event->trigger();
            return true;
        }

        return false;
    }
}
