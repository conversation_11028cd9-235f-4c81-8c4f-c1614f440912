<?php

namespace local_offermanager\util;

require_once($CFG->libdir . '/csvlib.class.php');

use csv_export_writer as csv_export_writer_base;

class csv_export_writer extends csv_export_writer_base
{
    /**
     * Set the filename for the uploaded csv file
     *
     * @param string $dataname    The name of the module.
     * @param string $extenstion  File extension for the file.
     */
    public function set_filename($dataname, $extension = '.csv')
    {
        $filename = clean_filename($dataname);
        $filename .= $extension;
        $this->filename = $filename;
    }
}
