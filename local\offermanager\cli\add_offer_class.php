<?php
define('CLI_SCRIPT', true);

require(__DIR__ . '/../../../config.php');
require_once($CFG->libdir.'/clilib.php');

use local_offermanager\external\offer_class_external;

// Define os parâmetros aceitos.
$longoptions = [
    'offercourseid' => false,
    'classname' => false,
    'enrol' => false,
    'startdate' => false,
    'teachers' => false,
    'optional_fields' => false,
    'help' => false
];

$shortoptions = [
    'i' => 'offercourseid',
    'n' => 'classname',
    'e' => 'enrol',
    's' => 'startdate',
    't' => 'teachers',
    'o' => 'optional_fields',
    'h' => 'help'
];

list($options, $unrecognized) = cli_get_params($longoptions, $shortoptions);

if (!empty($options['help'])) {
    echo "Adiciona uma turma a uma oferta de curso\n";
    echo "Uso: php script.php [opções]\n";
    echo "Parâmetros obrigatórios:\n";
    echo "  -i --offercourseid=ID         ID da oferta de curso\n";
    echo "  -n --classname=NOME           Nome da turma\n";
    echo "Parâmetros opcionais:\n";
    echo "  -e --enrol=PLUGIN             Nome do plugin de inscrição (ex: manual, self)\n";
    echo "  -s --startdate=TIMESTAMP      Data de início (timestamp UNIX). Padrão: agora\n";
    echo "  -t --teachers=JSON_ARRAY      IDs dos professores, ex: '[1,2,3]'\n";
    echo "  -o --optional_fields=JSON_OBJ Campos opcionais em JSON\n";
    echo "  -h --help                     Exibe esta ajuda\n";
    echo "Example: php add_offer_class.php -i=1 --classname='Turma teste'";
    exit(0);
}

$required = ['offercourseid', 'classname'];
foreach ($required as $param) {
    if (empty($options[$param])) {
        cli_error("Parâmetro obrigatório ausente: --$param");
    }
}

// Processa os parâmetros.
$offercourseid = (int)$options['offercourseid'];
$classname = $options['classname'];
$enrol = isset($options['startdate']) && $options['enrol'] ? $options['enrol'] : 'offer_manual';
$startdate = isset($options['startdate']) && is_numeric($options['startdate']) ? (int)$options['startdate'] : time();

$teachers = [];
if (!empty($options['teachers'])) {
    $decoded = json_decode($options['teachers'], true);
    if (!is_array($decoded)) {
        cli_error("Formato inválido para --teachers. Use JSON array, ex: '[1,2]'");
    }
    $teachers = $decoded;
}

$optional_fields = [];
if (!empty($options['optional_fields'])) {
    $decoded = json_decode($options['optional_fields'], true);
    if (!is_array($decoded)) {
        cli_error("Formato inválido para --optional_fields. Use JSON object");
    }
    $optional_fields = $decoded;
}

// Chama a função externa para adicionar a turma.
try {
    $result = offer_class_external::add(
        $enrol,
        $offercourseid,
        $classname,
        $startdate,
        $teachers,
        $optional_fields
    );
    cli_writeln("Turma criada com sucesso: $result");
} catch (Exception $e) {
    cli_error('Erro ao criar turma: ' . $e->getMessage());
}