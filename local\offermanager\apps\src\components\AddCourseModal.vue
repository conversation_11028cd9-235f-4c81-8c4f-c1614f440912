<template>
  <div class="modal-overlay" v-if="modelValue" @click="closeModal">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h2>Adicionar curso</h2>
        <button class="close-button" @click="closeModal">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="modal-body">
        <h3 class="section-title">SELECIONAR CURSO</h3>

        <div class="search-section">
          <div class="search-group">
            <Autocomplete
              v-model="selectedCategory"
              :items="categoryOptions"
              label="Categoria"
              placeholder="Pesquisar..."
              :input-max-width="null"
              :loading="loadingCategories"
              :show-filter-tags="false"
              :show-selected-in-input="true"
              :auto-open="true"
              :has-search-icon="true"
              :max-label-length="25"
              :no-results-text="
                categoryOptions.length === 0
                  ? 'Nenhuma categoria disponível'
                  : 'Nenhuma categoria encontrada'
              "
              @select="handleCategorySelect"
            />
          </div>

          <div class="search-group">
            <Autocomplete
              v-model="selectedCourse"
              :items="courseOptions"
              label="Curso"
              placeholder="Pesquisar..."
              :input-max-width="null"
              :disabled="!selectedCategory"
              :loading="loadingCourses || loadingMoreCourses"
              :auto-open="true"
              :has-search-icon="true"
              :max-label-length="25"
              :keep-open-on-select="true"
              :no-results-text="courseNoResultsText"
              @select="handleCourseSelect"
              @load-more="loadMoreCourses"
              @search="handleCourseSearch"
              ref="courseAutocomplete"
            />
          </div>
        </div>

        <div class="table-container">
          <div
            v-if="selectedCoursesPreview.length === 0"
            class="empty-preview-message"
          >
            <p>Selecione cursos acima para adicioná-los à oferta</p>
          </div>
          <CustomTable
            v-else
            :headers="tableHeaders"
            :items="filteredCourses"
            :sort-by="sortBy"
            :sort-desc="sortDesc"
            @sort="handleTableSort"
          >
            <template #item-actions="{ item }">
              <div class="action-buttons">
                <button
                  class="btn-action btn-delete"
                  @click="removeCourse(item)"
                  title="Remover da lista"
                >
                  <i class="fa fa-trash fa-fw"></i>
                </button>
              </div>
            </template>
          </CustomTable>
        </div>

        <Pagination
          v-if="selectedCoursesPreview.length > 0"
          v-model:current-page="currentPage"
          v-model:per-page="perPage"
          :total="selectedCoursesPreview.length"
          @update:current-page="handlePageChange"
          @update:per-page="handlePerPageChange"
        />
      </div>

      <div class="modal-footer">
        <CustomButton
          variant="primary"
          label="Confirmar"
          :disabled="selectedCoursesPreview.length === 0"
          @click="confirm"
        />
        <CustomButton
          variant="secondary"
          label="Cancelar"
          @click="closeModal"
        />
      </div>
    </div>
  </div>

  <!-- Modal de confirmação removido, pois a exclusão é feita diretamente -->
</template>

<script>
import CustomInput from "./CustomInput.vue";
import CustomButton from "./CustomButton.vue";
import CustomTable from "./CustomTable.vue";
import Pagination from "./Pagination.vue";
import Autocomplete from "./Autocomplete.vue";
import FilterTag from "./FilterTag.vue";
import {
  getCategories,
  getCoursesByCategory,
  addCoursesToOffer,
  getCurrentCourses,
} from "@/services/offer";

export default {
  name: "AddCourseModal",

  components: {
    CustomInput,
    CustomButton,
    CustomTable,
    Pagination,
    Autocomplete,
    FilterTag,
  },

  props: {
    modelValue: {
      type: Boolean,
      required: true,
    },
    offerId: {
      type: Number,
      required: true,
    },
  },

  emits: ["update:modelValue", "confirm"],

  data() {
    return {
      selectedCategory: null,
      selectedCategoryObject: null, // Armazenar o objeto completo da categoria selecionada
      selectedCourse: null,
      categoryOptions: [],
      courseOptions: [],
      currentPage: 1,
      perPage: 5,
      sortBy: "name",
      sortDesc: false,
      loadingCategories: false,
      loadingCourses: false,
      loadingCurrentCourses: false,

      // Paginação de cursos potenciais
      coursesPage: 1,
      coursesPerPage: 20,
      coursesTotalPages: 1,
      hasMoreCourses: false,
      loadingMoreCourses: false,

      // Configuração da tabela
      tableHeaders: [
        { text: "CURSO", value: "name", sortable: true },
        { text: "AÇÕES", value: "actions", sortable: false, align: "right" },
      ],

      // Cursos selecionados na sessão atual (pré-visualização)
      selectedCoursesPreview: [],

      // Cursos já adicionados à oferta (para filtrar os disponíveis)
      existingCourses: [],
    };
  },

  computed: {
    filteredCourses() {
      // Aplicar ordenação aos cursos selecionados na sessão atual (pré-visualização)
      const sortedCourses = [...this.selectedCoursesPreview].sort((a, b) => {
        const modifier = this.sortDesc ? -1 : 1;
        if (a[this.sortBy] < b[this.sortBy]) return -1 * modifier;
        if (a[this.sortBy] > b[this.sortBy]) return 1 * modifier;
        return 0;
      });

      // Aplicar paginação
      const startIndex = (this.currentPage - 1) * this.perPage;
      const endIndex = startIndex + this.perPage;
      return sortedCourses.slice(startIndex, endIndex);
    },

    // Calcula o número total de páginas para a paginação
    totalPages() {
      return Math.ceil(this.selectedCoursesPreview.length / this.perPage);
    },

    // Propriedade computada para texto de "sem resultados" do autocomplete de curso
    courseNoResultsText() {
      if (this.loadingCourses) {
        return "Buscando cursos...";
      }
      if (this.loadingMoreCourses) {
        return "Carregando mais cursos...";
      }
      if (!this.selectedCategory) {
        return "Selecione uma categoria primeiro";
      }
      // Verifica se há cursos disponíveis na categoria, mas todos já foram selecionados
      if (this.courseOptions.length === 0 && this.selectedCategory) {
        return "Todos os cursos já foram adicionados";
      }
      return "Nenhum curso encontrado";
    },
  },

  watch: {
    modelValue(val) {
      if (val) {
        this.loadCurrentCourses(); // Carrega cursos já adicionados à oferta
        this.loadAllCategories(); // Carrega todas as categorias ao abrir
      } else {
        // Limpa dados ao fechar para garantir estado inicial na próxima abertura
        this.selectedCategory = null;
        this.selectedCategoryObject = null;
        this.selectedCourse = null;
        this.categoryOptions = [];
        this.courseOptions = [];
        this.selectedCoursesPreview = []; // Limpa a lista de cursos selecionados na sessão atual
      }
    },
    selectedCategory(newValue) {
      // Se o usuário limpar a seleção, recarregar os cursos
      if (!newValue) {
        this.courseOptions = [];
        this.selectedCourse = null;
        this.selectedCategoryObject = null;
        this.loadCurrentCourses();
      }
    },
    courseOptions(newOptions) {
      // Verifica se há menos de 10 cursos disponíveis e carrega mais se necessário
      if (
        newOptions.length < 10 &&
        this.hasMoreCourses &&
        !this.loadingMoreCourses &&
        !this.loadingCourses
      ) {
        this.$nextTick(() => {
          this.loadMoreCourses();
          console.log("Carregando mais cursos... via watch");
        });
      }
    },
  },

  methods: {
    closeModal() {
      this.$emit("update:modelValue", false);
      // Limpar estados ao fechar o modal
      this.selectedCategory = null;
      this.selectedCategoryObject = null;
      this.selectedCourse = null;
      this.selectedCoursesPreview = []; // Limpa a lista de cursos selecionados na sessão atual
    },

    async confirm() {
      try {
        // Verificar se há cursos selecionados na sessão atual
        if (this.selectedCoursesPreview.length === 0) {
          console.warn("Nenhum curso selecionado para adicionar");
          this.closeModal();
          return;
        }

        // Obter apenas os IDs dos cursos selecionados na sessão atual
        const courseIds = this.selectedCoursesPreview.map(
          (course) => course.id
        );

        // Adicionar os cursos selecionados à oferta
        await addCoursesToOffer(this.offerId, courseIds);

        // Emitir evento de confirmação com a lista de cursos adicionados
        this.$emit("confirm", this.selectedCoursesPreview);

        // Fechar o modal
        this.closeModal();
      } catch (error) {
        console.error("Erro ao salvar cursos:", error);
        // Aqui você pode adicionar uma notificação de erro para o usuário
      }
    },

    async loadCurrentCourses() {
      console.log("loadCurrentCourses this.offerId:", this.offerId);
      try {
        this.loadingCurrentCourses = true;
        const response = await getCurrentCourses(this.offerId);
        console.log("loadCurrentCourses response:", response);
        if (response && response.data) {
          // Verificar se response.data é um array antes de usar map
          if (Array.isArray(response.data)) {
            // Armazenar os cursos já adicionados à oferta para filtrar os disponíveis
            this.existingCourses = response.data.map((course) => ({
              id: course.courseid,
              name: course.fullname,
              offerCourseId: course.id,
            }));
          } else {
            // Se não for um array, inicializa como array vazio
            this.existingCourses = [];
          }
        }
      } catch (error) {
        console.error("Erro ao carregar cursos da oferta:", error);
        // Caso ocorra erro, deixamos a lista vazia e permitimos que o usuário adicione cursos normalmente
        this.existingCourses = [];
      } finally {
        this.loadingCurrentCourses = false;
      }
    },

    // Carrega todas as categorias
    async loadAllCategories() {
      try {
        this.loadingCategories = true;
        this.categoryOptions = []; // Limpa antes de carregar
        const response = await getCategories(""); // Busca todas
        if (response && response.data) {
          this.categoryOptions = response.data.map((category) => ({
            value: category.id,
            label: category.name,
          }));
        }
      } catch (error) {
        console.error("Erro ao carregar todas as categorias:", error);
        this.categoryOptions = []; // Garante que esteja vazio em caso de erro
      } finally {
        this.loadingCategories = false;
      }
    },

    // Chamado quando uma categoria é selecionada no Autocomplete
    handleCategorySelect(category) {
      if (!category) {
        this.removeCategory(); // Limpa se a seleção for removida
        return;
      }

      // Armazenamos o objeto completo para referência
      this.selectedCategoryObject = category;
      // Para o v-model do Autocomplete, armazenamos apenas o value
      this.selectedCategory = category.value;

      // Limpa opções e seleção de curso anterior
      this.courseOptions = [];
      this.selectedCourse = null;

      // Reseta a paginação
      this.coursesPage = 1;
      this.coursesTotalPages = 1;
      this.hasMoreCourses = false;

      // Carrega os cursos para a nova categoria selecionada
      this.loadCoursesForCategory(category.value);
    },

    // Carrega todos os cursos para uma categoria específica
    async loadCoursesForCategory(
      categoryId,
      page = 1,
      append = false,
      searchText = ""
    ) {
      if (!categoryId) return;

      // Garantir que estamos usando o ID correto da categoria
      const id = typeof categoryId === "object" ? categoryId.value : categoryId;

      try {
        if (page === 1) {
          this.loadingCourses = true;
          if (!append) {
            this.courseOptions = []; // Limpa antes de carregar apenas se não estiver anexando
          }
        } else {
          this.loadingMoreCourses = true;
        }

        // Chama getCoursesByCategory com o termo de busca (se fornecido)
        const response = await getCoursesByCategory(
          this.offerId,
          id,
          searchText,
          page,
          this.coursesPerPage
        );

        // Verifica se a resposta é um array e extrai os dados
        let paginationData = null;
        let coursesList = [];

        // Tenta extrair a lista de cursos da resposta
        try {
          if (Array.isArray(response) && response.length > 0) {
            if (response[0].error === false && response[0].data) {
              // Formato: [{ error: false, data: { page, total_pages, courses } }]
              if (response[0].data.courses) {
                paginationData = response[0].data;
                coursesList = paginationData.courses || [];
              }
              // Formato alternativo: [{ error: false, data: [...cursos] }]
              else if (Array.isArray(response[0].data)) {
                coursesList = response[0].data;
                paginationData = { page: 1, total_pages: 1 };
              }
              // Formato: [{ error: false, data: { data: [...cursos] } }]
              else if (
                response[0].data.data &&
                Array.isArray(response[0].data.data)
              ) {
                coursesList = response[0].data.data;
                paginationData = {
                  page: response[0].data.page || 1,
                  total_pages: response[0].data.total_pages || 1,
                };
              }
            } else {
              // Formato simples: array de cursos diretamente
              coursesList = response;
              paginationData = { page: 1, total_pages: 1 };
            }
          } else if (response && typeof response === "object") {
            // Formato: { data: { courses: [...] } }
            if (response.data && response.data.courses) {
              coursesList = response.data.courses;
              paginationData = {
                page: response.data.page || 1,
                total_pages: response.data.total_pages || 1,
              };
            }
            // Formato: { courses: [...] }
            else if (response.courses) {
              coursesList = response.courses;
              paginationData = {
                page: response.page || 1,
                total_pages: response.total_pages || 1,
              };
            }
            // Formato: { data: [...] }
            else if (response.data && Array.isArray(response.data)) {
              coursesList = response.data;
              paginationData = { page: 1, total_pages: 1 };
            }
          }

          // Se ainda não encontrou cursos, tenta outras abordagens
          if (coursesList.length === 0 && response) {
            // Tenta extrair diretamente da propriedade 'data' se for um objeto
            if (typeof response === "object" && !Array.isArray(response)) {
              for (const key in response) {
                if (Array.isArray(response[key])) {
                  coursesList = response[key];
                  paginationData = { page: 1, total_pages: 1 };
                  break;
                }
              }
            }
          }
        } catch (e) {
          console.error("Erro ao processar resposta:", e);
        }

        if (paginationData) {
          // Atualiza informações de paginação
          this.coursesPage = paginationData.page || 1;
          this.coursesTotalPages = paginationData.total_pages || 1;
          this.hasMoreCourses =
            (paginationData.page || 1) < (paginationData.total_pages || 1);

          if (coursesList && coursesList.length > 0) {
            // Filtra os cursos que já foram adicionados à oferta
            const coursesNotYetAdded = coursesList.filter(
              (course) =>
                // Não mostrar cursos que já estão na oferta
                !this.existingCourses.some((c) => c.id === course.id) &&
                // Não mostrar cursos que já foram selecionados na sessão atual
                !this.selectedCoursesPreview.some((c) => c.id === course.id)
            );

            const newCourseOptions = coursesNotYetAdded.map((course) => ({
              value: course.id,
              label: course.fullname,
            }));

            if (append) {
              // Adiciona os novos cursos aos existentes
              this.courseOptions = [...this.courseOptions, ...newCourseOptions];
            } else {
              // Substitui completamente a lista
              this.courseOptions = newCourseOptions;
            }
          }
        } else {
          console.warn("Formato de resposta inesperado");
        }
      } catch (error) {
        console.error("Erro ao carregar cursos da categoria:", error);
        if (!append) {
          this.courseOptions = []; // Garante que esteja vazio em caso de erro apenas se não estiver anexando
        }
      } finally {
        if (page === 1) {
          this.loadingCourses = false;
        } else {
          this.loadingMoreCourses = false;
        }
      }
    },

    // Carrega mais cursos (próxima página)
    async loadMoreCourses() {
      if (
        this.hasMoreCourses &&
        !this.loadingMoreCourses &&
        !this.loadingCourses
      ) {
        const nextPage = this.coursesPage + 1;
        await this.loadCoursesForCategory(
          this.selectedCategory,
          nextPage,
          true
        );
      }
    },

    // Manipula a busca por texto no autocomplete de cursos
    async handleCourseSearch(searchText) {
      if (!this.selectedCategory) return;

      console.log("Buscando cursos com termo:", searchText);

      // Reseta a paginação para a primeira página
      this.coursesPage = 1;
      this.coursesTotalPages = 1;
      this.hasMoreCourses = false;

      // Carrega cursos com o termo de busca (ou sem termo se searchText for vazio)
      await this.loadCoursesForCategory(
        this.selectedCategory,
        1,
        false,
        searchText || ""
      );
    },

    // Chamado quando um curso é selecionado no Autocomplete
    handleCourseSelect(course) {
      if (
        course &&
        !this.selectedCoursesPreview.some((c) => c.id === course.value)
      ) {
        // Adiciona o curso à lista de pré-visualização se ainda não estiver lá
        this.selectedCoursesPreview.push({
          id: course.value,
          name: course.label,
        });

        // Atualiza as opções de cursos para remover o curso selecionado
        this.courseOptions = this.courseOptions.filter(
          (c) => c.value !== course.value
        );

        // Reseta a paginação para a primeira página quando um novo curso é adicionado
        this.currentPage = 1;
      }

      // Limpa a seleção do autocomplete de curso após adicionar
      this.selectedCourse = null;
    },

    removeCourse(course) {
      // Remove o curso diretamente da lista de pré-visualização sem abrir modal de confirmação
      // Encontra o curso na lista de pré-visualização
      const courseIndex = this.selectedCoursesPreview.findIndex(
        (c) => c.id === course.id
      );

      if (courseIndex !== -1) {
        // Remove o curso da lista de pré-visualização
        const removedCourse = this.selectedCoursesPreview.splice(
          courseIndex,
          1
        )[0];

        // Verifica se após a remoção, a página atual ficou vazia e não é a primeira página
        if (
          this.currentPage > 1 &&
          this.currentPage >
            Math.ceil(this.selectedCoursesPreview.length / this.perPage)
        ) {
          // Volta para a página anterior
          this.currentPage = Math.max(1, this.currentPage - 1);
        }

        // Adiciona o curso de volta às opções disponíveis
        if (this.selectedCategory) {
          // Se uma categoria estiver selecionada, verificar se o curso pertence a ela
          // antes de adicioná-lo de volta às opções (sem termo de busca para garantir que encontre o curso)
          getCoursesByCategory(
            this.offerId,
            this.selectedCategory,
            "",
            1,
            this.coursesPerPage
          )
            .then((response) => {
              let coursesList = [];

              // Tenta extrair a lista de cursos da resposta
              try {
                if (Array.isArray(response) && response.length > 0) {
                  if (response[0].error === false && response[0].data) {
                    // Formato: [{ error: false, data: { page, total_pages, courses } }]
                    if (response[0].data.courses) {
                      coursesList = response[0].data.courses || [];
                    }
                    // Formato alternativo: [{ error: false, data: [...cursos] }]
                    else if (Array.isArray(response[0].data)) {
                      coursesList = response[0].data;
                    }
                    // Formato: [{ error: false, data: { data: [...cursos] } }]
                    else if (
                      response[0].data.data &&
                      Array.isArray(response[0].data.data)
                    ) {
                      coursesList = response[0].data.data;
                    }
                  } else {
                    // Formato simples: array de cursos diretamente
                    coursesList = response;
                  }
                } else if (response && typeof response === "object") {
                  // Formato: { data: { courses: [...] } }
                  if (response.data && response.data.courses) {
                    coursesList = response.data.courses;
                  }
                  // Formato: { courses: [...] }
                  else if (response.courses) {
                    coursesList = response.courses;
                  }
                  // Formato: { data: [...] }
                  else if (response.data && Array.isArray(response.data)) {
                    coursesList = response.data;
                  }
                }

                // Se ainda não encontrou cursos, tenta outras abordagens
                if (coursesList.length === 0 && response) {
                  // Tenta extrair diretamente da propriedade 'data' se for um objeto
                  if (
                    typeof response === "object" &&
                    !Array.isArray(response)
                  ) {
                    for (const key in response) {
                      if (Array.isArray(response[key])) {
                        coursesList = response[key];
                        break;
                      }
                    }
                  }
                }
              } catch (e) {
                console.error("Erro ao processar resposta em removeCourse:", e);
              }

              if (coursesList && coursesList.length > 0) {
                const courseInCategory = coursesList.find(
                  (c) => c.id === removedCourse.id
                );
                if (courseInCategory) {
                  this.courseOptions.push({
                    value: removedCourse.id,
                    label: removedCourse.name,
                  });
                }
              }
            })
            .catch((error) => {
              console.error("Erro ao verificar categoria do curso:", error);
            });
        } else {
          // Se não houver categoria selecionada, adiciona o curso de volta diretamente
          this.courseOptions.push({
            value: removedCourse.id,
            label: removedCourse.name,
          });
        }
      }
    },

    // Métodos relacionados ao modal de confirmação de exclusão foram removidos

    handleTableSort({ sortBy, sortDesc }) {
      this.sortBy = sortBy;
      this.sortDesc = sortDesc;
    },

    // Manipula a mudança de página na paginação
    handlePageChange(page) {
      this.currentPage = page;
    },

    // Manipula a mudança de itens por página
    handlePerPageChange(perPage) {
      this.perPage = perPage;
      this.currentPage = 1; // Volta para a primeira página ao mudar itens por página
    },

    removeCategory() {
      this.selectedCategory = null;
      this.selectedCategoryObject = null;
      this.selectedCourse = null; // Limpa seleção de curso
      this.courseOptions = []; // Limpa opções de curso

      // Reseta a paginação
      this.coursesPage = 1;
      this.coursesTotalPages = 1;
      this.hasMoreCourses = false;

      // Recarregar as opções de cursos sem filtro de categoria
      this.loadCurrentCourses();
    },
  },
};
</script>

<style lang="scss" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #212529;
  border-radius: 4px;
  width: 100%;
  max-width: 800px;
  margin: 1rem;
  display: flex;
  flex-direction: column;
  max-height: 90vh;

  @media (max-width: 767px) {
    max-width: 95vw;
    max-height: 95vh;
    margin: 0.5rem;
    border-radius: 8px;
  }

  @media (max-width: 480px) {
    max-width: 98vw;
    max-height: 98vh;
    margin: 0.25rem;
  }
}

.modal-header {
  padding: 1rem;
  border-bottom: 1px solid #343a40;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h2 {
    font-size: 1.25rem;
    color: #fff;
    margin: 0;

    @media (max-width: 480px) {
      font-size: 1.1rem;
    }
  }

  .close-button {
    background: none;
    border: none;
    color: #fff;
    cursor: pointer;
    font-size: 1.25rem;
    padding: 0.5rem;
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      color: var(--primary);
    }

    @media (max-width: 480px) {
      font-size: 1.1rem;
      min-width: 40px;
      min-height: 40px;
    }
  }

  @media (max-width: 480px) {
    padding: 0.75rem;
  }
}

.modal-body {
  padding: 1rem;
  flex: 1;
  overflow-y: auto;
  max-height: 70vh;

  .section-title {
    color: var(--primary);
    font-size: 1rem;
    font-weight: bold;
    margin-bottom: 1rem;

    @media (max-width: 480px) {
      font-size: 0.9rem;
      margin-bottom: 0.75rem;
    }
  }

  @media (max-width: 480px) {
    padding: 0.75rem;
  }
}

.search-section {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;

  @media (max-width: 767px) {
    flex-direction: column;
    gap: 0.75rem;
  }

  @media (max-width: 480px) {
    gap: 0.5rem;
    margin-bottom: 0.75rem;
  }
}

.search-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;

  .search-button {
    background-color: var(--primary);
    border: none;
    color: #fff;
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    height: 38px;
    width: 38px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: none;

    &:hover {
      background-color: #0b5ed7;
    }
  }

  @media (max-width: 767px) {
    flex: none;
    width: 100%;
  }
}

.table-container {
  margin-bottom: 1rem;

  .empty-preview-message {
    background-color: #343a40;
    padding: 2rem;
    text-align: center;
    border-radius: 4px;

    p {
      color: #adb5bd;
      font-style: italic;
      margin: 0;
    }
  }
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

/* Os estilos de .btn-action foram movidos para global.scss */
.btn-action {
  padding: 0.5rem;

  i {
    font-size: 1.25rem; /* Mesmo tamanho do ícone de fechar */
  }
}

.modal-footer {
  padding: 1rem;
  border-top: 1px solid #343a40;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  flex-shrink: 0;

  @media (max-width: 767px) {
    flex-direction: column-reverse;
    gap: 0.75rem;

    :deep(.custom-button) {
      width: 100%;
      justify-content: center;
    }
  }

  @media (max-width: 480px) {
    padding: 0.75rem;
    gap: 0.5rem;
  }
}
</style>

<style lang="scss">
/* Estilos responsivos para componentes internos */
.modal-content {
  /* Autocomplete responsivo */
  :deep(.autocomplete-wrapper) {
    width: 100%;

    .input-container {
      width: 100%;
      max-width: none;

      input {
        width: 100%;
        max-width: none;
      }
    }
  }

  /* Tabela responsiva */
  :deep(.custom-table) {
    @media (max-width: 767px) {
      font-size: 0.875rem;

      th, td {
        padding: 0.5rem 0.25rem;
      }

      .action-buttons {
        justify-content: center;
      }
    }

    @media (max-width: 480px) {
      font-size: 0.8rem;

      th, td {
        padding: 0.375rem 0.125rem;
      }
    }
  }

  /* Paginação responsiva */
  :deep(.pagination) {
    @media (max-width: 767px) {
      flex-wrap: wrap;
      justify-content: center;
      gap: 0.25rem;

      .pagination-info {
        order: -1;
        width: 100%;
        text-align: center;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
      }
    }

    @media (max-width: 480px) {
      .pagination-controls {
        flex-wrap: wrap;
        justify-content: center;
      }

      .pagination-info {
        font-size: 0.8rem;
      }
    }
  }

  /* Botões de ação responsivos */
  :deep(.btn-action) {
    @media (max-width: 480px) {
      padding: 0.375rem;
      min-width: 36px;
      min-height: 36px;

      i {
        font-size: 1rem;
      }
    }
  }
}

.modal-content .custom-input {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}
</style>
