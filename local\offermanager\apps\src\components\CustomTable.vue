<template>
  <div class="table-responsive">
    <table class="table">
      <thead>
        <tr>
          <th
            v-for="header in headers"
            :key="header.value"
            :class="{
              'text-right': header.align === 'right',
            }"
            :style="header.width ? { width: header.width } : {}"
            :data-value="header.value"
          >
            <slot v-if="header.value === 'select'" name="header-select">
              {{ header.text }}
            </slot>
            <template v-else>
              {{ header.text }}
              <span
                @click="header.sortable ? handleSort(header.value) : null"
                v-if="header.sortable"
                class="sort-icon"
              >
                <i
                  class="fas"
                  :class="{
                    'fa-sort': sortBy !== header.value,
                    'fa-sort-up': sortBy === header.value && !sortDesc,
                    'fa-sort-down': sortBy === header.value && sortDesc,
                  }"
                ></i>
              </span>
            </template>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="item in items" :key="item.id">
          <td
            v-for="header in headers"
            :key="header.value"
            :class="{ 'text-right': header.align === 'right' }"
            :data-column="header.value"
          >
            <slot :name="'item-' + header.value" :item="item">
              {{ item[header.value] }}
            </slot>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
export default {
  name: "CustomTable",

  props: {
    headers: {
      type: Array,
      required: true,
    },
    items: {
      type: Array,
      required: true,
    },
    sortBy: {
      type: String,
      default: "",
    },
    sortDesc: {
      type: Boolean,
      default: false,
    },
  },

  methods: {
    handleSort(value) {
      this.$emit("sort", {
        sortBy: value,
        sortDesc: this.sortBy === value ? !this.sortDesc : false,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.table-responsive {
  width: 100%;
  overflow: visible !important; /* Alterado para visible para permitir que elementos como selects sejam exibidos corretamente */
}

.table {
  width: 100%;
  border-collapse: collapse;
  background-color: #212529 !important;
  overflow: visible !important; /* Alterado para visible para permitir que elementos como selects sejam exibidos corretamente */

  thead {
    tr {
      border-bottom: 3px solid var(--primary) !important;
    }
  }

  th,
  td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #373b3e !important;
    color: #fff !important;
    height: 50px;
    white-space: nowrap;
    position: relative !important;
    overflow: visible !important; /* Alterado para visible para permitir que elementos como selects sejam exibidos corretamente */
  }

  th {
    font-weight: 700;
    text-transform: uppercase;
    font-size: 16px;
    color: var(--primary) !important;
    border: none;

    .sort-icon {
      margin-left: 5px;
      color: #ced4da;
      cursor: pointer;
    }
  }

  tbody tr:nth-child(odd) {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }

  td {
    font-size: 16px;
    font-weight: 400;
  }
}

.status-active {
  color: inherit;
}

.status-inactive {
  color: inherit;
}

tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.15) !important;
}

.text-right {
  text-align: right !important;
}
</style>
