{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template local_offermanager/extension_modal

    TODO describe template extension_modal

    Example context (json):
    {
    }
}}

{{#js}}
    require(['jquery', 'core_form/modalform', 'core/notification', 'core/modal_factory'], function($, ModalForm, Notification, ModalFactory) {
        let offeruserenrol = {{ offeruserenrol }};
        let error = "{{ error }}";
        let $button = $('a.dropdown-item[data-key="extensionbutton"]');

		$button.on('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            if(error){
                ModalFactory.create({
                    type: ModalFactory.types.ALERT,
                    title: "{{#str}} error:cannot_extend_enrol, local_offermanager {{/str}}",
                    body: error,
                    show: true,
                    removeOnClose: true,
                }).then(function(modal) {
                    modal.show();
                });

                return;
            }

            const modalForm = new ModalForm({
                formClass: "local_offermanager_self_extension_form",
                args: {id: offeruserenrol},
                modalConfig: {
                    title: $button.text(),
                    removeOnClose: true,
                },
                saveButtonText: "{{#str}} confirm, core {{/str}}",
                returnFocus: $button[0],
            });

            modalForm.addEventListener(modalForm.events.SERVER_VALIDATION_ERROR,(event) => {
                    Notification.addNotification({
                        type: 'error',
                        message: event.toString()
                    });
            });
            modalForm.addEventListener(modalForm.events.FORM_SUBMITTED,(event) => {
                if (event.detail.result) {
                    const notificationType = event.detail.is_warning ? 'warning' : 'success';

                    Notification.addNotification({
                        type: notificationType,
                        message: event.detail.message
                    });

                    setTimeout(
                        function(){
                            window.location.reload();
                        },
                        2000
                    );
                } else {
                    Notification.addNotification({
                        type: 'error',
                        message: "{{#str}} error:cannot_extend_offer_user_enrol , local_offermanager {{/str}}"
                    });
                }
            });

            modalForm.show();
        });

    });
{{/js}}