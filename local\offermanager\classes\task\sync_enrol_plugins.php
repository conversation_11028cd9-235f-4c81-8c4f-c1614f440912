<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Offer Manager enrolment plugins sync task.
 *
 * @package    local_offermanager
 * @copyright  YYYY YOURNAME
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace local_offermanager\task;

defined('MOODLE_INTERNAL') || die();

/**
 * Offer Manager enrolment plugins sync task.
 *
 * @package    local_offermanager
 * @copyright  YYYY YOURNAME
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class sync_enrol_plugins extends \core\task\scheduled_task {

    /**
     * Get the name of the task.
     *
     * @return string
     */
    public function get_name() {
        return get_string('task:syncenrolplugins', 'local_offermanager');
    }

    /**
     * Execute the task.
     *
     * @return void
     */
    public function execute() {

        if(!get_config('local_offermanager', 'enableplugin')){
            mtrace('Offer Manager is disabled, we will skip it!');
            return true;
        }

        mtrace('Starting sync of dependent enrolment plugins...');

        $dependentplugins = \local_offermanager\enrol_setup::get_dependent_enrol_plugins();

        if (empty($dependentplugins)) {
            mtrace('No dependent enrolment plugins found to sync.');
            return;
        }

        foreach ($dependentplugins as $pluginname) {
            mtrace("Syncing enrolment plugin: {$pluginname}");
            try {
                $plugin = enrol_get_plugin($pluginname);
                if (method_exists($plugin, 'sync')) {
                    $plugin->sync(new \null_progress_trace());
                    mtrace("Successfully synced enrolment plugin: {$pluginname}");
                } else {
                    mtrace("Plugin {$pluginname} does not have a sync() method.");
                }
            } catch (\Exception $e) {
                mtrace("Error syncing enrolment plugin {$pluginname}: " . $e->getMessage());
            }
        }

        mtrace('Finished sync of dependent enrolment plugins.');
    }
}