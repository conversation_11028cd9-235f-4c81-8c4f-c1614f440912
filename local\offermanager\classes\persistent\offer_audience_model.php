<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON>le is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\persistent;

use core\persistent;
use moodle_exception;
use local_audience\repository as audience_repository;
use local_offermanager\event\offer_audience_created;
use local_offermanager\event\offer_audience_updated;
use local_offermanager\event\offer_audience_deleted;

defined('MOODLE_INTERNAL') || die();

/**
 * Class offer_audience_model
 *
 * @package    local_offermanager
 * @copyright  2025 2015 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class offer_audience_model extends persistent
{
    const TABLE = 'local_offermanager_audience';

    protected static function define_properties()
    {
        return [
            'offerid' => [
                'type' => PARAM_INT,
                'description' => 'ID da oferta',
            ],
            'audienceid' => [
                'type' => PARAM_INT,
                'description' => 'ID do público-alvo',
            ],
            'usercreated' => [
                'type' => PARAM_INT,
                'description' => 'User id do creador',
                'null' => NULL_NOT_ALLOWED,
                'default' => function () {
                    global $USER;

                    return $USER->id;
                }
            ]
        ];
    }

    /**
     * Valida o campo offerid.
     *
     * @return bool
     */
    protected function validate_offerid($value): bool
    {
        return offer_model::record_exists($value);
    }

    /**
     * Valida o campo audienceid.
     *
     * @return bool
     */
    protected function validate_audienceid($value): bool
    {
        try {
            audience_repository::get_audience_by_id($value);
        }
        catch(moodle_exception $e){
            $exception_class = $e::class;
            match($exception_class) {
                'dml_missing_record_exception' => throw new moodle_exception('error:audience_not_found', 'local_offermanager')
            };
        }

        return true;
    }

    /**
     * Valida a combinação única de offerid e audienceid.
     *
     * @return bool
     */
    protected function validate_unique_combination(): bool
    {
        $params = [
            'offerid' => $this->get('offerid'),
            'audienceid' => $this->get('audienceid'),
        ];

        $query = 'offerid = :offerid AND audienceid = :audienceid';

        if ($this->get('id')) {

            $params['id'] = $this->get('id');
            $query .= ' AND id <> :id';
        }

        return !self::record_exists_select($query, $params);
    }

    /**
     * Retorna a oferta associada a este registro.
     *
     * @return offer_model
     */
    public function get_offer()
    {
        return new offer_model($this->get('offerid'));
    }

    protected function before_validate()
    {
        if (!$this->validate_unique_combination()) {
            throw new moodle_exception('error:duplicate_offer_audience', 'local_offermanager');
        }
    }


    protected function after_create()
    {
        $event = offer_audience_created::instance($this);
        $event->trigger();
    }

    protected function after_update($result)
    {
        if ($result) {
            $event = offer_audience_updated::instance($this);
            $event->trigger();
        }
    }

    protected function before_delete()
    {
        $event = offer_audience_deleted::instance($this);
        $event->trigger();
    }
}
