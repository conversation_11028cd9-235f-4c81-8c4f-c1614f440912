<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\classes\persistent;

use advanced_testcase;
use local_offermanager\persistent\offer_audience_model;
use local_offermanager\persistent\offer_model;
use local_offermanager\event\offer_audience_created;
use local_offermanager\event\offer_audience_updated;
use local_offermanager\event\offer_audience_deleted;
use moodle_exception;

/**
 * Tests for Offer Manager
 *
 * @package    local_offermanager
 * @category   test
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
final class offer_audience_test extends advanced_testcase
{
    public function test_validate_audienceid_with_nonexisting_audienceid()
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(
            0,
            (object)
            [
                'name' => 'Test Offer'
            ]
        );

        $offer->save();

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:audience_not_found', 'local_offermanager'));

        $offer->add_audience(999);
    }

    public function test_validate_unique_combination_with_duplicate()
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) ['name' => 'Oferta Teste']);
        $offer->save();

        $audienceid = $DB->insert_record(
            'local_audience_audiences',
            [
                'name' => 'Público-alvo 1'
            ]
        );

        $offer->add_audience($audienceid);

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:duplicate_offer_audience', 'local_offermanager'));

        $offer->add_audience($audienceid);
    }

    public function test_after_create_triggers_event()
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) ['name' => 'Oferta Teste']);
        $offer->save();

        $audienceid = $DB->insert_record(
            'local_audience_audiences',
            [
                'name' => 'Público-alvo'
            ]
        );

        $sink = $this->redirectEvents();

        $offer->add_audience($audienceid);
        $events = $sink->get_events();
        $sink->close();

        $this->assertCount(1, $events);
        $this->assertInstanceOf(offer_audience_created::class, $events[0]);
    }

    public function test_before_delete_triggers_event()
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) ['name' => 'Oferta Teste']);
        $offer->save();

        $audienceid = $DB->insert_record(
            'local_audience_audiences',
            [
                'name' => 'Público-alvo'
            ]
        );

        $offer_audience = $offer->add_audience($audienceid);

        $sink = $this->redirectEvents();
        $offer_audience->delete();
        $events = $sink->get_events();
        $sink->close();

        $this->assertCount(1, $events);
        $this->assertInstanceOf(offer_audience_deleted::class, $events[0]);
    }

    public function test_get_offer()
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) ['name' => 'Oferta Teste']);
        $offer->save();

        $audienceid = $DB->insert_record(
            'local_audience_audiences',
            [
                'name' => 'Público-alvo'
            ]
        );

        $offer_audience = $offer->add_audience($audienceid);

        $this->assertEquals($offer->get('id'), $offer_audience->get_offer()->get('id'));
    }
}
