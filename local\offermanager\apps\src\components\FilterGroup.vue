<template>
  <div class="filter-group" :class="{ 'checkbox-group': isCheckbox }">
    <div v-if="label" class="filter-label">{{ label }}</div>
    <div class="filter-input">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FilterGroup',

  props: {
    label: {
      type: String,
      default: ''
    },
    isCheckbox: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 0;

  &.checkbox-group {
    flex-direction: row;
    align-items: flex-end;
    height: 100%;
    margin-left: 10px;
    min-width: auto; /* Garante que o grupo de checkbox não seja afetado pela largura mínima do Autocomplete */
    width: auto; /* Permite que o grupo de checkbox tenha apenas a largura necessária */

    /* Tablet e celular - mover checkbox para a esquerda */
    @media (max-width: 1024px) {
      margin-left: 0;
      order: -1;
      margin-bottom: 8px;
      align-self: flex-start;
    }
  }

  @media (max-width: 1024px) {
    width: 100%;
    margin-right: 0;

    &.checkbox-group {
      width: auto;
      align-self: flex-start;
    }
  }

  @media (max-width: 768px) {
    width: 100%;
    margin-right: 0;

    &.checkbox-group {
      width: auto;
      align-self: flex-start;
    }
  }
}

.filter-label {
  font-size: 14px;
  color: #fff !important;
  margin-bottom: 8px;
  font-weight: 500;
}

.filter-input {
  position: relative;
  display: flex;
  align-items: center;
  width: auto;

  input,
  select {
    padding: 10px 12px;
    background-color: #212529 !important;
    border: 1px solid #495057 !important;
    border-radius: 4px;
    color: #fff !important;
    height: 42px;
    font-size: 15px;
    font-weight: 400;
  }

  input {
    max-width: 336px;

    /* Ajuste para celular específico (390x844) */
    @media (max-width: 390px) {
      max-width: 100%;
      width: 100%;
    }
  }

  select {
    max-width: 144px;

    /* Ajuste para tablet e celular */
    @media (max-width: 1024px) {
      max-width: 100%;
      width: 100%;
    }
  }

  input::placeholder,
  select::placeholder {
    color: #6c757d !important;
  }
}
</style>