<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle. If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\persistent;

use core\persistent;
use moodle_exception;

defined('MOODLE_INTERNAL') || die();

/**
 * Class offer_extension_reason_model
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class offer_extension_reason_model extends persistent
{
    /** @var string The name of the database table this persistent maps to. */
    const TABLE = 'local_offermanager_extension';

    /**
     * Define the properties of this persistent class.
     *
     * @return array The property definitions.
     */
    protected static function define_properties()
    {
        return [
            'offeruserenrolid' => [
                'type' => PARAM_INT,
                'null' => NULL_NOT_ALLOWED,
                'description' => 'O ID do modelo offer_user_enrol_model relacionado.',
            ],
             'ueid' => [
                'type' => PARAM_INT,
                'null' => NULL_NOT_ALLOWED,
                'description' => 'ID da inscrição (user_enrolments) relacionada.',
            ],
            'reason' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'description' => 'Motivo da prorrogação.',
            ],
            'usercreated' => [
                'type' => PARAM_INT,
                'description' => 'User id do criador',
                'null' => NULL_NOT_ALLOWED,
                'default' => function () {
                    global $USER;
                    return $USER->id;
                }
            ],
        ];
    }

    /**
     * Validates the offeruserenrolid field.
     *
     * @param int $value The offer_user_enrol_model ID to validate.
     * @return bool Returns true if valid or throws an exception.
     */
    protected function validate_offeruserenrolid(int $value): bool
    {
        if (!$value || !offer_user_enrol_model::record_exists($value)) {
            throw new moodle_exception('error:invalid_offer_user_enrol', 'local_offermanager');
        }
        return true;
    }

     /**
     * Validates the ueid field.
     *
     * @param int $value The user enrolment ID to validate.
     * @return bool Returns true if valid or throws an exception.
     */
    protected function validate_ueid(int $value): bool
    {
        global $DB;
        if (!$value || !$DB->record_exists('user_enrolments', ['id' => $value])) {
            throw new moodle_exception('error:user_enrol_not_found', 'local_offermanager');
        }
        return true;
    }
}