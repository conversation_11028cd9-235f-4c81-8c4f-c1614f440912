<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

use local_offermanager\enrol_setup;
use local_offermanager\persistent\offer_class_model;
use local_offermanager\persistent\offer_user_enrol_model;
use local_offermanager\constants;
use core\notification;


/**
 * Callback implementations for Classes
 *
 * @package    local_offermanager
 * @copyright  2024 REVVO <www.somosrevvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
if (!defined('CONTEXT_OFFER_CLASS')) {
    define('CONTEXT_OFFER_CLASS', 110);
}

/**
 * Callback implementations for Offer Manager
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

function local_offermanager_after_require_login()
{
    global $CFG;

    require_once("{$CFG->dirroot}/local/offermanager/classes/enrol_setup.php");

    if (is_siteadmin()) {
        enrol_setup::handle_enrol_plugins();
    }
}

/**
 * Executes at the end of the setup.php.
 * The accesslib.php is already loaded at this point.
 *
 * @return void
 */
function local_offermanager_after_config()
{
    local_offermanager_init_custom_context();
}


function local_offermanager_add_menubar_icon()
{
    global $PAGE;

    if (!get_config("local_offermanager", "enableplugin") || !has_capability('local/offermanager:manage', \context_system::instance()) || isguestuser()) {
        return false;
    }

    return (object)[
        "name" => get_string('pluginname', 'local_offermanager'),
        "icon" => 'icon-offermanager',
        "url" => new \moodle_url("/local/offermanager/index.php"),
        "active" => $PAGE->pagetype == "local-offermanager" ? 'active' : '',
        "order" => 7
    ];
}

function local_offermanager_extend_navigation_course(\navigation_node $navigation, \stdClass $course, \context $context)
{
    global $CFG, $PAGE, $USER, $COURSE, $OUTPUT;

    if (has_capability('moodle/course:view', $context)) {
        return true;
    }

    require_once("{$CFG->dirroot}/enrol/locallib.php");

    if ($COURSE && isset($USER->enrol['enrolled'][$COURSE->id]) && $USER->enrol['enrolled'][$COURSE->id] > time()) {

        $course_enrolment_manager = new course_enrolment_manager(
            $PAGE,
            $COURSE,
            null,
            0,
            '',
            0,
            0
        );

        $offer_plugins = enrol_setup::get_dependent_enrol_plugins();

        $enrolments = $course_enrolment_manager->get_user_enrolments($USER->id);

        if (!$enrolments) {
            return true;
        }

        $offer_enrolments = array_filter(
            $enrolments,
            function ($enrolment) use ($offer_plugins) {
                return in_array($enrolment->enrolmentinstance->enrol, $offer_plugins);
            }
        );

        // other methods allow access, so user can see course
        if (count($enrolments) > count($offer_enrolments)) {
            return true;
        }

        foreach ($offer_enrolments as $ue) {
            $offer_class = offer_class_model::get_by_enrolid($ue->enrolid);
            $minusers = $offer_class->get_mapped_field('minusers');

            if (
                $minusers
                && $offer_class
                && $offer_class->is_user_enrolled_and_active($USER->id)
                && !$offer_class->is_accessible()
            ) {

                redirect(new moodle_url(
                    '/local/offermanager/notaccessible.php',
                    [
                        'offerclassid' => $offer_class->get('id'),
                        'backurl' => $PAGE->url
                    ]
                ));
            }
        }
    }

    $offer_user_enrol = offer_user_enrol_model::get_active_offer_user_enrol($USER->id, $COURSE->id);

    if ($offer_user_enrol && $offer_user_enrol->can_view_extension_modal()) {

        $params = ['offeruserenrol' => $offer_user_enrol->get('id')];
        $url = new moodle_url('/local/offermanager/requestextension.php', $params);
        $navigation->add(
            get_string('extendenrol', 'local_offermanager'),
            $url,
            navigation_node::TYPE_COURSE,
            null,
            'extensionbutton',
            null
        );

        $can_request_extension = $offer_user_enrol->can_request_extension();

        if (is_string($can_request_extension)) {
            $params['error'] = $can_request_extension;
        }
        echo $OUTPUT->render_from_template(
            'local_offermanager/extension_modal',
            $params
        );
    }

    // if ($offer_user_enrol && $offer_user_enrol->can_reenrol()) {
    //     if (in_array($offer_user_enrol->get('situation'), [constants::OFFER_USER_ENROL_SITUATION_APPROVED, constants::OFFER_USER_ENROL_SITUATION_COMPLETED])) {

    //         $offerclass = $offer_user_enrol->get_offer_class();
    //         $params = [
    //             'offeruserenrol' => $offer_user_enrol->get('id'),
    //             'courseid' => $offer_user_enrol->get('courseid')
    //         ];
    //         $url = new moodle_url('/local/offermanager/reenrrol.php', $params);
    //         $navigation->add(
    //             get_string('reenrolme', 'local_offermanager', $offerclass->get_name()),
    //             $url,
    //             navigation_node::TYPE_COURSE,
    //             null,
    //             'reenrolbutton',
    //             null
    //         );

    //         echo $OUTPUT->render_from_template(
    //             'local_offermanager/reenrol_modal',
    //             $params
    //         );
    //     }
    // }
}

/**
 * Appends the required custom contexts to the
 * related config, if necessary
 *
 * @return void
 */
function local_offermanager_init_custom_context()
{
    $custom_context_class_path = 'context_offer_class';
    $customcontexts = unserialize(get_config('core', 'custom_context_classes')) ?: [];
    // Appending custom context
    if (!in_array($custom_context_class_path, $customcontexts)) {
        $customcontexts[CONTEXT_OFFER_CLASS] = $custom_context_class_path;

        set_config('custom_context_classes', serialize($customcontexts));

        // Reseting "static" context levels
        context_helper::reset_levels();
    }
}


function local_offermanager_add_menubar_client_menu()
{
    global $PAGE;

    return [
        "name" => get_string('pluginname', 'local_offermanager'),
        "url" => new \moodle_url("/local/offermanager/index.php"),
        "active" => strstr($PAGE->url, "local/offermanager/") ? "active" : "",
    ];
}

/**
 * Class context_offer_class
 *
 * @package    local_offermanager
 * @copyright  Seu Copyright
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class context_offer_class extends \context
{
    const PLUGIN = 'local_offermanager';

    /**
     * Please use context_offer_class::instance($classid) if you need the instance of context.
     * Alternatively if you know only the context id use context::instance_by_id($contextid)
     *
     * @param stdClass $record
     */
    protected function __construct(stdClass $record)
    {
        parent::__construct($record);
        if ($record->contextlevel != CONTEXT_OFFER_CLASS) {
            throw new coding_exception('Invalid $record->contextlevel in context_offer_class constructor.');
        }
    }

    /**
     * Returns human readable context level name.
     *
     * @static
     * @return string the human readable context level name.
     */
    public static function get_level_name()
    {
        return get_string('offer_class', self::PLUGIN);
    }

    public static function get_short_name(): string
    {
        return get_string('pluginname', self::PLUGIN);
    }

    /**
     * Returns human readable context identifier.
     *
     * @param boolean $withprefix whether to prefix the name of the context with the
     *      type of context, e.g. User, Course, Forum, etc.
     * @param boolean $short whether to use the short name of the thing. Only applies
     *      to course contexts
     * @param boolean $escape Whether the returned name of the thing is to be
     *      HTML escaped or not.
     * @return string the human readable context name.
     */
    public function get_context_name($withprefix = true, $short = false, $escape = true)
    {
        $name = '';
        if ($class = offer_class_model::get_record(array('id' => $this->_instanceid))) {
            $name = format_string($class->get_name(), true, array('context' => $this));
            if ($withprefix) {
                $name = get_string('offer_class', self::PLUGIN) . ': ' . $name;
            }
        }

        if ($escape) {
            $name = s($name);
        }

        return $name;
    }

    /**
     * Returns the most relevant URL for this context.
     *
     * @return moodle_url
     */
    public function get_url()
    {
        return new moodle_url('/local/offermanager/index.php', array('id' => $this->_instanceid));
    }

    /**
     * Returns array of relevant context capability records.
     *
     * @param string $sort
     * @return array
     */
    public function get_capabilities(string $sort = self::DEFAULT_CAPABILITY_SORT)
    {
        global $DB;

        return $DB->get_records_list(
            'capabilities',
            'contextlevel',
            [
                CONTEXT_OFFER_CLASS,
            ],
            $sort
        );
    }

    /**
     * Returns offer class context instance.
     *
     * @static
     * @param int $classid id from the local_offermanager_class table
     * @param int $strictness
     * @return static context instance
     */
    public static function instance($classid, $strictness = MUST_EXIST)
    {
        global $DB;

        if ($context = context::cache_get(CONTEXT_OFFER_CLASS, $classid)) {
            return $context;
        }

        $record = $DB->get_record(
            'context',
            [
                'contextlevel' => CONTEXT_OFFER_CLASS,
                'instanceid' => $classid
            ]
        );

        if (!$record) {
            if ($class = $DB->get_record('local_offermanager_class', array('id' => $classid), 'id', $strictness)) {
                $record = context::insert_context_record(
                    CONTEXT_OFFER_CLASS,
                    $class->id,
                    '/' . SYSCONTEXTID
                );
            }
        }

        if ($record) {
            $context = new static($record);
            context::cache_add($context);
            return $context;
        }

        return null;
    }
}

/**
 * Class local_offermanager_self_unenrol_form
 *
 * Form for user self-unenrollment confirmation.
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class local_offermanager_self_unenrol_form extends local_offermanager\form\self_unenrol_form {}

/**
 * Class local_offermanager_extension_form
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class local_offermanager_self_extension_form extends local_offermanager\form\self_extension_form {}


/**
 * Class local_offermanager_self_reenrol_form
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class local_offermanager_self_reenrol_form extends local_offermanager\form\self_reenrol_dynamic_form {}
