<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\persistent\trait;

use local_offermanager\constants;
use local_offermanager\event\offer_class_cycle_started;
use local_offermanager\event\offer_class_cycle_finished;
use local_offermanager\task\update_operational_cycle_on_class_starttime;
use local_offermanager\task\update_operational_cycle_on_class_endtime;

defined('MOODLE_INTERNAL') || die();

/**
 * Trait class_operational_cycle_trait
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
trait class_operational_cycle_trait {
    /**
     * Calcula o ciclo operacional da turma da oferta.
     *
     * @return int
     */
    public function calculate_operational_cycle()
    {
        $currenttime = time();

        $starttime = (int) $this->get_mapped_field('startdate');

        $enableenddate = (bool) $this->get_mapped_field('enableenddate');
        $endtime =  $enableenddate ? (int) $this->get_mapped_field('enddate') : 0;

        $course = $this->get_course();
        $coursestartdate = (int) $course->startdate;
        $courseenddate = (int) $course->enddate;

        if ($endtime > 0 && $currenttime >= $endtime) {
            return constants::OFFER_CLASS_OPERATIONAL_CYCLE_FINISHED;
        }

        $is_course_period_valid = $coursestartdate == 0 || $coursestartdate <= $starttime;
        $is_course_end_valid = $courseenddate == 0 || ($courseenddate > $starttime && $courseenddate > $currenttime);
        $is_enrol_end_valid = $endtime == 0 || ($endtime > $starttime && (!$courseenddate || $endtime < $courseenddate));

        if (
            $starttime > 0
            && $currenttime >= $starttime
            && $is_course_period_valid
            && $is_course_end_valid
            && $is_enrol_end_valid
            && $course->visible
        ) {
            return constants::OFFER_CLASS_OPERATIONAL_CYCLE_STARTED;
        }

        return constants::OFFER_CLASS_OPERATIONAL_CYCLE_NOT_STARTED;
    }

    /**
     * Define o ciclo operacional da turma da oferta.
     *
     * @return bool
     */
    public function update_operational_cycle(): bool
    {
        $instance_cycle = (int) $this->get('operational_cycle');

        $actualcycle = $this->calculate_operational_cycle();

        if ($instance_cycle === $actualcycle) {
            return false;
        }

        $this->set('operational_cycle', $actualcycle);
        $this->save();

        switch($actualcycle) {
            case constants::OFFER_CLASS_OPERATIONAL_CYCLE_STARTED:
                offer_class_cycle_started::create_from_instance($this)->trigger();
                break;
            case constants::OFFER_CLASS_OPERATIONAL_CYCLE_FINISHED:
                $ongoing_offer_user_enrols = $this->get_ongoing_offer_user_enrols();
                if ($ongoing_offer_user_enrols) {
                    foreach ($ongoing_offer_user_enrols as $ongoing_offer_user_enrol) {
                        $ongoing_offer_user_enrol->process_expiration();
                    }
                }
                offer_class_cycle_finished::create_from_instance($this)->trigger();
                break;
        }

        return true;
    }

    /**
     * Remove tarefas agendadas relacionadas à turma
     *
     * @return void
     */
    public function remove_related_adhoc_tasks(): void
    {
        global $DB;

        $offerclassid = (int)$this->get('id');

        $sql = "DELETE FROM {task_adhoc}
            WHERE component = :component
                AND classname IN (:starttask, :endtask)
                AND customdata LIKE :offerclassid"
        ;

        $params = [
            'component' => 'local_offermanager',
            'starttask' => '\\' . update_operational_cycle_on_class_starttime::class,
            'endtask' => '\\' . update_operational_cycle_on_class_endtime::class,
            'offerclassid' => "%\"offerclassid\":" . $offerclassid . '%'
        ];

        $DB->execute($sql, $params);
    }

    /**
     * Verifica se a turma já iniciou seu período operacional
     * 
     * @return bool
     */
    public function has_started()
    {
        return $this->get('operational_cycle') > constants::OFFER_CLASS_OPERATIONAL_CYCLE_NOT_STARTED;
    }

    /**
     * Verifica se a turma está no período operacional ativo
     * 
     * @return bool
     */
    public function is_started()
    {
        return $this->get('operational_cycle') == constants::OFFER_CLASS_OPERATIONAL_CYCLE_STARTED;
    }

    /**
     * Verifica se o período operacional da turma já terminou
     * 
     * @return bool
     */
    public function is_finished()
    {
        return $this->get('operational_cycle') == constants::OFFER_CLASS_OPERATIONAL_CYCLE_FINISHED;
    }
}
