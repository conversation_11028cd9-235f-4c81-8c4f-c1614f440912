<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\persistent\trait;

use core_date;

defined('MOODLE_INTERNAL') || die();

/**
 * Trait class_availability_trait
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
trait class_availability_trait
{
    public function in_preenrol_period()
    {
        $now = time();

        $preenrol = $this->get_preenrol_timestamps();

        if (($preenrol->startdate && $now <= $preenrol->startdate) || ($preenrol->enddate && $now > $preenrol->enddate)) {
            return false;
        }

        return true;
    }

    public function get_preenrol_timestamps()
    {
        $enablepreenrolment = (bool) $this->get_mapped_field('enablepreenrolment');
        $preenrolmentstartdate = (int) $this->get_mapped_field('startdate');
        $preenrolmentenddate = (int) $this->get_mapped_field('enddate');

        if ($enablepreenrolment) {
            $preenrolmentstartdate = (int) $this->get_mapped_field('preenrolmentstartdate');
            $preenrolmentenddate = (int) $this->get_mapped_field('preenrolmentenddate');
        }

        return (object) [
            'startdate' => $preenrolmentstartdate,
            'enddate' => $preenrolmentenddate
        ];
    }

    public function is_available()
    {
        return count($this->get_unavailability_reasons(false)) === 0;
    }

    public function get_unavailability_reasons($commonuser = true)
    {
        $reasons = [];

        if (!$this->in_preenrol_period()) {
            $userstimezone = core_date::get_user_timezone();

            $format = get_string('strftimedatetimeshort', 'langconfig');
            $preenrol = $this->get_preenrol_timestamps();

            $preenrol->startdate = userdate($preenrol->startdate, $format, $userstimezone);

            $preenrol->enddate = $preenrol->enddate ? userdate($preenrol->enddate, $format, $userstimezone) : null;

            if ($preenrol->enddate) {
                $reasons[] = get_string('error:preenrol_starts_between', 'local_offermanager', $preenrol);
            } else {

                $reasons[] = get_string('error:preenrol_starts_on', 'local_offermanager', $preenrol);
            }
        }

        $maxusers = $this->get_mapped_field('maxusers');

        if ($maxusers) {
            $total_user_enrolments = $this->count_user_enrolments();
            if ($total_user_enrolments && $total_user_enrolments >= $maxusers) {
                $reasons[] = get_string('error:reached_max_users', 'local_offermanager');
            }
        }

        if ($commonuser && $reasons) {
            return $reasons;
        }

        $offer = $this->get_offer();

        if (!$offer->is_active()) {
            $reasons[] = get_string('error:offer_not_active', 'local_offermanager');
        } else {
            $offer_course = $this->get_offer_course();
            if (!$offer_course->is_active()) {
                $reasons[] = get_string('error:offercourse_not_active', 'local_offermanager');
            } else {
                if (!$this->is_active()) {
                    $reasons[] = get_string('error:offerclass_not_active', 'local_offermanager');
                }
            }
        }

        return $reasons;
    }
}
