<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle. If not, see <http://www.gnu.org/licenses/>.

require_once('../../config.php');
require_once($CFG->dirroot . '/local/offermanager/classes/persistent/offer_user_enrol_model.php');
require_once($CFG->dirroot . '/local/offermanager/classes/form/self_extension_form.php');

use local_offermanager\persistent\offer_user_enrol_model;
use local_offermanager\form\self_extension_form;
use core\output\notification;
use moodle_exception;
use context_course;

$id = required_param('id', PARAM_INT); // ID do local_offermanager_ue

require_login();

// Verificar se o usuário tem permissão para acessar esta página
$offeruserenrol = offer_user_enrol_model::get_record(['id' => $id], MUST_EXIST);

// Verificar se o usuário logado é o proprietário da inscrição.
if ($offeruserenrol->get('userid') !== $USER->id) {
    throw new moodle_exception('nopermissions', 'local_offermanager');
}

// Verificar se o usuário pode solicitar prorrogação
$can_request = $offeruserenrol->can_request_extension();
if ($can_request !== true) {
    // Obter a URL do curso para redirecionamento.
    $courseurl = new moodle_url('/course/view.php', ['id' => $offeruserenrol->get('courseid')]);
    redirect($courseurl, $can_request, notification::NOTIFY_ERROR);
}

// Configurar o contexto da página
$courseid = $offeruserenrol->get('courseid');
$course = get_course($courseid);
$context = context_course::instance($courseid);

// Configurar a página
$PAGE->set_context($context);
$PAGE->set_course($course);
$PAGE->set_url(new moodle_url('/local/offermanager/requestextension.php', ['id' => $id]));
$PAGE->set_title(get_string('extendenrol', 'local_offermanager'));
$PAGE->set_heading(format_string($course->fullname));

// Criar o formulário
$form = new self_extension_form(null, ['id' => $id, 'userid' => $USER->id]);

// Obter a URL do curso para redirecionamento.
$courseurl = new moodle_url('/course/view.php', ['id' => $courseid]);

// Processar o formulário
if ($form->is_cancelled()) {
    redirect($courseurl);
} else if ($data = $form->get_data()) {
    try {
        // Processar a prorrogação com a razão fornecida pelo usuário
        $reason = $data->reason['text'] ?? null;
        $result = $offeruserenrol->process_extension($reason);

        // Determinar o tipo de notificação (sucesso ou aviso).
        $notifytype = $result['is_warning'] ? notification::NOTIFY_WARNING : notification::NOTIFY_SUCCESS;

        // Redirecionar com a mensagem apropriada.
        redirect($courseurl, $result['message'], $notifytype);
    } catch (moodle_exception $e) {
        // Redirecionar com a mensagem de erro.
        redirect($courseurl, $e->getMessage(), notification::NOTIFY_ERROR);
    }
}

// Exibir o formulário
echo $OUTPUT->header();
echo $OUTPUT->heading(get_string('extendenrol', 'local_offermanager'));
$form->display();
echo $OUTPUT->footer();