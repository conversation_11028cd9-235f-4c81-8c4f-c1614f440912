<template>
  <div v-if="show" class="modal-backdrop" @click="closeOnBackdrop ? $emit('close') : null">
    <div class="modal-container" :class="[`modal-${size}`]" @click.stop>
      <div class="modal-body">
        <slot></slot>
      </div>
      
      <div v-if="$slots.footer" class="modal-footer">
        <slot name="footer"></slot>
      </div>
      <div v-else-if="showDefaultFooter" class="modal-footer">
        <custom-button 
          variant="secondary" 
          :label="cancelButtonText" 
          @click="$emit('close')" 
        />
        <custom-button 
          variant="primary" 
          :label="confirmButtonText" 
          @click="$emit('confirm')" 
          :disabled="confirmDisabled"
        />
      </div>
    </div>
  </div>
</template>

<script>
import CustomButton from './CustomButton.vue';

export default {
  name: 'Modal',
  components: {
    CustomButton
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'md',
      validator: (value) => ['sm', 'md', 'lg', 'xl'].includes(value)
    },
    closeOnBackdrop: {
      type: Boolean,
      default: true
    },
    showDefaultFooter: {
      type: Boolean,
      default: true
    },
    confirmButtonText: {
      type: String,
      default: 'Confirmar'
    },
    cancelButtonText: {
      type: String,
      default: 'Cancelar'
    },
    confirmDisabled: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close', 'confirm'],
  mounted() {
    // Adiciona evento para fechar o modal com a tecla ESC
    document.addEventListener('keydown', this.handleKeyDown);
    // Impede o scroll do body quando o modal está aberto
    if (this.show) {
      document.body.style.overflow = 'hidden';
    }
  },
  unmounted() {
    document.removeEventListener('keydown', this.handleKeyDown);
    document.body.style.overflow = '';
  },
  watch: {
    show(newVal) {
      document.body.style.overflow = newVal ? 'hidden' : '';
    }
  },
  methods: {
    handleKeyDown(e) {
      if (this.show && e.key === 'Escape') {
        this.$emit('close');
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1050;
}

.modal-container {
  background-color: #212529;
  border-radius: 6px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  max-height: 90vh;
  width: 100%;
  max-width: 500px;
  border: 1px solid #373b3e;
}

.modal-sm {
  max-width: 300px;
}

.modal-md {
  max-width: 500px;
}

.modal-lg {
  max-width: 800px;
}

.modal-xl {
  max-width: 1140px;
}

.modal-body {
  padding: 1.25rem;
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 1.25rem;
  border-top: 1px solid #373b3e;
  gap: 0.5rem;
}
</style> 