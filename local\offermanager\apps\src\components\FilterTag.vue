<template>
  <div class="tag badge badge-primary" @click="$emit('remove')">
    <i class="fas fa-times"></i>
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'FilterTag',

  emits: ['remove']
}
</script>

<style lang="scss" scoped>
.tag {
  display: inline-flex;
  align-items: center;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;

  &:hover {
    opacity: 0.9;
  }

  i {
    margin-right: 6px;
    font-size: 14px;
  }
}
</style>