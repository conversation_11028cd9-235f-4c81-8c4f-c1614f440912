<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\external;

use core_external\external_api;
use core_external\external_function_parameters;
use core_external\external_value;
use core_external\external_single_structure;
use core_external\external_multiple_structure;
use local_offermanager\persistent\offer_course_model;
use local_offermanager\persistent\offer_class_model;
use context_course;
use core_date;
use DateTime;
use moodle_exception;
use Exception;

defined('MOODLE_INTERNAL') || die();

/**
 * Class offer_class_external
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class offer_class_external extends external_api
{
    /**
     * Define os parâmetros do método.
     *
     * @return external_function_parameters
     */
    public static function add_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            array(
                'enrol' => new external_value(PARAM_PLUGIN, 'Nome do plugin de método de inscrição'),
                'offercourseid' => new external_value(PARAM_INT, 'ID do curso'),
                'classname' => new external_value(PARAM_TEXT, 'Nome da turma'),
                'startdate' => new external_value(PARAM_TEXT, 'Data de início da turma no formato yyyy-mm-dd'),
                'teachers' => new external_multiple_structure(
                    new external_value(PARAM_INT, 'ID do professor'),
                    'Lista de IDs dos professores',
                    VALUE_DEFAULT,
                    []
                ),
                'optional_fields' => new external_single_structure(
                    array(
                        'enableenddate' => new external_value(PARAM_BOOL, 'Habilitar data de fim', VALUE_OPTIONAL),
                        'enddate' => new external_value(PARAM_TEXT, 'Data de fim da turma no formato yyyy-mm-dd', VALUE_OPTIONAL),
                        'enablepreenrolment' => new external_value(PARAM_BOOL, 'Habilitar pré-inscrição', VALUE_OPTIONAL),
                        'preenrolmentstartdate' => new external_value(PARAM_TEXT, 'Data início para pré-inscrição no formato yyyy-mm-dd', VALUE_OPTIONAL),
                        'preenrolmentenddate' => new external_value(PARAM_TEXT, 'Data fim para pré-inscrição no formato yyyy-mm-dd', VALUE_OPTIONAL),
                        'description' => new external_value(PARAM_TEXT, 'Descrição da turma', VALUE_OPTIONAL),
                        'enableenrolperiod' => new external_value(PARAM_BOOL, 'Habilitar prazo de conclusão', VALUE_OPTIONAL),
                        'enrolperiod' => new external_value(PARAM_INT, 'Prazo de conclusão em dias', VALUE_OPTIONAL),
                        'minusers' => new external_value(PARAM_INT, 'Mínimo de usuários inscritos', VALUE_OPTIONAL),
                        'maxusers' => new external_value(PARAM_INT, 'Máximo de usuários inscritos', VALUE_OPTIONAL),
                        'roleid' => new external_value(PARAM_INT, 'Papel atribuído por padrão', VALUE_OPTIONAL),
                        'enablereenrol' => new external_value(PARAM_BOOL, 'Habilitar rematrícula', VALUE_OPTIONAL),
                        'reenrolmentsituations' => new external_multiple_structure(
                            new external_value(PARAM_INT, 'Situações de matrícula permitidas para rematrícula'),
                            'Lista de status permitidos para rematrícula',
                            VALUE_OPTIONAL
                        ),
                        'enableextension' => new external_value(PARAM_BOOL, 'Habilitar prorrogação de matrícula', VALUE_OPTIONAL),
                        'extensionperiod' => new external_value(PARAM_INT, 'Período de prorrogação em dias', VALUE_OPTIONAL),
                        'extensiondaysavailable' => new external_value(PARAM_INT, 'Dias antes do término da inscrição que a prorrogação vai estar disponível', VALUE_OPTIONAL),
                        'extensionmaxrequests' => new external_value(PARAM_INT, 'Máximo de prorrogações permitidas', VALUE_OPTIONAL),
                        'extensionallowedsituations' => new external_multiple_structure(
                            new external_value(PARAM_INT, 'Situações de matrícula permitidas para prorrogação'),
                            'Lista de status permitidos para prorrogação',
                            VALUE_OPTIONAL
                        )
                    ),
                    'Array de campos opcionais para a instância'
                ),
            )
        );
    }

    /**
     * Adiciona uma turma a um curso de uma oferta.
     *
     * @param string $enrol Nome do plugin de inscrição (ex: 'manual', 'self').
     * @param int $offercourseid ID da oferta de curso.
     * @param string $classname Nome da turma.
     * @param string $startdate Data de início da turma no formato yyyy-mm-dd.
     * @param array $teachers Lista de IDs dos professores.
     * @param array $optional_fields Array de campos opcionais para a instância de inscrição.
     *
     * @return array Mensagem sobre o sucesso ou falha da operação e o ID da turma criada.
     * @throws moodle_exception
     */
    public static function add($enrol, $offercourseid, $classname, string $startdate, $teachers = [], $optional_fields = []): array
    {
        $params = self::validate_parameters(self::add_parameters(), [
            'enrol' => $enrol,
            'offercourseid' => $offercourseid,
            'classname' => $classname,
            'startdate' => $startdate,
            'teachers' => $teachers,
            'optional_fields' => $optional_fields,
        ]);

        $dateFields = [
            'startdate' => &$params['startdate'],
            'enddate' => &$params['optional_fields']['enddate'],
            'preenrolmentstartdate' => &$params['optional_fields']['preenrolmentstartdate'],
            'preenrolmentenddate' => &$params['optional_fields']['preenrolmentenddate']
        ];

        foreach ($dateFields as $field => &$dateValue) {

            if (!isset($dateValue)) {
                continue;
            }

            try {
                $datetime = new DateTime($dateValue, core_date::get_user_timezone_object());

                if (in_array($field, ['enddate', 'preenrolmentenddate'])) {
                    $datetime->setTime(23, 59, 59);
                } else {
                    $datetime->setTime(0, 0, 0);
                }

                $dateValue = $datetime->getTimestamp();
            } catch (Exception $e) {
                throw new moodle_exception('invaliddateformat', 'local_offermanager', $dateValue);
            }
        }

        $dayPeriodFields = [
            'enrolperiod',
            'extensionperiod',
            'extensiondaysavailable'
        ];

        foreach ($dayPeriodFields as $field) {
            if (isset($params['optional_fields'][$field])) {
                $params['optional_fields'][$field] = $params['optional_fields'][$field] * DAYSECS;
            }
        }
        $instance = offer_class_model::add_instance(
            $params['enrol'],
            $params['offercourseid'],
            $params['classname'],
            $params['startdate'],
            $params['teachers'],
            $params['optional_fields'],
        );

        $offerclass = offer_class_model::get_by_enrolid($instance->id);
        $offerclassid = $offerclass->get('id');

        if ($instance) {
            return [
                'message' => get_string('message:class_created', 'local_offermanager', $instance),
                'offerclassid' => $offerclassid,
            ];
        } else {
            return [
                'message' => get_string('error:cannot_created_offer_class', 'local_offermanager'),
                'offerclassid' => null,
            ];
        }
    }

    /**
     * Define a estrutura de retorno do método.
     *
     * @return external_single_structure
     */
    public static function add_returns(): external_single_structure
    {
        return new external_single_structure(
            [
                'message' => new external_value(PARAM_TEXT, 'Mensagem de retorno'),
                'offerclassid' => new external_value(PARAM_INT, 'ID da turma criada', VALUE_OPTIONAL),
            ],
            'Retorno da operação'
        );
    }

    /**
     * Define os parâmetros do método.
     *
     * @return external_function_parameters
     */
    public static function get_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'offerclassid' => new external_value(PARAM_INT, 'ID da turma')
        ]);
    }

    /**
     * Obtém os dados de uma turma específica.
     *
     * @param int $offerclassid ID da turma
     * @return array Dados da turma
     */
    public static function get($offerclassid): array
    {
        $params = self::validate_parameters(self::get_parameters(), [
            'offerclassid' => $offerclassid
        ]);

        $offerclass = offer_class_model::get_record(['id' => $params['offerclassid']]);

        if (!$offerclass) {
            throw new moodle_exception('error:offer_class_not_found', 'local_offermanager');
        }

        // Obter o courseid e o contextid do curso associado à turma
        $course = $offerclass->get_course();
        $course_context = context_course::instance($course->id);

        $datetime = new DateTime('now', core_date::get_user_timezone_object());

        $startdate = $enddate = $preenrolmentstartdate = $preenrolmentenddate = null;

        if ($offerclass->get_mapped_field('startdate')) {
            $datetime->setTimestamp($offerclass->get_mapped_field('startdate'));
            $startdate = $datetime->format('Y-m-d');
        }

        if ($offerclass->get_mapped_field('enddate')) {
            $datetime->setTimestamp($offerclass->get_mapped_field('enddate'));
            $enddate = $datetime->format('Y-m-d');
        }

        if ($offerclass->get_mapped_field('preenrolmentstartdate')) {
            $datetime->setTimestamp($offerclass->get_mapped_field('preenrolmentstartdate'));
            $preenrolmentstartdate = $datetime->format('Y-m-d');
        }
        if ($offerclass->get_mapped_field('preenrolmentenddate')) {
            $datetime->setTimestamp($offerclass->get_mapped_field('preenrolmentenddate'));
            $preenrolmentenddate = $datetime->format('Y-m-d');
        }

        $teachers = $offerclass->get_teacher_users();

        $teachers = $teachers
            ? array_map(
                function ($teacher) {
                    return (object) [
                        'id' => $teacher->id,
                        'fullname' => $teacher->firstname . ' ' . $teacher->lastname
                    ];
                },
                $teachers
            )
            : $teachers
        ;

        $offercourse = $offerclass->get_offer_course();

        $extensiondata = $offerclass->get_extension_data();
        $result = [
            'id' => $params['offerclassid'],
            'enrolid' =>  $offerclass->get('enrolid'),
            'enrol' => $offerclass->get('enrol'),
            'offerid' => $offercourse->get('offerid'),
            'offercourseid' => $offercourse->get('id'),
            'course_fullname' => $course->fullname,
            'courseid' => (int) $course->id,
            'course_context_id' => (int) $course_context->id,
            'classname' => $offerclass->get_mapped_field('classname'),
            'startdate' => $startdate,
            'teachers' => $teachers,
            'operational_cycle' => $offerclass->get('operational_cycle'),
            'optional_fields' => [
                'enableenddate' => (bool) $offerclass->get_mapped_field('enableenddate') ?? 0,
                'enddate' => $enddate,
                'enablepreenrolment' => $offerclass->get_mapped_field('enablepreenrolment') ?? 0,
                'preenrolmentstartdate' => $preenrolmentstartdate,
                'preenrolmentenddate' => $preenrolmentenddate,
                'description' => $offerclass->get_mapped_field('description') ?? '',
                'enableenrolperiod' => $offerclass->get_mapped_field('enableenrolperiod') ?? 0,
                'enrolperiod' => $offerclass->get_mapped_field('enrolperiod') / DAYSECS,
                'minusers' => $offerclass->get_mapped_field('minusers'),
                'maxusers' => $offerclass->get_mapped_field('maxusers'),
                'roleid' => $offerclass->get_mapped_field('roleid'),
                'enablereenrol' => $offerclass->get_mapped_field('enablereenrol') ?? 0,
                'reenrolmentsituations' => $offerclass->get_mapped_field('reenrolmentsituations') ? explode(', ', $offerclass->get_mapped_field('reenrolmentsituations')) : [],
                'enableextension' => $offerclass->get_mapped_field('enableextension') ?? 0,
                'extensionperiod' => $extensiondata->period / DAYSECS,
                'extensiondaysavailable' => $extensiondata->days_available / DAYSECS,
                'extensionmaxrequests' => $extensiondata->max_requests,
                'extensionallowedsituations' => $extensiondata->allowed_situations ?? []
            ]
        ];

        return $result;
    }

    /**
     * Define a estrutura de retorno do método.
     *
     * @return external_single_structure
     */
    public static function get_returns(): external_single_structure
    {
        return new external_single_structure([
            'id' => new external_value(PARAM_INT, 'ID da Turma'),
            'enrolid' => new external_value(PARAM_INT, 'ID da instância de inscrição'),
            'enrol' => new external_value(PARAM_PLUGIN, 'Nome do plugin de método de inscrição'),
            'offerid' => new external_value(PARAM_INT, 'ID da oferta'),
            'offercourseid' => new external_value(PARAM_INT, 'ID da relação entre oferta e curso'),
            'courseid' => new external_value(PARAM_INT, 'ID do curso real no Moodle', VALUE_OPTIONAL),
            'course_fullname' => new external_value(PARAM_TEXT, 'Nome do curso'),
            'course_context_id' => new external_value(PARAM_INT, 'ID do contexto do curso no Moodle', VALUE_OPTIONAL),
            'classname' => new external_value(PARAM_TEXT, 'Nome da turma'),
            'startdate' => new external_value(PARAM_TEXT, 'Data de início da turma em formato yyyy-mm-dd'),
            'teachers' => new external_multiple_structure(
                new external_single_structure([
                    'id' => new external_value(PARAM_INT, 'ID do professor'),
                    'fullname' => new external_value(PARAM_TEXT, 'Nome completo do professor')
                ]),
                'Lista dos professores'
            ),
            'operational_cycle' => new external_value(PARAM_INT, 'Ciclo operacional da turma (0=Não iniciado, 1=Em andamento, 2=Encerrado)'),
            'optional_fields' => new external_single_structure([
                'enableenddate' => new external_value(PARAM_BOOL, 'Habilitar data de fim', VALUE_OPTIONAL),
                'enddate' => new external_value(PARAM_TEXT, 'Data de fim da turma em formato yyyy-mm-dd', VALUE_OPTIONAL),
                'enablepreenrolment' => new external_value(PARAM_BOOL, 'Habilitar pré-inscrição', VALUE_OPTIONAL),
                'preenrolmentstartdate' => new external_value(PARAM_TEXT, 'Data início para pré-inscrição em formato yyyy-mm-dd', VALUE_OPTIONAL),
                'preenrolmentenddate' => new external_value(PARAM_TEXT, 'Data fim para pré-inscrição em formato yyyy-mm-dd', VALUE_OPTIONAL),
                'description' => new external_value(PARAM_TEXT, 'Descrição da turma', VALUE_OPTIONAL),
                'enableenrolperiod' => new external_value(PARAM_BOOL, 'Habilitar prazo de conclusão', VALUE_OPTIONAL),
                'enrolperiod' => new external_value(PARAM_INT, 'Prazo de conclusão em dias', VALUE_OPTIONAL),
                'minusers' => new external_value(PARAM_INT, 'Mínimo de usuários inscritos', VALUE_OPTIONAL),
                'maxusers' => new external_value(PARAM_INT, 'Máximo de usuários inscritos', VALUE_OPTIONAL),
                'roleid' => new external_value(PARAM_INT, 'Papel atribuído por padrão', VALUE_OPTIONAL),
                'enablereenrol' => new external_value(PARAM_BOOL, 'Habilitar rematrícula', VALUE_OPTIONAL),
                'reenrolmentsituations' => new external_multiple_structure(
                    new external_value(PARAM_INT, 'Situações de matrícula permitidas para rematrícula'),
                    'Lista de status permitidos para rematrícula',
                    VALUE_OPTIONAL
                ),
                'enableextension' => new external_value(PARAM_BOOL, 'Habilitar prorrogação de matrícula', VALUE_OPTIONAL),
                'extensionperiod' => new external_value(PARAM_INT, 'Período de prorrogação em dias', VALUE_OPTIONAL),
                'extensiondaysavailable' => new external_value(PARAM_INT, 'Dias antes do término para inscrição que o usuário pode solicitar prorrogação', VALUE_OPTIONAL),
                'extensionmaxrequests' => new external_value(PARAM_INT, 'Máximo de prorrogações permitidas', VALUE_OPTIONAL),
                'extensionallowedsituations' => new external_multiple_structure(
                    new external_value(PARAM_INT, 'Situações de matrícula permitidas para prorrogação'),
                    'Lista de status permitidos para prorrogação',
                    VALUE_OPTIONAL
                )
            ])
        ]);
    }

    /**
     * Define os parâmetros do método.
     *
     * @return external_function_parameters
     */
    public static function update_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'offerclassid' => new external_value(PARAM_INT, 'ID da turma da oferta'),
            'classname' => new external_value(PARAM_TEXT, 'Nome da turma', VALUE_DEFAULT, null),
            'startdate' => new external_value(PARAM_TEXT, 'Data de início da turma no formato yyyy-mm-dd', VALUE_DEFAULT, null),
            'teachers' => new external_multiple_structure(
                new external_value(PARAM_INT, 'ID do professor'),
                'Lista de IDs dos professores',
                VALUE_DEFAULT,
                []
            ),
            'optional_fields' => new external_single_structure([
                'enableenddate' => new external_value(PARAM_BOOL, 'Habilitar data de fim', VALUE_OPTIONAL),
                'enddate' => new external_value(PARAM_TEXT, 'Data de fim da turma no formato yyyy-mm-dd', VALUE_OPTIONAL),
                'enablepreenrolment' => new external_value(PARAM_BOOL, 'Habilitar pré-inscrição', VALUE_OPTIONAL),
                'preenrolmentstartdate' => new external_value(PARAM_TEXT, 'Data início para pré-inscrição no formato yyyy-mm-dd', VALUE_OPTIONAL),
                'preenrolmentenddate' => new external_value(PARAM_TEXT, 'Data fim para pré-inscrição no formato yyyy-mm-dd', VALUE_OPTIONAL),
                'description' => new external_value(PARAM_TEXT, 'Descrição da turma', VALUE_OPTIONAL),
                'enableenrolperiod' => new external_value(PARAM_BOOL, 'Habilitar prazo de conclusão', VALUE_OPTIONAL),
                'enrolperiod' => new external_value(PARAM_INT, 'Prazo de conclusão em dias', VALUE_OPTIONAL),
                'minusers' => new external_value(PARAM_INT, 'Mínimo de usuários inscritos', VALUE_OPTIONAL),
                'maxusers' => new external_value(PARAM_INT, 'Máximo de usuários inscritos', VALUE_OPTIONAL),
                'roleid' => new external_value(PARAM_INT, 'Papel atribuído por padrão', VALUE_OPTIONAL),
                'enablereenrol' => new external_value(PARAM_BOOL, 'Habilitar rematrícula', VALUE_OPTIONAL),
                'reenrolmentsituations' => new external_multiple_structure(
                    new external_value(PARAM_INT, 'Situações de matrícula permitidas para rematrícula'),
                    'Lista de status permitidos para rematrícula',
                    VALUE_OPTIONAL
                ),
                'enableextension' => new external_value(PARAM_BOOL, 'Habilitar prorrogação de matrícula', VALUE_OPTIONAL),
                'extensionperiod' => new external_value(PARAM_INT, 'Período de prorrogação em dias', VALUE_OPTIONAL),
                'extensiondaysavailable' => new external_value(PARAM_INT, 'Dias antes do término para inscrição que o usuário pode solicitar prorrogação', VALUE_OPTIONAL),
                'extensionmaxrequests' => new external_value(PARAM_INT, 'Máximo de prorrogações permitidas', VALUE_OPTIONAL),
                'extensionallowedsituations' => new external_multiple_structure(
                    new external_value(PARAM_INT, 'Situações de matrícula permitidas para prorrogação'),
                    'Lista de status permitidos para prorrogação',
                    VALUE_OPTIONAL
                )
            ], 'Array de campos opcionais para a instância')
        ]);
    }

    /**
     * Atualiza uma turma específica.
     *
     * @param int $offerclassid ID da turma
     * @param string $classname Nome da turma
     * @param int $startdate Data de início
     * @param array $teachers Lista de IDs dos professores
     * @param array $optional_fields Campos opcionais
     * @return array Mensagem de retorno + offerclassid
     * @throws moodle_exception
     */
    public static function update($offerclassid, $classname, $startdate, $teachers, $optional_fields): array
    {
        $params = self::validate_parameters(self::update_parameters(), [
            'offerclassid' => $offerclassid,
            'classname' => $classname,
            'startdate' => $startdate,
            'teachers' => $teachers,
            'optional_fields' => $optional_fields
        ]);

        $dateFields = [
            'startdate' => &$params['startdate'],
            'enddate' => &$params['optional_fields']['enddate'],
            'preenrolmentstartdate' => &$params['optional_fields']['preenrolmentstartdate'],
            'preenrolmentenddate' => &$params['optional_fields']['preenrolmentenddate']
        ];

        foreach ($dateFields as $field => &$dateValue) {

            if (!isset($dateValue)) {
                continue;
            }

            try {
                $datetime = new DateTime($dateValue, core_date::get_user_timezone_object());

                if (in_array($field, ['enddate', 'preenrolmentenddate'])) {
                    $datetime->setTime(23, 59, 59);
                } else {
                    $datetime->setTime(0, 0, 0);
                }

                $dateValue = $datetime->getTimestamp();
            } catch (Exception $e) {
                throw new moodle_exception('invaliddateformat', 'local_offermanager', $dateValue);
            }
        }

        $dayPeriodFields = [
            'enrolperiod',
            'extensionperiod',
            'extensiondaysavailable'
        ];

        foreach ($dayPeriodFields as $field) {
            if (isset($params['optional_fields'][$field])) {
                $params['optional_fields'][$field] = $params['optional_fields'][$field] * DAYSECS;
            }
        }

        $offer_class = offer_class_model::get_record(
            [
                'id' => $params['offerclassid']
            ]
        );

        if (!$offer_class) {
            throw new moodle_exception('error:offer_class_not_found', 'local_offermanager');
        }

        if (!isset($params['optional_fields']['reenrolmentsituations'])) {
            $params['optional_fields']['reenrolmentsituations'] = [];
        }

        $instance = $offer_class->update_instance(
            $params['offerclassid'],
            $params['classname'],
            $params['startdate'],
            $params['teachers'],
            $params['optional_fields']
        );

        if ($instance) {
            return [
                'message' => get_string('message:class_updated', 'local_offermanager', $instance),
                'offerclassid' => $params['offerclassid']
            ];
        } else {
            return [
                'message' => get_string('error:cannot_update_offer_class', 'local_offermanager'),
                'offerclassid' => $params['offerclassid']
            ];
        }
    }
    /**
     * Define a estrutura de retorno do método update.
     *
     * @return external_single_structure
     */
    public static function update_returns(): external_single_structure
    {
        return new external_single_structure(
            [
                'message' => new external_value(PARAM_TEXT, 'Mensagem de retorno'),
                'offerclassid' => new external_value(PARAM_INT, 'ID da turma criada', VALUE_OPTIONAL),
            ],
            'Retorno da operação'
        );
    }

    /**
     * Define os parâmetros do método delete.
     *
     * @return external_function_parameters
     */
    public static function delete_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'offerclassid' => new external_value(PARAM_INT, 'ID da turma da oferta')
        ]);
    }

    /**
     * Exclui uma turma específica.
     *
     * @param int $offerclassid ID da turma
     * @return string Mensagem de retorno
     */
    public static function delete($offerclassid): string
    {
        $params = self::validate_parameters(self::delete_parameters(), [
            'offerclassid' => $offerclassid
        ]);

        $offerclass = offer_class_model::get_record(['id' => $params['offerclassid']]);

        if (!$offerclass) {
            throw new moodle_exception('error:offer_class_not_found', 'local_offermanager');
        }

        if (!$offerclass->can_delete()) {

            throw new moodle_exception('error:cannot_delete_offer_class', 'local_offermanager');
        }

        $instance = $offerclass->get_enrol_instance();

        $return = $offerclass->delete_instance();

        return $return
            ? get_string('message:class_deleted', 'local_offermanager', $instance)
            : get_string('error:cannot_delete_offer_class', 'local_offermanager')
        ;
    }

    /**
     * Define a estrutura de retorno do método delete.
     *
     * @return external_value
     */
    public static function delete_returns(): external_value
    {
        return new external_value(PARAM_TEXT, 'Mensagem de retorno');
    }

    /**
     * Define os parâmetros do método get_potential_users_to_enrol.
     *
     * @return external_function_parameters
     */
    public static function get_potential_users_to_enrol_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'offerclassid' => new external_value(PARAM_INT, 'ID da turma'),
            'search_string' => new external_value(PARAM_TEXT, 'String para busca de usuário'),
            'excluded_userids' => new external_multiple_structure(
                new external_value(PARAM_INT, 'Userid'),
                'Userids to exclude in query'
            )
        ]);
    }

    /**
     * Retorna lista de usuários potenciais para matrícula em uma turma.
     *
     * @param int $offerclassid ID da turma
     * @return array Array de usuários com id e nome completo
     * @throws moodle_exception
     */
    public static function get_potential_users_to_enrol($offerclassid, $search_string, $excluded_userids): array
    {
        $params = self::validate_parameters(
            self::get_potential_users_to_enrol_parameters(),
            [
                'offerclassid' => $offerclassid,
                'search_string' => $search_string,
                'excluded_userids' => $excluded_userids
            ]
        );

        $offerclass = offer_class_model::get_record(['id' => $params['offerclassid']]);

        if (!$offerclass) {
            throw new moodle_exception('error:offer_class_not_found', 'local_offermanager');
        }

        $offercourse = $offerclass->get_offer_course();

        $users = $offercourse->get_potential_users_to_enrol($search_string, $excluded_userids);

        $result = [];

        foreach ($users as $user) {
            $result[] = [
                'id' => (int) $user->id,
                'fullname' => $user->fullname ?? fullname($user)
            ];
        }

        return $result;
    }

    /**
     * Define a estrutura de retorno do método get_potential_users_to_enrol.
     *
     * @return external_multiple_structure
     */
    public static function get_potential_users_to_enrol_returns(): external_multiple_structure
    {
        return new external_multiple_structure(
            new external_single_structure([
                'id' => new external_value(PARAM_INT, 'ID do usuário'),
                'fullname' => new external_value(PARAM_TEXT, 'Nome completo do usuário')
            ])
        );
    }

    /**
     * Define os parâmetros do método.
     *
     * @return external_function_parameters
     */
    public static function get_potential_teachers_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'offercourseid' => new external_value(PARAM_TEXT, 'ID da relação entre oferta e curso'),
            'search_string' => new external_value(PARAM_TEXT, 'String para busca de usuário'),
            'offerclassid' => new external_value(PARAM_INT, 'ID da turma da oferta', VALUE_DEFAULT, 0),
            'excluded_userids' => new external_multiple_structure(
                new external_value(PARAM_INT, 'Userid'),
                'Userids to exclude in query'
            )
        ]);
    }

    /**
     * Retorna lista de usuários do sistema que podem ser professores,
     * opcionalmente filtrando por curso
     *
     * @param int $offercourseid
     * @param int $$offerclassid
     * @return array Array de usuários com id e nome completo
     * @throws moodle_exception
     */
    public static function get_potential_teachers($offercourseid, $search_string, $offerclassid, $excluded_userids): array
    {
        $params = self::validate_parameters(
            self::get_potential_teachers_parameters(),
            [
                'offercourseid' => $offercourseid,
                'search_string' => $search_string,
                'offerclassid' => $offerclassid,
                'excluded_userids' => $excluded_userids
            ]
        );

        $offercourse = offer_course_model::get_record(['id' => $params['offercourseid']]);

        if (!$offercourse) {
            throw new moodle_exception('error:offer_course_not_found', 'local_offermanager');
        }

        $users = $offercourse->get_potential_users_to_enrol($search_string, $excluded_userids);

        if ($params['offerclassid']) {
            $combined = [];

            $offerclass = offer_class_model::get_record(['id' => $params['offerclassid']]);

            $enroled_users = $offerclass->get_enroled_users($search_string, 'name', $excluded_userids);

            foreach (array_merge($enroled_users, $users) as $user) {
                if ($user) {
                    $combined[$user->id] = $user;
                }
            }
            $users = array_values($combined);
        }

        return array_map(function ($user) {
            return [
                'id' => $user->id,
                'fullname' => $user->fullname ?? fullname($user)
            ];
        }, $users);
    }

    /**
     * Define a estrutura de retorno do método.
     *
     * @return external_multiple_structure
     */
    public static function get_potential_teachers_returns(): external_multiple_structure
    {
        return new external_multiple_structure(
            new external_single_structure([
                'id' => new external_value(PARAM_INT, 'ID do usuário'),
                'fullname' => new external_value(PARAM_TEXT, 'Nome completo do usuário')
            ])
        );
    }

    /**
     * Define os parâmetros do método.
     *
     * @return external_function_parameters
     */
    public static function enrol_users_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'offerclassid' => new external_value(PARAM_INT, 'ID da turma da oferta'),
            'userids' => new external_multiple_structure(
                new external_value(PARAM_INT, 'ID do usuário'),
                'Array de userids'
            ),
            'roleid' => new external_value(PARAM_INT, 'ID da função a ser atribuída aos usuários', VALUE_DEFAULT, 5)
        ]);
    }

    /**
     * Realiza a inscrição em massa de vários usuários em uma turma específica.
     *
     * @param int $offerclassid ID da turma onde os usuários serão inscritos.
     * @param int[] $userids Array contendo o id dos usuários a serem inscritos.
     * @param int $roleid
     *
     * @return array Retorna um array com os resultados da inscrição para cada usuário.
     *               Cada elemento contém:
     *               - userid: ID do usuário.
     *               - success: Indica se a inscrição foi bem-sucedida (true/false).
     *               - message: Mensagem detalhada.
     */
    public static function enrol_users($offerclassid, $userids, $roleid): array
    {
        $params = self::validate_parameters(self::enrol_users_parameters(), [
            'offerclassid' => $offerclassid,
            'userids' => $userids,
            'roleid' => $roleid
        ]);

        $offerclass = offer_class_model::get_record(['id' => $params['offerclassid']]);

        if (!$offerclass) {
            throw new moodle_exception('error:offer_class_not_found', 'local_offermanager');
        }

        $results = [];

        foreach ($params['userids'] as $userid) {
            $results[] = self::process_user_enrolment($userid, $offerclass, $roleid);
        }

        return $results;
    }

    /**
     * Define a estrutura de retorno do método.
     *
     * @return external_multiple_structure
     */
    public static function enrol_users_returns(): external_multiple_structure
    {
        return new external_multiple_structure(
            new external_single_structure([
                'userid' => new external_value(PARAM_INT, 'ID do usuário'),
                'success' => new external_value(PARAM_BOOL, 'Status da operação'),
                'message' => new external_value(PARAM_TEXT, 'Mensagem detalhada'),
                'reenrol' => new external_value(PARAM_BOOL,'Boleano para casos de rematrícula')
            ])
        );
    }

    /**
     * Processa a inscrição individual de um usuário
     *
     * @param int $userid Id do usuário
     * @param offer_class_model $offerclass Instância da turma
     * @param int $roleid Id do papel a ser atribuido
     *
     * @return array Resultado do processamento
     */
    private static function process_user_enrolment(int $userid, offer_class_model $offerclass, $roleid): array
    {
        global $DB;
        try {

            $reenrol = false;

            if (!$user = $DB->get_record('user', ['id' => $userid, 'deleted' => 0])) {
                throw new moodle_exception('error:user_not_found', 'local_offermanager', '', $userid);
            }

            if ($offer_user_enrolment = $offerclass->get_user_offer_user_enrolment($userid)) {
                $reenrol = $offer_user_enrolment->can_reenrol();
                if ($reenrol) {
                    $success = $offer_user_enrolment->process_reenrolment();

                    $params = (object) [
                        'classname' => $offerclass->get_mapped_field('classname'),
                        'userfullname' => fullname($user)
                    ];

                    $message = is_string($success)
                        ? $success
                        : ($success
                            ? get_string('message:general_reenrolment_success', 'local_offermanager', $params)
                            :  get_string('message:general_reenrolment_failed', 'local_offermanager', $params)
                        );
                } else {
                    throw new moodle_exception('error:user_already_enrolled', 'local_offermanager', '', fullname($user));
                }
            } else {
                $success = $offerclass->enrol_user($userid, $roleid);

                $message = is_string($success)
                    ? $success
                    : ($success
                        ? get_string('message:enrolment_success', 'local_offermanager')
                        :  get_string('message:enrolment_failed', 'local_offermanager')
                    );
            }

            return [
                'userid' => $userid,
                'success' => $success,
                'message' => $message,
                'reenrol' => $reenrol
            ];
        } catch (moodle_exception $e) {
            return [
                'userid' => $userid ?? 0,
                'success' => false,
                'message' => $e->getMessage(),
                'reenrol' => false
            ];
        }
    }

    /**
     * Define os parâmetros para alterar o status de uma turma.
     *
     * @return external_function_parameters
     */
    public static function set_status_parameters()
    {
        return new external_function_parameters([
            'id' => new external_value(PARAM_INT, 'ID da turma a ser alterada'),
            'status' => new external_value(PARAM_BOOL, 'Status a ser alterado. 0 para desabilitar e 1 para habilitar')
        ]);
    }

    /**
     * Alterar o status de uma turma.
     *
     * @param int $id ID da turma
     * @param bool $status Novo status (true para ativo, false para inativo)
     * @return array Resultado da operação
     * @throws moodle_exception
     */
    public static function set_status($id, $status)
    {
        self::validate_parameters(
            self::set_status_parameters(),
            compact('id', 'status')
        );

        $offer_class = offer_class_model::get_record(
            [
                'id' => $id
            ]
        );

        if (!$offer_class) {
            throw new moodle_exception('error:offer_class_not_found', 'local_offermanager');
        }

        $current_status = (bool) $offer_class->get('status');

        if ($status == $current_status) {
            $message_key = $status
                ? 'error:offer_class_already_active'
                : 'error:offer_class_already_inactive';

            throw new moodle_exception($message_key, 'local_offermanager');
        }

        $status
            ? $offer_class->activate()
            : $offer_class->inactivate();

        $message_key = $status
            ? 'event:offerclassactivated'
            : 'event:offerclassinactivated';

        return [
            'status' => $offer_class->get('status'),
            'message' => get_string($message_key, 'local_offermanager'),
        ];
    }

    /**
     * Define a estrutura de retorno da função set_status.
     *
     * @return external_single_structure
     */
    public static function set_status_returns(): external_single_structure
    {
        return new external_single_structure([
            'status' => new external_value(PARAM_BOOL, 'O status atual'),
            'message' => new external_value(PARAM_TEXT, 'Mensagem de retorno'),
        ]);
    }

    /**
     * Define os parâmetros do método duplicate.
     *
     * @return external_function_parameters
     */
    public static function duplicate_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'offerclassid' => new external_value(PARAM_INT, 'ID da turma original a ser duplicada'),
            'targetoffercourseid' => new external_value(PARAM_INT, 'ID do curso de destino para a nova turma')
        ]);
    }

    /**
     * Duplica uma turma para um novo curso dentro da mesma oferta.
     *
     * @param int $offerclassid ID da turma original.
     * @param int $targetoffercourseid ID do curso de destino.
     * @return array Dados da nova turma criada.
     * @throws moodle_exception
     */
    public static function duplicate(int $offerclassid, int $targetoffercourseid): array
    {
        $params = self::validate_parameters(self::duplicate_parameters(), [
            'offerclassid' => $offerclassid,
            'targetoffercourseid' => $targetoffercourseid
        ]);

        $original_offer_class = offer_class_model::get_record(['id' => $params['offerclassid']]);
        if (!$original_offer_class) {
            throw new moodle_exception('error:offer_class_not_found', 'local_offermanager', '', $params['offerclassid']);
        }

        $target_offer_course = offer_course_model::get_record(['id' => $params['targetoffercourseid']]);
        if (!$target_offer_course) {
            throw new moodle_exception('error:offer_course_not_found', 'local_offermanager', '', $params['targetoffercourseid']);
        }

        $new_offer_class = $original_offer_class->duplicate($target_offer_course);

        return self::get($new_offer_class->get('id'));
    }

    /**
     * Define a estrutura de retorno do método duplicate.
     * Reutiliza a estrutura de retorno do método 'get' para consistência.
     *
     * @return external_single_structure
     */
    public static function duplicate_returns(): external_single_structure
    {
        return self::get_returns();
    }

    /**
     * Define os parâmetros do método duplicate.
     *
     * @return external_function_parameters
     */
    public static function get_enroled_users_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'offerclassid' => new external_value(PARAM_INT, 'ID da turma'),
            'searchstring' => new external_value(PARAM_TEXT, 'String de busca do filtro'),
            'fieldstring' => new external_value(PARAM_TEXT, "Campo para fazer o filtro: 'name', 'email', 'username'."),
            'excludeduserids' => new external_multiple_structure(
                new external_value(PARAM_INT, 'ID do usuário'),
                'Array de userids a excluir do filtro',
                VALUE_DEFAULT,
                []
            ),
        ]);
    }

    /**
     * Duplica uma turma para um novo curso dentro da mesma oferta.
     *
     * @param int $offerclassid ID da turma original.
     * @param int $targetoffercourseid ID do curso de destino.
     * @return array Dados da nova turma criada.
     * @throws moodle_exception
     */
    public static function get_enroled_users(int $offerclassid, string $searchstring, string $fieldstring, array $excludeduserids): array
    {
        $params = self::validate_parameters(self::get_enroled_users_parameters(), [
            'offerclassid' => $offerclassid,
            'searchstring' => $searchstring,
            'fieldstring' => $fieldstring,
            'excludeduserids' => $excludeduserids
        ]);

        $valid_fields = ['name', 'email', 'username'];

        if (!in_array($params['fieldstring'], $valid_fields)) {
            throw new moodle_exception('invalidfieldname', 'error', $params['fieldstring']);
        }

        $offerclass = offer_class_model::get_record(['id' => $params['offerclassid']]);

        if (!$offerclass) {
            throw new moodle_exception('error:offer_class_not_found', 'local_offermanager');
        }

        return $offerclass->get_enroled_users(
            $params['searchstring'],
            $params['fieldstring'],
            $params['excludeduserids'],
            true
        );
    }

    /**
     * Define a estrutura de retorno do método duplicate.
     * Reutiliza a estrutura de retorno do método 'get' para consistência.
     *
     * @return external_multiple_structure
     */
    public static function get_enroled_users_returns(): external_multiple_structure
    {
        return new external_multiple_structure(
            new external_single_structure([
                'id' => new external_value(PARAM_INT, 'ID do usuário'),
                'fullname' => new external_value(PARAM_TEXT, 'Nome completo do usuário')
            ])
        );
    }

    /**
     * Parâmetros para buscar inscrições filtradas por turma com paginação.
     *
     * @return external_function_parameters
     */
    public static function fetch_enrolments_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'offerclassid' => new external_value(PARAM_INT, 'ID da turma (offer_class)'),
            'userids' => new external_multiple_structure(
                new external_value(PARAM_INT, 'ID do usuário'),
                'IDs de usuários para mostrar na tabela',
                VALUE_DEFAULT,
                []
            ),
            'page' => new external_value(PARAM_INT, 'Número da página (1-based)', VALUE_DEFAULT, 1),
            'perpage' => new external_value(PARAM_INT, 'Itens por página', VALUE_DEFAULT, 20),
            'orderby' => new external_value(PARAM_TEXT, 'Valores: fullname, email, cpf, startdate, enddate, enrolperiod, situation, status', VALUE_DEFAULT, 'fullname'),
            'direction' => new external_value(PARAM_TEXT, 'Valores: ASC, DESC', VALUE_DEFAULT, 'ASC')
        ]);
    }

    /**
     * Busca inscrições da turma aplicando filtros combinados e paginação.
     *
     * @param int $offerclassid
     * @param int[] $userids
     * @param int $page
     * @param int $perpage
     * @param string $orderby
     * @param string $direction
     * @return array
     * @throws \invalid_parameter_exception
     */
    public static function fetch_enrolments(
        int $offerclassid,
        array $userids,
        int $page,
        int $perpage,
        string $orderby,
        string $direction
    ): array {
        global $DB, $CFG;

        require_once($CFG->libdir . '/gradelib.php');

        $params = self::validate_parameters(self::fetch_enrolments_parameters(), [
            'offerclassid' => $offerclassid,
            'userids' => $userids,
            'page' => $page,
            'perpage' => $perpage,
            'orderby' => $orderby,
            'direction' => $direction
        ]);

        $offerclass = offer_class_model::get_record(['id' => $params['offerclassid']]);

        if (!$offerclass) {
            throw new moodle_exception('error:class_not_found', 'local_offermanager');
        }

        $result = $offerclass->get_filtered_enrolments(
            $params['userids'],
            $params['page'] - 1,
            $params['perpage'],
            $params['orderby'],
            $params['direction']
        );

        $enrolments = [];

        foreach ($result['enrolments'] as $enrolment) {
            $userid = $enrolment->get('userid');
            $user = \core_user::get_user($userid);

            $cpf = $user->username;
            $course = $enrolment->get_course();
            $courseid = $course->id;
            $groups =  groups_get_all_groups($courseid, $userid);

            $roles = $enrolment->get_roles();

            if ($groups) {
                $groups = array_map(fn($g) => $g->name, $groups);
            }

            $ue = $enrolment->get_user_enrolment();

            $timestart = $ue->timestart ?? 0;
            $timeend = $ue->timeend ?? 0;

            $enrolperiod_days = floor(($timeend - $timestart) / DAYSECS);
            $enrolperiod_days = $enrolperiod_days > 1 ? "{$enrolperiod_days} " . get_string('days') : "{$enrolperiod_days} " . get_string('day');

            $enrolperiod = $timeend
                ?  $enrolperiod_days
                : get_string('unlimited');

            $progress = $enrolment->get_course_progress();

            $grade = $enrolment->get_course_grade();
            $grade = number_format((float) $grade, 2, ',');

            $enrolments[] = [
                'userid' => $userid,
                'offeruserenrolid' => $enrolment->get('id'),
                'fullname' => fullname($user),
                'email' => $user->email,
                'cpf' => $cpf,
                'roles' => $roles,
                'groups' => $groups ? implode(', ', $groups) : get_string('groupsnone', 'group'),
                'timestart' => $timestart,
                'timeend' => $timeend,
                'enrolperiod' => $enrolperiod,
                'progress' => $progress,
                'status' => (int) $ue->status,
                'grade' => $grade,
                'situation' => $enrolment->get('situation'),
                'situation_name' => $enrolment->get_situation_name(),
                'enrol' => $offerclass->get('enrol'),
                'timecreated' => $enrolment->get('timecreated'),
                'can_cancel' => $enrolment->can_cancel()
            ];
        }

        return [
            'page' => $page,
            'perpage' => $perpage,
            'total' => $result['total'],
            'enrolments' => $enrolments,
        ];
    }

    /**
     * Estrutura de retorno do método fetch_enrolments.
     *
     * @return external_single_structure
     */
    public static function fetch_enrolments_returns(): external_single_structure
    {
        return new external_single_structure([
            'page' => new external_value(PARAM_INT, 'Número da página (1-based)'),
            'perpage' => new external_value(PARAM_INT, 'Itens por página'),
            'total' => new external_value(PARAM_INT, 'Total de inscrições encontradas'),
            'enrolments' => new external_multiple_structure(
                new external_single_structure([
                    'userid' => new external_value(PARAM_INT, 'ID do usuário'),
                    'offeruserenrolid' => new external_value(PARAM_INT, 'ID da matrícula (offeruserenrolid)'),
                    'fullname' => new external_value(PARAM_TEXT, 'Nome completo'),
                    'email' => new external_value(PARAM_TEXT, 'E-mail'),
                    'cpf' => new external_value(PARAM_RAW, 'CPF'),
                    'roles' => new external_multiple_structure(
                        new external_single_structure([
                            'id' => new external_value(PARAM_INT, 'Id do papel'),
                            'name' => new external_value(PARAM_TEXT, 'Nome do papel'),
                        ])
                    ),
                    'groups' => new external_value(PARAM_TEXT, 'Grupos'),
                    'timestart' => new external_value(PARAM_INT, 'Data início matrícula'),
                    'timeend' => new external_value(PARAM_INT, 'Data fim matrícula'),
                    'enrolperiod' => new external_value(PARAM_TEXT, 'String com o prazo de conclusão'),
                    'progress' => new external_value(PARAM_FLOAT, 'Progresso percentual'),
                    'status' =>  new external_value(PARAM_INT, 'Status da inscrição na tabela user_enrolments'),
                    'grade' => new external_value(PARAM_RAW, 'Nota'),
                    'situation' => new external_value(PARAM_INT, 'Situação customizada'),
                    'situation_name' => new external_value(PARAM_TEXT, 'Nome da situação customizada'),
                    'enrol' => new external_value(PARAM_PLUGIN, 'Método de inscrição'),
                    'timecreated' => new external_value(PARAM_INT, 'Data de criação da matrícula'),
                    'can_cancel' => new external_value(PARAM_BOOL, 'Pode cancelar')
                ])
            )
        ]);
    }

    /**
     * Parâmetros para buscar cursos potenciais para duplicação de turma.
     *
     * @return external_function_parameters
     */
    public static function get_potential_duplication_courses_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'offerclassid' => new external_value(PARAM_INT, 'ID da turma')
        ]);
    }

    /**
     * Retorna os cursos potenciais para duplicação da turma.
     *
     * @param int $offerclassid ID da turma
     * @return array Lista de cursos com id, fullname e informações da categoria
     * @throws moodle_exception
     */
    public static function get_potential_duplication_courses(int $offerclassid): array
    {
        $params = self::validate_parameters(
            self::get_potential_duplication_courses_parameters(),
            [
                'offerclassid' => $offerclassid
            ]
        );

        $offerclass = offer_class_model::get_record(['id' => $params['offerclassid']]);

        if (!$offerclass) {
            throw new moodle_exception('error:offer_class_not_found', 'local_offermanager');
        }

        return array_map(
            function ($course) {
                return [
                    'id' => $course->id,
                    'name' => $course->fullname,
                    'categoryid' => $course->categoryid,
                    'category_name' => $course->category_name
                ];
            },
            $offerclass->get_potential_duplication_courses()
        );
    }

    /**
     * Estrutura de retorno para cursos potenciais de duplicação.
     *
     * @return external_multiple_structure
     */
    public static function get_potential_duplication_courses_returns(): external_multiple_structure
    {
        return new external_multiple_structure(
            new external_single_structure([
                'id' => new external_value(PARAM_INT, 'ID do curso'),
                'name' => new external_value(PARAM_TEXT, 'Nome completo do curso'),
                'categoryid' => new external_value(PARAM_INT, 'ID da categoria do curso'),
                'category_name' => new external_value(PARAM_TEXT, 'Nome da categoria do curso')
            ])
        );
    }

    /**
     * Define os parâmetros do método get_filter_options.
     *
     * @return external_function_parameters
     */
    public static function get_filter_options_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'offerclassid' => new external_value(PARAM_INT, 'ID da turma'),
            'filter_type' => new external_value(PARAM_TEXT, 'Tipo de filtro (name, email, cpf)', VALUE_DEFAULT, 'name')
        ]);
    }

    /**
     * Retorna opções para os filtros de usuários matriculados.
     *
     * @param int $offerclassid ID da turma
     * @param string $filter_type Tipo de filtro (name, email, cpf)
     * @return array Lista de opções para o filtro
     */
    public static function get_filter_options(int $offerclassid, string $filter_type = 'name'): array
    {
        global $DB;

        $params = self::validate_parameters(self::get_filter_options_parameters(), [
            'offerclassid' => $offerclassid,
            'filter_type' => $filter_type
        ]);

        debugging('get_filter_options - params: ' . json_encode($params), DEBUG_DEVELOPER);

        $offerclass = new offer_class_model($params['offerclassid']);

        if (!$offerclass || !$offerclass->get('id')) {
            debugging('get_filter_options - offerclass not found', DEBUG_DEVELOPER);
            throw new moodle_exception('error:offer_class_not_found', 'local_offermanager');
        }

        // Obter todos os usuários matriculados na turma
        $enrolid = $offerclass->get('enrolid');
        debugging('get_filter_options - enrolid: ' . $enrolid, DEBUG_DEVELOPER);

        // Verificar se a turma tem um enrolid válido
        if (!$enrolid) {
            debugging('get_filter_options - enrolid is empty', DEBUG_DEVELOPER);
            return [];
        }

        // Usar uma abordagem diferente para obter os usuários matriculados
        $sql = "SELECT DISTINCT u.id, u.firstname, u.lastname, u.email, u.username AS cpf
                FROM {user} u
                JOIN {user_enrolments} ue ON ue.userid = u.id
                WHERE ue.enrolid = :enrolid AND u.deleted = 0
                ORDER BY u.firstname, u.lastname";

        $users = $DB->get_records_sql($sql, ['enrolid' => $enrolid]);
        debugging('get_filter_options - found users: ' . count($users), DEBUG_DEVELOPER);

        $options = [];
        foreach ($users as $user) {
            $option = [
                'id' => $user->id
            ];

            switch ($params['filter_type']) {
                case 'name':
                    $option['fullname'] = fullname($user);
                    break;
                case 'email':
                    $option['email'] = $user->email;
                    break;
                case 'cpf':
                    $option['cpf'] = $user->cpf;
                    break;
            }

            $options[] = $option;
        }

        debugging('get_filter_options - returning options: ' . count($options), DEBUG_DEVELOPER);

        return $options;
    }

    /**
     * Define a estrutura de retorno do método get_filter_options.
     *
     * @return external_multiple_structure
     */
    public static function get_filter_options_returns(): external_multiple_structure
    {
        return new external_multiple_structure(
            new external_single_structure([
                'id' => new external_value(PARAM_INT, 'ID do usuário'),
                'fullname' => new external_value(PARAM_TEXT, 'Nome completo do usuário', VALUE_OPTIONAL),
                'email' => new external_value(PARAM_TEXT, 'Email do usuário', VALUE_OPTIONAL),
                'cpf' => new external_value(PARAM_RAW, 'CPF do usuário', VALUE_OPTIONAL)
            ])
        );
    }

    /**
     * Define os parâmetros do método get_all_roles.
     *
     * @return external_function_parameters
     */
    public static function get_all_roles_parameters(): external_function_parameters
    {
        return new external_function_parameters([]);
    }

    /**
     * Retorna todos os papéis disponíveis no sistema.
     *
     * @return array Array de papéis com id e nome
     */
    public static function get_all_roles(): array
    {
        // Buscar todos os papéis disponíveis no sistema
        $roles = role_get_names();

        $result = [];
        foreach ($roles as $role) {
            $result[] = [
                'id' => (int)$role->id,
                'name' => $role->localname
            ];
        }

        return $result;
    }

    /**
     * Define a estrutura de retorno do método get_all_roles.
     *
     * @return external_multiple_structure
     */
    public static function get_all_roles_returns(): external_multiple_structure
    {
        return new external_multiple_structure(
            new external_single_structure([
                'id' => new external_value(PARAM_INT, 'ID do papel'),
                'name' => new external_value(PARAM_TEXT, 'Nome do papel')
            ])
        );
    }
}
