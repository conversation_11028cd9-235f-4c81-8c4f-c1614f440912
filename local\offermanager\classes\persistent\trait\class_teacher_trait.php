<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON>le is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\persistent\trait;

use local_offermanager\constants;
use local_offermanager\event\offer_class_teachers_updated;
use core_user;
use context_course;
use moodle_exception;

defined('MOODLE_INTERNAL') || die();

/**
 * Trait class_teacher_trait
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
trait class_teacher_trait
{
    /** @var int|null ID do papel de professor editor */
    protected $teacherroleid = null;
    /**
     * Inscreve ou atualiza a inscrição de um professor na turma.
     *
     * @param int $userid ID do usuário (professor).
     * 
     * @return bool Retorna true se a operação for bem-sucedida.
     * @throws moodle_exception
     */
    public function enrol_teacher($userid): bool
    {
        global $DB;

        $enrol_instance = $this->get_enrol_instance();

        $plugin = $this->get_plugin();

        $course = $this->get_course();
        $context = context_course::instance($course->id);

        $startdate = time();

        $enddate = $this->get_mapped_field('enableenddate') ? $this->get_mapped_field('enddate') : 0;

        if ($this->user_has_enrolment($userid)) {

            $roleid = role_assign(
                $this->get_teacher_role_id(),
                $userid,
                $context->id,
                'enrol_' . $plugin->get_name(),
                $enrol_instance->id
            );

            if ($roleid) {
                $plugin->update_user_enrol(
                    $enrol_instance,
                    $userid,
                    ENROL_USER_ACTIVE,
                    $startdate,
                    $enddate
                );
            }
        } else {
            $plugin->enrol_user(
                $enrol_instance,
                $userid,
                $this->get_teacher_role_id(),
                $startdate,
                $enddate,
                ENROL_USER_ACTIVE
            );
        }



        $current_teachers = $DB->get_field(
            'enrol',
            'customtext2',
            ['id' => $this->get('enrolid')]
        );

        $teacher_ids = $current_teachers ? explode(', ', $current_teachers) : [];

        if (!in_array($userid, $teacher_ids)) {
            $teacher_ids[] = $userid;
            $DB->set_field(
                'enrol',
                'customtext2',
                implode(', ', $teacher_ids),
                ['id' => $this->get('enrolid')]
            );
        }

        return $this->is_teacher($userid);
    }

    /**
     * Desinscreve ou professor da turma.
     *
     * @param int $userid ID do usuário (professor).
     * 
     * @return bool Retorna true se a operação for bem-sucedida.
     * @throws moodle_exception
     */
    public function unenrol_teacher(int $userid): bool
    {
        global $DB;

        $instance = $this->get_enrol_instance();
        $plugin = $this->get_plugin();

        $user = \core_user::get_user($userid);

        if (!$user) {
            throw new moodle_exception('error:invalid_userid', 'local_offermanager');
        }

        $ue = $this->get_user_enrolment($userid);

        if (!$ue) {
            throw new moodle_exception('error:user_not_enrolled', 'local_offermanager');
        }

        $plugin->unenrol_user($instance, $ue->userid);

        $current_teachers = $DB->get_field(
            'enrol',
            'customtext2',
            ['id' => $this->get('enrolid')]
        );

        if ($current_teachers) {
            $teacher_ids = explode(', ', $current_teachers);

            if (($key = array_search($userid, $teacher_ids)) !== false) {
                unset($teacher_ids[$key]);
                $DB->set_field(
                    'enrol',
                    'customtext2',
                    implode(', ', $teacher_ids),
                    ['id' => $this->get('enrolid')]
                );
            }
        }

        return !$this->is_teacher($userid);
    }

    /**
     * Retorna os professores da turma.
     * 
     * @return array
     */
    public function get_teachers()
    {
        $teachers = $this->get_mapped_field('teachers');

        return $teachers
            ? explode(', ', $teachers)
            : [];
    }

    /**
     * Retorna os objetos user dos professores da turma.
     * 
     * @return array de objetos user
     */
    public function get_teacher_users()
    {
        global $DB;

        $teachers = $this->get_teachers();

        if (!$teachers) {
            return [];
        }

        list($insql, $inparams) = $DB->get_in_or_equal($teachers);
        return $DB->get_records_select(
            'user',
            "id $insql AND deleted = 0",
            $inparams
        );
    }

    /**
     * Atualiza a lista de professores da turma.
     *
     * @param int[] $userids Array com IDs dos professores
     * @return bool Retorna true se houve alteração, false caso contrário
     * @throws moodle_exception
     */
    public function update_teachers(array $userids)
    {
        $userids = array_map('intval', $userids);

        $current_teachers = $this->get_mapped_field('teachers')
            ? array_map('intval', explode(', ', $this->get_mapped_field('teachers')))
            : [];

        $teachers_to_add = array_diff($userids, $current_teachers);
        $teachers_to_remove = array_diff($current_teachers, $userids);
        $changes_made = false;

        foreach ($teachers_to_add as $userid) {
            $result = $this->enrol_teacher($userid);
            $changes_made = $changes_made || $result;
        }
        foreach ($teachers_to_remove as $userid) {
            $result = $this->unenrol_teacher($userid);
            $changes_made = $changes_made || $result;
        }

        if ($changes_made) {

            $event = offer_class_teachers_updated::instance($this, $userids);
            $event->trigger();

            $this->fetch_enrol_instance();
        }

        return $changes_made;
    }

    /**
     * Verifica se o usuário é professor da turma.
     *
     * @param int $userid ID do usuário a ser verificado
     * @return bool Retorna true se o usuário é professor e está inscrito na turma, false caso contrário
     */
    public function is_teacher(int $userid): bool
    {
        $has_role = $this->has_teacher_role_assignment($userid);
        $is_enrolled = $this->user_has_enrolment($userid);

        return $has_role && $is_enrolled;
    }

    /**
     * Verifica se o usuário tem atribuição de papel de professor.
     * 
     * @param int $userid ID do usuário
     * @return bool
     */
    public function has_teacher_role_assignment($userid)
    {
        $teacherroleid = $this->get_teacher_role_id();
        return $this->has_role_assignment($userid, $teacherroleid);
    }



    /**
     * Retorna o ID do papel de professor editor (editingteacher).
     *
     * @return int ID do papel de professor editor
     */
    public function get_teacher_role_id(): int
    {
        global $DB;
        if ($this->teacherroleid === null) {
            $roles = $this->get_course_roles();

            $get_all_roles = $DB->get_records_menu('role', null, '', 'id, shortname');

            foreach ($roles as $id => $fullname) {
                $shortname = $get_all_roles[$id];
                if ($shortname === constants::OFFER_TEACHER_ROLE_DEFAULT) {
                    $this->teacherroleid = $id;
                    break;
                }
            }

            if ($this->teacherroleid === null) {
                throw new moodle_exception('error:role_not_found', 'local_offermanager');
            }
        }

        return $this->teacherroleid;
    }
}
