<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Capability definitions for Offer Manager
 *
 * Documentation: {@link https://moodledev.io/docs/apis/subsystems/access}
 *
 * @package    local_offermanager
 * @category   access
 * @copyright  2025 2015 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

if (!defined('CONTEXT_OFFER_CLASS')) {
    define('CONTEXT_OFFER_CLASS', 110);
}

$capabilities = array(
    'local/offermanager:manage' => array(
        'riskbitmask' => RISK_SPAM | RISK_XSS,
        'captype' => 'write',
        'contextlevel' => CONTEXT_SYSTEM,
        'archetypes' => array(
            'manager' => CAP_ALLOW,
            'client' => CAP_ALLOW,
        ),
    ),

    'local/offermanager:viewparticipants' => [
        'captype' => 'write',
        'contextlevel' => CONTEXT_OFFER_CLASS,
        'archetypes' => [
            'manager' => CAP_ALLOW,
            'editingteacher' => CAP_ALLOW,
            'client' => CAP_ALLOW,
        ],
    ],
    'local/offermanager:manageparticipants' => [
        'captype' => 'write',
        'contextlevel' => CONTEXT_OFFER_CLASS,
        'archetypes' => [
            'manager' => CAP_ALLOW,
            'editingteacher' => CAP_ALLOW,
            'client' => CAP_ALLOW,
        ],
    ],
);