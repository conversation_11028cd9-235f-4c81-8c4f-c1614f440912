<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\persistent\trait;

/**
 * Trait class_reenrolment_trait
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
trait class_reenrolment_trait {

    public function get_allowed_reenrol_situations() : array
    {
        $reenrolsituations_str = $this->get_mapped_field('reenrolmentsituations');

        if(!$reenrolsituations_str){
            return [];
        }

        return explode(', ', $reenrolsituations_str);
    }

    public function allow_reenrol()
    {
        if ($this->get_mapped_field('enablereenrol') != 1) {
            return true;
        }

        $plugin = $this->get_plugin();
  
        if ($plugin->reenrol_available()) {
            return true;
        }

        return false;
    }
}
