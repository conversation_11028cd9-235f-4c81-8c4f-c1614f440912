define("local_offermanager/app/app-lazy",function(){"use strict";/**
* @vue/shared v3.5.13
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function er(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const st={}.NODE_ENV!=="production"?Object.freeze({}):{},Dn={}.NODE_ENV!=="production"?Object.freeze([]):[],Ot=()=>{},Lg=()=>!1,to=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),ni=e=>e.startsWith("onUpdate:"),pt=Object.assign,Ga=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},Bg=Object.prototype.hasOwnProperty,Ye=(e,t)=>Bg.call(e,t),ge=Array.isArray,Kr=e=>so(e)==="[object Map]",xn=e=>so(e)==="[object Set]",Pc=e=>so(e)==="[object Date]",xe=e=>typeof e=="function",dt=e=>typeof e=="string",As=e=>typeof e=="symbol",Je=e=>e!==null&&typeof e=="object",Ka=e=>(Je(e)||xe(e))&&xe(e.then)&&xe(e.catch),kc=Object.prototype.toString,so=e=>kc.call(e),Qa=e=>so(e).slice(8,-1),Vc=e=>so(e)==="[object Object]",Ya=e=>dt(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,ro=er(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),$g=er("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),oi=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},jg=/-(\w)/g,Kt=oi(e=>e.replace(jg,(t,s)=>s?s.toUpperCase():"")),Hg=/\B([A-Z])/g,Sr=oi(e=>e.replace(Hg,"-$1").toLowerCase()),Qr=oi(e=>e.charAt(0).toUpperCase()+e.slice(1)),Yr=oi(e=>e?`on${Qr(e)}`:""),Or=(e,t)=>!Object.is(e,t),Sn=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},ii=(e,t,s,i=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:i,value:s})},ai=e=>{const t=parseFloat(e);return isNaN(t)?e:t},qg=e=>{const t=dt(e)?Number(e):NaN;return isNaN(t)?e:t};let Rc;const no=()=>Rc||(Rc=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function us(e){if(ge(e)){const t={};for(let s=0;s<e.length;s++){const i=e[s],n=dt(i)?Kg(i):us(i);if(n)for(const a in n)t[a]=n[a]}return t}else if(dt(e)||Je(e))return e}const zg=/;(?![^(]*\))/g,Wg=/:([^]+)/,Gg=/\/\*[^]*?\*\//g;function Kg(e){const t={};return e.replace(Gg,"").split(zg).forEach(s=>{if(s){const i=s.split(Wg);i.length>1&&(t[i[0].trim()]=i[1].trim())}}),t}function pe(e){let t="";if(dt(e))t=e;else if(ge(e))for(let s=0;s<e.length;s++){const i=pe(e[s]);i&&(t+=i+" ")}else if(Je(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const Qg="html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot",Yg="svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view",Zg="annotation,annotation-xml,maction,maligngroup,malignmark,math,menclose,merror,mfenced,mfrac,mfraction,mglyph,mi,mlabeledtr,mlongdiv,mmultiscripts,mn,mo,mover,mpadded,mphantom,mprescripts,mroot,mrow,ms,mscarries,mscarry,msgroup,msline,mspace,msqrt,msrow,mstack,mstyle,msub,msubsup,msup,mtable,mtd,mtext,mtr,munder,munderover,none,semantics",Jg=er(Qg),Xg=er(Yg),ev=er(Zg),tv=er("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function Uc(e){return!!e||e===""}function sv(e,t){if(e.length!==t.length)return!1;let s=!0;for(let i=0;s&&i<e.length;i++)s=oo(e[i],t[i]);return s}function oo(e,t){if(e===t)return!0;let s=Pc(e),i=Pc(t);if(s||i)return s&&i?e.getTime()===t.getTime():!1;if(s=As(e),i=As(t),s||i)return e===t;if(s=ge(e),i=ge(t),s||i)return s&&i?sv(e,t):!1;if(s=Je(e),i=Je(t),s||i){if(!s||!i)return!1;const n=Object.keys(e).length,a=Object.keys(t).length;if(n!==a)return!1;for(const u in e){const c=e.hasOwnProperty(u),h=t.hasOwnProperty(u);if(c&&!h||!c&&h||!oo(e[u],t[u]))return!1}}return String(e)===String(t)}function Za(e,t){return e.findIndex(s=>oo(s,t))}const Fc=e=>!!(e&&e.__v_isRef===!0),q=e=>dt(e)?e:e==null?"":ge(e)||Je(e)&&(e.toString===kc||!xe(e.toString))?Fc(e)?q(e.value):JSON.stringify(e,Lc,2):String(e),Lc=(e,t)=>Fc(t)?Lc(e,t.value):Kr(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[i,n],a)=>(s[Ja(i,a)+" =>"]=n,s),{})}:xn(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>Ja(s))}:As(t)?Ja(t):Je(t)&&!ge(t)&&!Vc(t)?String(t):t,Ja=(e,t="")=>{var s;return As(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Hs(e,...t){console.warn(`[Vue warn] ${e}`,...t)}let cs;class Bc{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=cs,!t&&cs&&(this.index=(cs.scopes||(cs.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=cs;try{return cs=this,t()}finally{cs=s}}else({}).NODE_ENV!=="production"&&Hs("cannot run an inactive effect scope.")}on(){cs=this}off(){cs=this.parent}stop(t){if(this._active){this._active=!1;let s,i;for(s=0,i=this.effects.length;s<i;s++)this.effects[s].stop();for(this.effects.length=0,s=0,i=this.cleanups.length;s<i;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,i=this.scopes.length;s<i;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const n=this.parent.scopes.pop();n&&n!==this&&(this.parent.scopes[this.index]=n,n.index=this.index)}this.parent=void 0}}}function rv(e){return new Bc(e)}function nv(){return cs}let rt;const Xa=new WeakSet;class $c{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,cs&&cs.active&&cs.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Xa.has(this)&&(Xa.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Hc(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Kc(this),qc(this);const t=rt,s=Ms;rt=this,Ms=!0;try{return this.fn()}finally{({}).NODE_ENV!=="production"&&rt!==this&&Hs("Active effect was not restored correctly - this is likely a Vue internal bug."),zc(this),rt=t,Ms=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)rl(t);this.deps=this.depsTail=void 0,Kc(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Xa.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){sl(this)&&this.run()}get dirty(){return sl(this)}}let jc=0,io,ao;function Hc(e,t=!1){if(e.flags|=8,t){e.next=ao,ao=e;return}e.next=io,io=e}function el(){jc++}function tl(){if(--jc>0)return;if(ao){let t=ao;for(ao=void 0;t;){const s=t.next;t.next=void 0,t.flags&=-9,t=s}}let e;for(;io;){let t=io;for(io=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(i){e||(e=i)}t=s}}if(e)throw e}function qc(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function zc(e){let t,s=e.depsTail,i=s;for(;i;){const n=i.prevDep;i.version===-1?(i===s&&(s=n),rl(i),ov(i)):t=i,i.dep.activeLink=i.prevActiveLink,i.prevActiveLink=void 0,i=n}e.deps=t,e.depsTail=s}function sl(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Wc(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Wc(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===lo))return;e.globalVersion=lo;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!sl(e)){e.flags&=-3;return}const s=rt,i=Ms;rt=e,Ms=!0;try{qc(e);const n=e.fn(e._value);(t.version===0||Or(n,e._value))&&(e._value=n,t.version++)}catch(n){throw t.version++,n}finally{rt=s,Ms=i,zc(e),e.flags&=-3}}function rl(e,t=!1){const{dep:s,prevSub:i,nextSub:n}=e;if(i&&(i.nextSub=n,e.prevSub=void 0),n&&(n.prevSub=i,e.nextSub=void 0),{}.NODE_ENV!=="production"&&s.subsHead===e&&(s.subsHead=n),s.subs===e&&(s.subs=i,!i&&s.computed)){s.computed.flags&=-5;for(let a=s.computed.deps;a;a=a.nextDep)rl(a,!0)}!t&&!--s.sc&&s.map&&s.map.delete(s.key)}function ov(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let Ms=!0;const Gc=[];function tr(){Gc.push(Ms),Ms=!1}function sr(){const e=Gc.pop();Ms=e===void 0?!0:e}function Kc(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=rt;rt=void 0;try{t()}finally{rt=s}}}let lo=0;class iv{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class nl{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,{}.NODE_ENV!=="production"&&(this.subsHead=void 0)}track(t){if(!rt||!Ms||rt===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==rt)s=this.activeLink=new iv(rt,this),rt.deps?(s.prevDep=rt.depsTail,rt.depsTail.nextDep=s,rt.depsTail=s):rt.deps=rt.depsTail=s,Qc(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const i=s.nextDep;i.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=i),s.prevDep=rt.depsTail,s.nextDep=void 0,rt.depsTail.nextDep=s,rt.depsTail=s,rt.deps===s&&(rt.deps=i)}return{}.NODE_ENV!=="production"&&rt.onTrack&&rt.onTrack(pt({effect:rt},t)),s}trigger(t){this.version++,lo++,this.notify(t)}notify(t){el();try{if({}.NODE_ENV!=="production")for(let s=this.subsHead;s;s=s.nextSub)s.sub.onTrigger&&!(s.sub.flags&8)&&s.sub.onTrigger(pt({effect:s.sub},t));for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{tl()}}}function Qc(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let i=t.deps;i;i=i.nextDep)Qc(i)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),{}.NODE_ENV!=="production"&&e.dep.subsHead===void 0&&(e.dep.subsHead=e),e.dep.subs=e}}const ol=new WeakMap,Zr=Symbol({}.NODE_ENV!=="production"?"Object iterate":""),il=Symbol({}.NODE_ENV!=="production"?"Map keys iterate":""),uo=Symbol({}.NODE_ENV!=="production"?"Array iterate":"");function Tt(e,t,s){if(Ms&&rt){let i=ol.get(e);i||ol.set(e,i=new Map);let n=i.get(s);n||(i.set(s,n=new nl),n.map=i,n.key=s),{}.NODE_ENV!=="production"?n.track({target:e,type:t,key:s}):n.track()}}function qs(e,t,s,i,n,a){const u=ol.get(e);if(!u){lo++;return}const c=h=>{h&&({}.NODE_ENV!=="production"?h.trigger({target:e,type:t,key:s,newValue:i,oldValue:n,oldTarget:a}):h.trigger())};if(el(),t==="clear")u.forEach(c);else{const h=ge(e),m=h&&Ya(s);if(h&&s==="length"){const p=Number(i);u.forEach((v,w)=>{(w==="length"||w===uo||!As(w)&&w>=p)&&c(v)})}else switch((s!==void 0||u.has(void 0))&&c(u.get(s)),m&&c(u.get(uo)),t){case"add":h?m&&c(u.get("length")):(c(u.get(Zr)),Kr(e)&&c(u.get(il)));break;case"delete":h||(c(u.get(Zr)),Kr(e)&&c(u.get(il)));break;case"set":Kr(e)&&c(u.get(Zr));break}}tl()}function On(e){const t=Me(e);return t===e?t:(Tt(t,"iterate",uo),Qt(e)?t:t.map(Ht))}function li(e){return Tt(e=Me(e),"iterate",uo),e}const av={__proto__:null,[Symbol.iterator](){return al(this,Symbol.iterator,Ht)},concat(...e){return On(this).concat(...e.map(t=>ge(t)?On(t):t))},entries(){return al(this,"entries",e=>(e[1]=Ht(e[1]),e))},every(e,t){return rr(this,"every",e,t,void 0,arguments)},filter(e,t){return rr(this,"filter",e,t,s=>s.map(Ht),arguments)},find(e,t){return rr(this,"find",e,t,Ht,arguments)},findIndex(e,t){return rr(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return rr(this,"findLast",e,t,Ht,arguments)},findLastIndex(e,t){return rr(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return rr(this,"forEach",e,t,void 0,arguments)},includes(...e){return ll(this,"includes",e)},indexOf(...e){return ll(this,"indexOf",e)},join(e){return On(this).join(e)},lastIndexOf(...e){return ll(this,"lastIndexOf",e)},map(e,t){return rr(this,"map",e,t,void 0,arguments)},pop(){return co(this,"pop")},push(...e){return co(this,"push",e)},reduce(e,...t){return Yc(this,"reduce",e,t)},reduceRight(e,...t){return Yc(this,"reduceRight",e,t)},shift(){return co(this,"shift")},some(e,t){return rr(this,"some",e,t,void 0,arguments)},splice(...e){return co(this,"splice",e)},toReversed(){return On(this).toReversed()},toSorted(e){return On(this).toSorted(e)},toSpliced(...e){return On(this).toSpliced(...e)},unshift(...e){return co(this,"unshift",e)},values(){return al(this,"values",Ht)}};function al(e,t,s){const i=li(e),n=i[t]();return i!==e&&!Qt(e)&&(n._next=n.next,n.next=()=>{const a=n._next();return a.value&&(a.value=s(a.value)),a}),n}const lv=Array.prototype;function rr(e,t,s,i,n,a){const u=li(e),c=u!==e&&!Qt(e),h=u[t];if(h!==lv[t]){const v=h.apply(e,a);return c?Ht(v):v}let m=s;u!==e&&(c?m=function(v,w){return s.call(this,Ht(v),w,e)}:s.length>2&&(m=function(v,w){return s.call(this,v,w,e)}));const p=h.call(u,m,i);return c&&n?n(p):p}function Yc(e,t,s,i){const n=li(e);let a=s;return n!==e&&(Qt(e)?s.length>3&&(a=function(u,c,h){return s.call(this,u,c,h,e)}):a=function(u,c,h){return s.call(this,u,Ht(c),h,e)}),n[t](a,...i)}function ll(e,t,s){const i=Me(e);Tt(i,"iterate",uo);const n=i[t](...s);return(n===-1||n===!1)&&pi(s[0])?(s[0]=Me(s[0]),i[t](...s)):n}function co(e,t,s=[]){tr(),el();const i=Me(e)[t].apply(e,s);return tl(),sr(),i}const uv=er("__proto__,__v_isRef,__isVue"),Zc=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(As));function cv(e){As(e)||(e=String(e));const t=Me(this);return Tt(t,"has",e),t.hasOwnProperty(e)}class Jc{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,i){if(s==="__v_skip")return t.__v_skip;const n=this._isReadonly,a=this._isShallow;if(s==="__v_isReactive")return!n;if(s==="__v_isReadonly")return n;if(s==="__v_isShallow")return a;if(s==="__v_raw")return i===(n?a?od:nd:a?rd:sd).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(i)?t:void 0;const u=ge(t);if(!n){let h;if(u&&(h=av[s]))return h;if(s==="hasOwnProperty")return cv}const c=Reflect.get(t,s,Dt(t)?t:i);return(As(s)?Zc.has(s):uv(s))||(n||Tt(t,"get",s),a)?c:Dt(c)?u&&Ya(s)?c:c.value:Je(c)?n?ad(c):fi(c):c}}class Xc extends Jc{constructor(t=!1){super(!1,t)}set(t,s,i,n){let a=t[s];if(!this._isShallow){const h=nr(a);if(!Qt(i)&&!nr(i)&&(a=Me(a),i=Me(i)),!ge(t)&&Dt(a)&&!Dt(i))return h?!1:(a.value=i,!0)}const u=ge(t)&&Ya(s)?Number(s)<t.length:Ye(t,s),c=Reflect.set(t,s,i,Dt(t)?t:n);return t===Me(n)&&(u?Or(i,a)&&qs(t,"set",s,i,a):qs(t,"add",s,i)),c}deleteProperty(t,s){const i=Ye(t,s),n=t[s],a=Reflect.deleteProperty(t,s);return a&&i&&qs(t,"delete",s,void 0,n),a}has(t,s){const i=Reflect.has(t,s);return(!As(s)||!Zc.has(s))&&Tt(t,"has",s),i}ownKeys(t){return Tt(t,"iterate",ge(t)?"length":Zr),Reflect.ownKeys(t)}}class ed extends Jc{constructor(t=!1){super(!0,t)}set(t,s){return{}.NODE_ENV!=="production"&&Hs(`Set operation on key "${String(s)}" failed: target is readonly.`,t),!0}deleteProperty(t,s){return{}.NODE_ENV!=="production"&&Hs(`Delete operation on key "${String(s)}" failed: target is readonly.`,t),!0}}const dv=new Xc,fv=new ed,hv=new Xc(!0),pv=new ed(!0),ul=e=>e,ui=e=>Reflect.getPrototypeOf(e);function mv(e,t,s){return function(...i){const n=this.__v_raw,a=Me(n),u=Kr(a),c=e==="entries"||e===Symbol.iterator&&u,h=e==="keys"&&u,m=n[e](...i),p=s?ul:t?dl:Ht;return!t&&Tt(a,"iterate",h?il:Zr),{next(){const{value:v,done:w}=m.next();return w?{value:v,done:w}:{value:c?[p(v[0]),p(v[1])]:p(v),done:w}},[Symbol.iterator](){return this}}}}function ci(e){return function(...t){if({}.NODE_ENV!=="production"){const s=t[0]?`on key "${t[0]}" `:"";Hs(`${Qr(e)} operation ${s}failed: target is readonly.`,Me(this))}return e==="delete"?!1:e==="clear"?void 0:this}}function gv(e,t){const s={get(n){const a=this.__v_raw,u=Me(a),c=Me(n);e||(Or(n,c)&&Tt(u,"get",n),Tt(u,"get",c));const{has:h}=ui(u),m=t?ul:e?dl:Ht;if(h.call(u,n))return m(a.get(n));if(h.call(u,c))return m(a.get(c));a!==u&&a.get(n)},get size(){const n=this.__v_raw;return!e&&Tt(Me(n),"iterate",Zr),Reflect.get(n,"size",n)},has(n){const a=this.__v_raw,u=Me(a),c=Me(n);return e||(Or(n,c)&&Tt(u,"has",n),Tt(u,"has",c)),n===c?a.has(n):a.has(n)||a.has(c)},forEach(n,a){const u=this,c=u.__v_raw,h=Me(c),m=t?ul:e?dl:Ht;return!e&&Tt(h,"iterate",Zr),c.forEach((p,v)=>n.call(a,m(p),m(v),u))}};return pt(s,e?{add:ci("add"),set:ci("set"),delete:ci("delete"),clear:ci("clear")}:{add(n){!t&&!Qt(n)&&!nr(n)&&(n=Me(n));const a=Me(this);return ui(a).has.call(a,n)||(a.add(n),qs(a,"add",n,n)),this},set(n,a){!t&&!Qt(a)&&!nr(a)&&(a=Me(a));const u=Me(this),{has:c,get:h}=ui(u);let m=c.call(u,n);m?{}.NODE_ENV!=="production"&&td(u,c,n):(n=Me(n),m=c.call(u,n));const p=h.call(u,n);return u.set(n,a),m?Or(a,p)&&qs(u,"set",n,a,p):qs(u,"add",n,a),this},delete(n){const a=Me(this),{has:u,get:c}=ui(a);let h=u.call(a,n);h?{}.NODE_ENV!=="production"&&td(a,u,n):(n=Me(n),h=u.call(a,n));const m=c?c.call(a,n):void 0,p=a.delete(n);return h&&qs(a,"delete",n,void 0,m),p},clear(){const n=Me(this),a=n.size!==0,u={}.NODE_ENV!=="production"?Kr(n)?new Map(n):new Set(n):void 0,c=n.clear();return a&&qs(n,"clear",void 0,void 0,u),c}}),["keys","values","entries",Symbol.iterator].forEach(n=>{s[n]=mv(n,e,t)}),s}function di(e,t){const s=gv(e,t);return(i,n,a)=>n==="__v_isReactive"?!e:n==="__v_isReadonly"?e:n==="__v_raw"?i:Reflect.get(Ye(s,n)&&n in i?s:i,n,a)}const vv={get:di(!1,!1)},_v={get:di(!1,!0)},bv={get:di(!0,!1)},yv={get:di(!0,!0)};function td(e,t,s){const i=Me(s);if(i!==s&&t.call(e,i)){const n=Qa(e);Hs(`Reactive ${n} contains both the raw and reactive versions of the same object${n==="Map"?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}const sd=new WeakMap,rd=new WeakMap,nd=new WeakMap,od=new WeakMap;function wv(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Ev(e){return e.__v_skip||!Object.isExtensible(e)?0:wv(Qa(e))}function fi(e){return nr(e)?e:hi(e,!1,dv,vv,sd)}function id(e){return hi(e,!1,hv,_v,rd)}function ad(e){return hi(e,!0,fv,bv,nd)}function zs(e){return hi(e,!0,pv,yv,od)}function hi(e,t,s,i,n){if(!Je(e))return{}.NODE_ENV!=="production"&&Hs(`value cannot be made ${t?"readonly":"reactive"}: ${String(e)}`),e;if(e.__v_raw&&!(t&&e.__v_isReactive))return e;const a=n.get(e);if(a)return a;const u=Ev(e);if(u===0)return e;const c=new Proxy(e,u===2?i:s);return n.set(e,c),c}function Jr(e){return nr(e)?Jr(e.__v_raw):!!(e&&e.__v_isReactive)}function nr(e){return!!(e&&e.__v_isReadonly)}function Qt(e){return!!(e&&e.__v_isShallow)}function pi(e){return e?!!e.__v_raw:!1}function Me(e){const t=e&&e.__v_raw;return t?Me(t):e}function cl(e){return!Ye(e,"__v_skip")&&Object.isExtensible(e)&&ii(e,"__v_skip",!0),e}const Ht=e=>Je(e)?fi(e):e,dl=e=>Je(e)?ad(e):e;function Dt(e){return e?e.__v_isRef===!0:!1}function ld(e){return ud(e,!1)}function Cv(e){return ud(e,!0)}function ud(e,t){return Dt(e)?e:new Dv(e,t)}class Dv{constructor(t,s){this.dep=new nl,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:Me(t),this._value=s?t:Ht(t),this.__v_isShallow=s}get value(){return{}.NODE_ENV!=="production"?this.dep.track({target:this,type:"get",key:"value"}):this.dep.track(),this._value}set value(t){const s=this._rawValue,i=this.__v_isShallow||Qt(t)||nr(t);t=i?t:Me(t),Or(t,s)&&(this._rawValue=t,this._value=i?t:Ht(t),{}.NODE_ENV!=="production"?this.dep.trigger({target:this,type:"set",key:"value",newValue:t,oldValue:s}):this.dep.trigger())}}function Tr(e){return Dt(e)?e.value:e}const xv={get:(e,t,s)=>t==="__v_raw"?e:Tr(Reflect.get(e,t,s)),set:(e,t,s,i)=>{const n=e[t];return Dt(n)&&!Dt(s)?(n.value=s,!0):Reflect.set(e,t,s,i)}};function cd(e){return Jr(e)?e:new Proxy(e,xv)}class Sv{constructor(t,s,i){this.fn=t,this.setter=s,this._value=void 0,this.dep=new nl(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=lo-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=i}notify(){if(this.flags|=16,!(this.flags&8)&&rt!==this)return Hc(this,!0),!0}get value(){const t={}.NODE_ENV!=="production"?this.dep.track({target:this,type:"get",key:"value"}):this.dep.track();return Wc(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter?this.setter(t):{}.NODE_ENV!=="production"&&Hs("Write operation failed: computed value is readonly")}}function Ov(e,t,s=!1){let i,n;xe(e)?i=e:(i=e.get,n=e.set);const a=new Sv(i,n,s);return{}.NODE_ENV!=="production"&&t&&!s&&(a.onTrack=t.onTrack,a.onTrigger=t.onTrigger),a}const mi={},gi=new WeakMap;let Xr;function Tv(e,t=!1,s=Xr){if(s){let i=gi.get(s);i||gi.set(s,i=[]),i.push(e)}else({}).NODE_ENV!=="production"&&!t&&Hs("onWatcherCleanup() was called when there was no active watcher to associate with.")}function Nv(e,t,s=st){const{immediate:i,deep:n,once:a,scheduler:u,augmentJob:c,call:h}=s,m=Z=>{(s.onWarn||Hs)("Invalid watch source: ",Z,"A watch source can only be a getter/effect function, a ref, a reactive object, or an array of these types.")},p=Z=>n?Z:Qt(Z)||n===!1||n===0?or(Z,1):or(Z);let v,w,D,k,L=!1,re=!1;if(Dt(e)?(w=()=>e.value,L=Qt(e)):Jr(e)?(w=()=>p(e),L=!0):ge(e)?(re=!0,L=e.some(Z=>Jr(Z)||Qt(Z)),w=()=>e.map(Z=>{if(Dt(Z))return Z.value;if(Jr(Z))return p(Z);if(xe(Z))return h?h(Z,2):Z();({}).NODE_ENV!=="production"&&m(Z)})):xe(e)?t?w=h?()=>h(e,2):e:w=()=>{if(D){tr();try{D()}finally{sr()}}const Z=Xr;Xr=v;try{return h?h(e,3,[k]):e(k)}finally{Xr=Z}}:(w=Ot,{}.NODE_ENV!=="production"&&m(e)),t&&n){const Z=w,he=n===!0?1/0:n;w=()=>or(Z(),he)}const I=nv(),ne=()=>{v.stop(),I&&I.active&&Ga(I.effects,v)};if(a&&t){const Z=t;t=(...he)=>{Z(...he),ne()}}let Q=re?new Array(e.length).fill(mi):mi;const ye=Z=>{if(!(!(v.flags&1)||!v.dirty&&!Z))if(t){const he=v.run();if(n||L||(re?he.some((be,Ae)=>Or(be,Q[Ae])):Or(he,Q))){D&&D();const be=Xr;Xr=v;try{const Ae=[he,Q===mi?void 0:re&&Q[0]===mi?[]:Q,k];h?h(t,3,Ae):t(...Ae),Q=he}finally{Xr=be}}}else v.run()};return c&&c(ye),v=new $c(w),v.scheduler=u?()=>u(ye,!1):ye,k=Z=>Tv(Z,!1,v),D=v.onStop=()=>{const Z=gi.get(v);if(Z){if(h)h(Z,4);else for(const he of Z)he();gi.delete(v)}},{}.NODE_ENV!=="production"&&(v.onTrack=s.onTrack,v.onTrigger=s.onTrigger),t?i?ye(!0):Q=v.run():u?u(ye.bind(null,!0),!0):v.run(),ne.pause=v.pause.bind(v),ne.resume=v.resume.bind(v),ne.stop=ne,ne}function or(e,t=1/0,s){if(t<=0||!Je(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,Dt(e))or(e.value,t,s);else if(ge(e))for(let i=0;i<e.length;i++)or(e[i],t,s);else if(xn(e)||Kr(e))e.forEach(i=>{or(i,t,s)});else if(Vc(e)){for(const i in e)or(e[i],t,s);for(const i of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,i)&&or(e[i],t,s)}return e}/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const en=[];function vi(e){en.push(e)}function _i(){en.pop()}let fl=!1;function Y(e,...t){if(fl)return;fl=!0,tr();const s=en.length?en[en.length-1].component:null,i=s&&s.appContext.config.warnHandler,n=Iv();if(i)Tn(i,s,11,[e+t.map(a=>{var u,c;return(c=(u=a.toString)==null?void 0:u.call(a))!=null?c:JSON.stringify(a)}).join(""),s&&s.proxy,n.map(({vnode:a})=>`at <${Fi(s,a.type)}>`).join(`
`),n]);else{const a=[`[Vue warn]: ${e}`,...t];n.length&&a.push(`
`,...Av(n)),console.warn(...a)}sr(),fl=!1}function Iv(){let e=en[en.length-1];if(!e)return[];const t=[];for(;e;){const s=t[0];s&&s.vnode===e?s.recurseCount++:t.push({vnode:e,recurseCount:0});const i=e.component&&e.component.parent;e=i&&i.vnode}return t}function Av(e){const t=[];return e.forEach((s,i)=>{t.push(...i===0?[]:[`
`],...Mv(s))}),t}function Mv({vnode:e,recurseCount:t}){const s=t>0?`... (${t} recursive calls)`:"",i=e.component?e.component.parent==null:!1,n=` at <${Fi(e.component,e.type,i)}`,a=">"+s;return e.props?[n,...Pv(e.props),a]:[n+a]}function Pv(e){const t=[],s=Object.keys(e);return s.slice(0,3).forEach(i=>{t.push(...dd(i,e[i]))}),s.length>3&&t.push(" ..."),t}function dd(e,t,s){return dt(t)?(t=JSON.stringify(t),s?t:[`${e}=${t}`]):typeof t=="number"||typeof t=="boolean"||t==null?s?t:[`${e}=${t}`]:Dt(t)?(t=dd(e,Me(t.value),!0),s?t:[`${e}=Ref<`,t,">"]):xe(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=Me(t),s?t:[`${e}=`,t])}function kv(e,t){({}).NODE_ENV!=="production"&&e!==void 0&&(typeof e!="number"?Y(`${t} is not a valid number - got ${JSON.stringify(e)}.`):isNaN(e)&&Y(`${t} is NaN - the duration expression might be incorrect.`))}const hl={sp:"serverPrefetch hook",bc:"beforeCreate hook",c:"created hook",bm:"beforeMount hook",m:"mounted hook",bu:"beforeUpdate hook",u:"updated",bum:"beforeUnmount hook",um:"unmounted hook",a:"activated hook",da:"deactivated hook",ec:"errorCaptured hook",rtc:"renderTracked hook",rtg:"renderTriggered hook",0:"setup function",1:"render function",2:"watcher getter",3:"watcher callback",4:"watcher cleanup function",5:"native event handler",6:"component event handler",7:"vnode hook",8:"directive hook",9:"transition hook",10:"app errorHandler",11:"app warnHandler",12:"ref function",13:"async component loader",14:"scheduler flush",15:"component update",16:"app unmount cleanup function"};function Tn(e,t,s,i){try{return i?e(...i):e()}catch(n){fo(n,t,s)}}function Ps(e,t,s,i){if(xe(e)){const n=Tn(e,t,s,i);return n&&Ka(n)&&n.catch(a=>{fo(a,t,s)}),n}if(ge(e)){const n=[];for(let a=0;a<e.length;a++)n.push(Ps(e[a],t,s,i));return n}else({}).NODE_ENV!=="production"&&Y(`Invalid value type passed to callWithAsyncErrorHandling(): ${typeof e}`)}function fo(e,t,s,i=!0){const n=t?t.vnode:null,{errorHandler:a,throwUnhandledErrorInProduction:u}=t&&t.appContext.config||st;if(t){let c=t.parent;const h=t.proxy,m={}.NODE_ENV!=="production"?hl[s]:`https://vuejs.org/error-reference/#runtime-${s}`;for(;c;){const p=c.ec;if(p){for(let v=0;v<p.length;v++)if(p[v](e,h,m)===!1)return}c=c.parent}if(a){tr(),Tn(a,null,10,[e,h,m]),sr();return}}Vv(e,s,n,i,u)}function Vv(e,t,s,i=!0,n=!1){if({}.NODE_ENV!=="production"){const a=hl[t];if(s&&vi(s),Y(`Unhandled error${a?` during execution of ${a}`:""}`),s&&_i(),i)throw e;console.error(e)}else{if(n)throw e;console.error(e)}}const Yt=[];let Ws=-1;const Nn=[];let Nr=null,In=0;const fd=Promise.resolve();let bi=null;const Rv=100;function pl(e){const t=bi||fd;return e?t.then(this?e.bind(this):e):t}function Uv(e){let t=Ws+1,s=Yt.length;for(;t<s;){const i=t+s>>>1,n=Yt[i],a=ho(n);a<e||a===e&&n.flags&2?t=i+1:s=i}return t}function yi(e){if(!(e.flags&1)){const t=ho(e),s=Yt[Yt.length-1];!s||!(e.flags&2)&&t>=ho(s)?Yt.push(e):Yt.splice(Uv(t),0,e),e.flags|=1,hd()}}function hd(){bi||(bi=fd.then(vd))}function pd(e){ge(e)?Nn.push(...e):Nr&&e.id===-1?Nr.splice(In+1,0,e):e.flags&1||(Nn.push(e),e.flags|=1),hd()}function md(e,t,s=Ws+1){for({}.NODE_ENV!=="production"&&(t=t||new Map);s<Yt.length;s++){const i=Yt[s];if(i&&i.flags&2){if(e&&i.id!==e.uid||{}.NODE_ENV!=="production"&&ml(t,i))continue;Yt.splice(s,1),s--,i.flags&4&&(i.flags&=-2),i(),i.flags&4||(i.flags&=-2)}}}function gd(e){if(Nn.length){const t=[...new Set(Nn)].sort((s,i)=>ho(s)-ho(i));if(Nn.length=0,Nr){Nr.push(...t);return}for(Nr=t,{}.NODE_ENV!=="production"&&(e=e||new Map),In=0;In<Nr.length;In++){const s=Nr[In];({}).NODE_ENV!=="production"&&ml(e,s)||(s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2)}Nr=null,In=0}}const ho=e=>e.id==null?e.flags&2?-1:1/0:e.id;function vd(e){({}).NODE_ENV!=="production"&&(e=e||new Map);const t={}.NODE_ENV!=="production"?s=>ml(e,s):Ot;try{for(Ws=0;Ws<Yt.length;Ws++){const s=Yt[Ws];if(s&&!(s.flags&8)){if({}.NODE_ENV!=="production"&&t(s))continue;s.flags&4&&(s.flags&=-2),Tn(s,s.i,s.i?15:14),s.flags&4||(s.flags&=-2)}}}finally{for(;Ws<Yt.length;Ws++){const s=Yt[Ws];s&&(s.flags&=-2)}Ws=-1,Yt.length=0,gd(e),bi=null,(Yt.length||Nn.length)&&vd(e)}}function ml(e,t){const s=e.get(t)||0;if(s>Rv){const i=t.i,n=i&&$l(i.type);return fo(`Maximum recursive updates exceeded${n?` in component <${n}>`:""}. This means you have a reactive effect that is mutating its own dependencies and thus recursively triggering itself. Possible sources include component template, render function, updated hook or watcher source function.`,null,10),!0}return e.set(t,s+1),!1}let ks=!1;const wi=new Map;({}).NODE_ENV!=="production"&&(no().__VUE_HMR_RUNTIME__={createRecord:gl(_d),rerender:gl(Bv),reload:gl($v)});const tn=new Map;function Fv(e){const t=e.type.__hmrId;let s=tn.get(t);s||(_d(t,e.type),s=tn.get(t)),s.instances.add(e)}function Lv(e){tn.get(e.type.__hmrId).instances.delete(e)}function _d(e,t){return tn.has(e)?!1:(tn.set(e,{initialDef:Ei(t),instances:new Set}),!0)}function Ei(e){return Af(e)?e.__vccOpts:e}function Bv(e,t){const s=tn.get(e);s&&(s.initialDef.render=t,[...s.instances].forEach(i=>{t&&(i.render=t,Ei(i.type).render=t),i.renderCache=[],ks=!0,i.update(),ks=!1}))}function $v(e,t){const s=tn.get(e);if(!s)return;t=Ei(t),bd(s.initialDef,t);const i=[...s.instances];for(let n=0;n<i.length;n++){const a=i[n],u=Ei(a.type);let c=wi.get(u);c||(u!==s.initialDef&&bd(u,t),wi.set(u,c=new Set)),c.add(a),a.appContext.propsCache.delete(a.type),a.appContext.emitsCache.delete(a.type),a.appContext.optionsCache.delete(a.type),a.ceReload?(c.add(a),a.ceReload(t.styles),c.delete(a)):a.parent?yi(()=>{ks=!0,a.parent.update(),ks=!1,c.delete(a)}):a.appContext.reload?a.appContext.reload():typeof window<"u"?window.location.reload():console.warn("[HMR] Root or manually mounted instance modified. Full reload required."),a.root.ce&&a!==a.root&&a.root.ce._removeChildStyle(u)}pd(()=>{wi.clear()})}function bd(e,t){pt(e,t);for(const s in e)s!=="__file"&&!(s in t)&&delete e[s]}function gl(e){return(t,s)=>{try{return e(t,s)}catch(i){console.error(i),console.warn("[HMR] Something went wrong during Vue component hot-reload. Full reload required.")}}}let Gs,po=[],vl=!1;function mo(e,...t){Gs?Gs.emit(e,...t):vl||po.push({event:e,args:t})}function yd(e,t){var s,i;Gs=e,Gs?(Gs.enabled=!0,po.forEach(({event:n,args:a})=>Gs.emit(n,...a)),po=[]):typeof window<"u"&&window.HTMLElement&&!((i=(s=window.navigator)==null?void 0:s.userAgent)!=null&&i.includes("jsdom"))?((t.__VUE_DEVTOOLS_HOOK_REPLAY__=t.__VUE_DEVTOOLS_HOOK_REPLAY__||[]).push(a=>{yd(a,t)}),setTimeout(()=>{Gs||(t.__VUE_DEVTOOLS_HOOK_REPLAY__=null,vl=!0,po=[])},3e3)):(vl=!0,po=[])}function jv(e,t){mo("app:init",e,t,{Fragment:Ie,Text:wo,Comment:wt,Static:Eo})}function Hv(e){mo("app:unmount",e)}const qv=_l("component:added"),wd=_l("component:updated"),zv=_l("component:removed"),Wv=e=>{Gs&&typeof Gs.cleanupBuffer=="function"&&!Gs.cleanupBuffer(e)&&zv(e)};/*! #__NO_SIDE_EFFECTS__ */function _l(e){return t=>{mo(e,t.appContext.app,t.uid,t.parent?t.parent.uid:void 0,t)}}const Gv=Ed("perf:start"),Kv=Ed("perf:end");function Ed(e){return(t,s,i)=>{mo(e,t.appContext.app,t.uid,t,s,i)}}function Qv(e,t,s){mo("component:emit",e.appContext.app,e,t,s)}let xt=null,Cd=null;function Ci(e){const t=xt;return xt=e,Cd=e&&e.type.__scopeId||null,t}function Ne(e,t=xt,s){if(!t||e._n)return e;const i=(...n)=>{i._d&&wf(-1);const a=Ci(t);let u;try{u=e(...n)}finally{Ci(a),i._d&&wf(1)}return{}.NODE_ENV!=="production"&&wd(t),u};return i._n=!0,i._c=!0,i._d=!0,i}function Dd(e){$g(e)&&Y("Do not use built-in directive ids as custom directive id: "+e)}function ut(e,t){if(xt===null)return{}.NODE_ENV!=="production"&&Y("withDirectives can only be used inside render functions."),e;const s=Ui(xt),i=e.dirs||(e.dirs=[]);for(let n=0;n<t.length;n++){let[a,u,c,h=st]=t[n];a&&(xe(a)&&(a={mounted:a,updated:a}),a.deep&&or(u),i.push({dir:a,instance:s,value:u,oldValue:void 0,arg:c,modifiers:h}))}return e}function sn(e,t,s,i){const n=e.dirs,a=t&&t.dirs;for(let u=0;u<n.length;u++){const c=n[u];a&&(c.oldValue=a[u].value);let h=c.dir[i];h&&(tr(),Ps(h,s,8,[e.el,c,e,t]),sr())}}const xd=Symbol("_vte"),Sd=e=>e.__isTeleport,rn=e=>e&&(e.disabled||e.disabled===""),Od=e=>e&&(e.defer||e.defer===""),Td=e=>typeof SVGElement<"u"&&e instanceof SVGElement,Nd=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,bl=(e,t)=>{const s=e&&e.to;if(dt(s))if(t){const i=t(s);return{}.NODE_ENV!=="production"&&!i&&!rn(e)&&Y(`Failed to locate Teleport target with selector "${s}". Note the target element must exist before the component is mounted - i.e. the target cannot be rendered by the component itself, and ideally should be outside of the entire Vue component tree.`),i}else return{}.NODE_ENV!=="production"&&Y("Current renderer does not support string target for Teleports. (missing querySelector renderer option)"),null;else return{}.NODE_ENV!=="production"&&!s&&!rn(e)&&Y(`Invalid Teleport target: ${s}`),s},Id={name:"Teleport",__isTeleport:!0,process(e,t,s,i,n,a,u,c,h,m){const{mc:p,pc:v,pbc:w,o:{insert:D,querySelector:k,createText:L,createComment:re}}=m,I=rn(t.props);let{shapeFlag:ne,children:Q,dynamicChildren:ye}=t;if({}.NODE_ENV!=="production"&&ks&&(h=!1,ye=null),e==null){const Z=t.el={}.NODE_ENV!=="production"?re("teleport start"):L(""),he=t.anchor={}.NODE_ENV!=="production"?re("teleport end"):L("");D(Z,s,i),D(he,s,i);const be=(ue,ae)=>{ne&16&&(n&&n.isCE&&(n.ce._teleportTarget=ue),p(Q,ue,ae,n,a,u,c,h))},Ae=()=>{const ue=t.target=bl(t.props,k),ae=Ad(ue,t,L,D);ue?(u!=="svg"&&Td(ue)?u="svg":u!=="mathml"&&Nd(ue)&&(u="mathml"),I||(be(ue,ae),xi(t,!1))):{}.NODE_ENV!=="production"&&!I&&Y("Invalid Teleport target on mount:",ue,`(${typeof ue})`)};I&&(be(s,he),xi(t,!0)),Od(t.props)?Jt(()=>{Ae(),t.el.__isMounted=!0},a):Ae()}else{if(Od(t.props)&&!e.el.__isMounted){Jt(()=>{Id.process(e,t,s,i,n,a,u,c,h,m),delete e.el.__isMounted},a);return}t.el=e.el,t.targetStart=e.targetStart;const Z=t.anchor=e.anchor,he=t.target=e.target,be=t.targetAnchor=e.targetAnchor,Ae=rn(e.props),ue=Ae?s:he,ae=Ae?Z:be;if(u==="svg"||Td(he)?u="svg":(u==="mathml"||Nd(he))&&(u="mathml"),ye?(w(e.dynamicChildren,ye,ue,n,a,u,c),yo(e,t,!0)):h||v(e,t,ue,ae,n,a,u,c,!1),I)Ae?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):Di(t,s,Z,m,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const V=t.target=bl(t.props,k);V?Di(t,V,null,m,0):{}.NODE_ENV!=="production"&&Y("Invalid Teleport target on update:",he,`(${typeof he})`)}else Ae&&Di(t,he,be,m,1);xi(t,I)}},remove(e,t,s,{um:i,o:{remove:n}},a){const{shapeFlag:u,children:c,anchor:h,targetStart:m,targetAnchor:p,target:v,props:w}=e;if(v&&(n(m),n(p)),a&&n(h),u&16){const D=a||!rn(w);for(let k=0;k<c.length;k++){const L=c[k];i(L,t,s,D,!!L.dynamicChildren)}}},move:Di,hydrate:Yv};function Di(e,t,s,{o:{insert:i},m:n},a=2){a===0&&i(e.targetAnchor,t,s);const{el:u,anchor:c,shapeFlag:h,children:m,props:p}=e,v=a===2;if(v&&i(u,t,s),(!v||rn(p))&&h&16)for(let w=0;w<m.length;w++)n(m[w],t,s,2);v&&i(c,t,s)}function Yv(e,t,s,i,n,a,{o:{nextSibling:u,parentNode:c,querySelector:h,insert:m,createText:p}},v){const w=t.target=bl(t.props,h);if(w){const D=rn(t.props),k=w._lpa||w.firstChild;if(t.shapeFlag&16)if(D)t.anchor=v(u(e),t,c(e),s,i,n,a),t.targetStart=k,t.targetAnchor=k&&u(k);else{t.anchor=u(e);let L=k;for(;L;){if(L&&L.nodeType===8){if(L.data==="teleport start anchor")t.targetStart=L;else if(L.data==="teleport anchor"){t.targetAnchor=L,w._lpa=t.targetAnchor&&u(t.targetAnchor);break}}L=u(L)}t.targetAnchor||Ad(w,t,p,m),v(k&&u(k),t,w,s,i,n,a)}xi(t,D)}return t.anchor&&u(t.anchor)}const Zv=Id;function xi(e,t){const s=e.ctx;if(s&&s.ut){let i,n;for(t?(i=e.el,n=e.anchor):(i=e.targetStart,n=e.targetAnchor);i&&i!==n;)i.nodeType===1&&i.setAttribute("data-v-owner",s.uid),i=i.nextSibling;s.ut()}}function Ad(e,t,s,i){const n=t.targetStart=s(""),a=t.targetAnchor=s("");return n[xd]=a,e&&(i(n,e),i(a,e)),a}const Ir=Symbol("_leaveCb"),Si=Symbol("_enterCb");function Jv(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return $d(()=>{e.isMounted=!0}),jd(()=>{e.isUnmounting=!0}),e}const ws=[Function,Array],Md={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:ws,onEnter:ws,onAfterEnter:ws,onEnterCancelled:ws,onBeforeLeave:ws,onLeave:ws,onAfterLeave:ws,onLeaveCancelled:ws,onBeforeAppear:ws,onAppear:ws,onAfterAppear:ws,onAppearCancelled:ws},Pd=e=>{const t=e.subTree;return t.component?Pd(t.component):t},Xv={name:"BaseTransition",props:Md,setup(e,{slots:t}){const s=Vi(),i=Jv();return()=>{const n=t.default&&Ud(t.default(),!0);if(!n||!n.length)return;const a=kd(n),u=Me(e),{mode:c}=u;if({}.NODE_ENV!=="production"&&c&&c!=="in-out"&&c!=="out-in"&&c!=="default"&&Y(`invalid <transition> mode: ${c}`),i.isLeaving)return wl(a);const h=Rd(a);if(!h)return wl(a);let m=yl(h,u,i,s,v=>m=v);h.type!==wt&&go(h,m);let p=s.subTree&&Rd(s.subTree);if(p&&p.type!==wt&&!ln(h,p)&&Pd(s).type!==wt){let v=yl(p,u,i,s);if(go(p,v),c==="out-in"&&h.type!==wt)return i.isLeaving=!0,v.afterLeave=()=>{i.isLeaving=!1,s.job.flags&8||s.update(),delete v.afterLeave,p=void 0},wl(a);c==="in-out"&&h.type!==wt?v.delayLeave=(w,D,k)=>{const L=Vd(i,p);L[String(p.key)]=p,w[Ir]=()=>{D(),w[Ir]=void 0,delete m.delayedLeave,p=void 0},m.delayedLeave=()=>{k(),delete m.delayedLeave,p=void 0}}:p=void 0}else p&&(p=void 0);return a}}};function kd(e){let t=e[0];if(e.length>1){let s=!1;for(const i of e)if(i.type!==wt){if({}.NODE_ENV!=="production"&&s){Y("<transition> can only be used on a single element or component. Use <transition-group> for lists.");break}if(t=i,s=!0,{}.NODE_ENV==="production")break}}return t}const e_=Xv;function Vd(e,t){const{leavingVNodes:s}=e;let i=s.get(t.type);return i||(i=Object.create(null),s.set(t.type,i)),i}function yl(e,t,s,i,n){const{appear:a,mode:u,persisted:c=!1,onBeforeEnter:h,onEnter:m,onAfterEnter:p,onEnterCancelled:v,onBeforeLeave:w,onLeave:D,onAfterLeave:k,onLeaveCancelled:L,onBeforeAppear:re,onAppear:I,onAfterAppear:ne,onAppearCancelled:Q}=t,ye=String(e.key),Z=Vd(s,e),he=(ue,ae)=>{ue&&Ps(ue,i,9,ae)},be=(ue,ae)=>{const V=ae[1];he(ue,ae),ge(ue)?ue.every(te=>te.length<=1)&&V():ue.length<=1&&V()},Ae={mode:u,persisted:c,beforeEnter(ue){let ae=h;if(!s.isMounted)if(a)ae=re||h;else return;ue[Ir]&&ue[Ir](!0);const V=Z[ye];V&&ln(e,V)&&V.el[Ir]&&V.el[Ir](),he(ae,[ue])},enter(ue){let ae=m,V=p,te=v;if(!s.isMounted)if(a)ae=I||m,V=ne||p,te=Q||v;else return;let Ke=!1;const vt=ue[Si]=mt=>{Ke||(Ke=!0,mt?he(te,[ue]):he(V,[ue]),Ae.delayedLeave&&Ae.delayedLeave(),ue[Si]=void 0)};ae?be(ae,[ue,vt]):vt()},leave(ue,ae){const V=String(e.key);if(ue[Si]&&ue[Si](!0),s.isUnmounting)return ae();he(w,[ue]);let te=!1;const Ke=ue[Ir]=vt=>{te||(te=!0,ae(),vt?he(L,[ue]):he(k,[ue]),ue[Ir]=void 0,Z[V]===e&&delete Z[V])};Z[V]=e,D?be(D,[ue,Ke]):Ke()},clone(ue){const ae=yl(ue,t,s,i,n);return n&&n(ae),ae}};return Ae}function wl(e){if(vo(e))return e=Ks(e),e.children=null,e}function Rd(e){if(!vo(e))return Sd(e.type)&&e.children?kd(e.children):e;if({}.NODE_ENV!=="production"&&e.component)return e.component.subTree;const{shapeFlag:t,children:s}=e;if(s){if(t&16)return s[0];if(t&32&&xe(s.default))return s.default()}}function go(e,t){e.shapeFlag&6&&e.component?(e.transition=t,go(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Ud(e,t=!1,s){let i=[],n=0;for(let a=0;a<e.length;a++){let u=e[a];const c=s==null?u.key:String(s)+String(u.key!=null?u.key:a);u.type===Ie?(u.patchFlag&128&&n++,i=i.concat(Ud(u.children,t,c))):(t||u.type!==wt)&&i.push(c!=null?Ks(u,{key:c}):u)}if(n>1)for(let a=0;a<i.length;a++)i[a].patchFlag=-2;return i}/*! #__NO_SIDE_EFFECTS__ */function Fd(e,t){return xe(e)?(()=>pt({name:e.name},t,{setup:e}))():e}function Ld(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}const t_=new WeakSet;function Oi(e,t,s,i,n=!1){if(ge(e)){e.forEach((k,L)=>Oi(k,t&&(ge(t)?t[L]:t),s,i,n));return}if(An(i)&&!n){i.shapeFlag&512&&i.type.__asyncResolved&&i.component.subTree.component&&Oi(e,t,s,i.component.subTree);return}const a=i.shapeFlag&4?Ui(i.component):i.el,u=n?null:a,{i:c,r:h}=e;if({}.NODE_ENV!=="production"&&!c){Y("Missing ref owner context. ref cannot be used on hoisted vnodes. A vnode with ref must be created inside the render function.");return}const m=t&&t.r,p=c.refs===st?c.refs={}:c.refs,v=c.setupState,w=Me(v),D=v===st?()=>!1:k=>({}).NODE_ENV!=="production"&&(Ye(w,k)&&!Dt(w[k])&&Y(`Template ref "${k}" used on a non-ref value. It will not work in the production build.`),t_.has(w[k]))?!1:Ye(w,k);if(m!=null&&m!==h&&(dt(m)?(p[m]=null,D(m)&&(v[m]=null)):Dt(m)&&(m.value=null)),xe(h))Tn(h,c,12,[u,p]);else{const k=dt(h),L=Dt(h);if(k||L){const re=()=>{if(e.f){const I=k?D(h)?v[h]:p[h]:h.value;n?ge(I)&&Ga(I,a):ge(I)?I.includes(a)||I.push(a):k?(p[h]=[a],D(h)&&(v[h]=p[h])):(h.value=[a],e.k&&(p[e.k]=h.value))}else k?(p[h]=u,D(h)&&(v[h]=u)):L?(h.value=u,e.k&&(p[e.k]=u)):{}.NODE_ENV!=="production"&&Y("Invalid template ref type:",h,`(${typeof h})`)};u?(re.id=-1,Jt(re,s)):re()}else({}).NODE_ENV!=="production"&&Y("Invalid template ref type:",h,`(${typeof h})`)}}no().requestIdleCallback,no().cancelIdleCallback;const An=e=>!!e.type.__asyncLoader,vo=e=>e.type.__isKeepAlive;function s_(e,t){Bd(e,"a",t)}function r_(e,t){Bd(e,"da",t)}function Bd(e,t,s=Nt){const i=e.__wdc||(e.__wdc=()=>{let n=s;for(;n;){if(n.isDeactivated)return;n=n.parent}return e()});if(Ti(t,i,s),s){let n=s.parent;for(;n&&n.parent;)vo(n.parent.vnode)&&n_(i,t,s,n),n=n.parent}}function n_(e,t,s,i){const n=Ti(t,e,i,!0);Hd(()=>{Ga(i[t],n)},s)}function Ti(e,t,s=Nt,i=!1){if(s){const n=s[e]||(s[e]=[]),a=t.__weh||(t.__weh=(...u)=>{tr();const c=xo(s),h=Ps(t,s,e,u);return c(),sr(),h});return i?n.unshift(a):n.push(a),a}else if({}.NODE_ENV!=="production"){const n=Yr(hl[e].replace(/ hook$/,""));Y(`${n} is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.`)}}const ir=e=>(t,s=Nt)=>{(!So||e==="sp")&&Ti(e,(...i)=>t(...i),s)},o_=ir("bm"),$d=ir("m"),i_=ir("bu"),a_=ir("u"),jd=ir("bum"),Hd=ir("um"),l_=ir("sp"),u_=ir("rtg"),c_=ir("rtc");function d_(e,t=Nt){Ti("ec",e,t)}const El="components",f_="directives";function X(e,t){return qd(El,e,!0,t)||e}const h_=Symbol.for("v-ndc");function p_(e){return qd(f_,e)}function qd(e,t,s=!0,i=!1){const n=xt||Nt;if(n){const a=n.type;if(e===El){const c=$l(a,!1);if(c&&(c===t||c===Kt(t)||c===Qr(Kt(t))))return a}const u=zd(n[e]||a[e],t)||zd(n.appContext[e],t);if(!u&&i)return a;if({}.NODE_ENV!=="production"&&s&&!u){const c=e===El?`
If this is a native custom element, make sure to exclude it from component resolution via compilerOptions.isCustomElement.`:"";Y(`Failed to resolve ${e.slice(0,-1)}: ${t}${c}`)}return u}else({}).NODE_ENV!=="production"&&Y(`resolve${Qr(e.slice(0,-1))} can only be used in render() or setup().`)}function zd(e,t){return e&&(e[t]||e[Kt(t)]||e[Qr(Kt(t))])}function at(e,t,s,i){let n;const a=s&&s[i],u=ge(e);if(u||dt(e)){const c=u&&Jr(e);let h=!1;c&&(h=!Qt(e),e=li(e)),n=new Array(e.length);for(let m=0,p=e.length;m<p;m++)n[m]=t(h?Ht(e[m]):e[m],m,void 0,a&&a[m])}else if(typeof e=="number"){({}).NODE_ENV!=="production"&&!Number.isInteger(e)&&Y(`The v-for range expect an integer value but got ${e}.`),n=new Array(e);for(let c=0;c<e;c++)n[c]=t(c+1,c,void 0,a&&a[c])}else if(Je(e))if(e[Symbol.iterator])n=Array.from(e,(c,h)=>t(c,h,void 0,a&&a[h]));else{const c=Object.keys(e);n=new Array(c.length);for(let h=0,m=c.length;h<m;h++){const p=c[h];n[h]=t(e[p],p,h,a&&a[h])}}else n=[];return s&&(s[i]=n),n}function Rt(e,t,s={},i,n){if(xt.ce||xt.parent&&An(xt.parent)&&xt.parent.ce)return t!=="default"&&(s.name=t),S(),Ut(Ie,null,[M("slot",s,i&&i())],64);let a=e[t];({}).NODE_ENV!=="production"&&a&&a.length>1&&(Y("SSR-optimized slot function detected in a non-SSR-optimized render function. You need to mark this component with $dynamic-slots in the parent template."),a=()=>[]),a&&a._c&&(a._d=!1),S();const u=a&&Wd(a(s)),c=s.key||u&&u.key,h=Ut(Ie,{key:(c&&!As(c)?c:`_${t}`)+(!u&&i?"_fb":"")},u||(i?i():[]),u&&e._===1?64:-2);return!n&&h.scopeId&&(h.slotScopeIds=[h.scopeId+"-s"]),a&&a._c&&(a._d=!0),h}function Wd(e){return e.some(t=>an(t)?!(t.type===wt||t.type===Ie&&!Wd(t.children)):!0)?e:null}const Cl=e=>e?Of(e)?Ui(e):Cl(e.parent):null,nn=pt(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>({}).NODE_ENV!=="production"?zs(e.props):e.props,$attrs:e=>({}).NODE_ENV!=="production"?zs(e.attrs):e.attrs,$slots:e=>({}).NODE_ENV!=="production"?zs(e.slots):e.slots,$refs:e=>({}).NODE_ENV!=="production"?zs(e.refs):e.refs,$parent:e=>Cl(e.parent),$root:e=>Cl(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Ol(e),$forceUpdate:e=>e.f||(e.f=()=>{yi(e.update)}),$nextTick:e=>e.n||(e.n=pl.bind(e.proxy)),$watch:e=>G_.bind(e)}),Dl=e=>e==="_"||e==="$",xl=(e,t)=>e!==st&&!e.__isScriptSetup&&Ye(e,t),Gd={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:i,data:n,props:a,accessCache:u,type:c,appContext:h}=e;if({}.NODE_ENV!=="production"&&t==="__isVue")return!0;let m;if(t[0]!=="$"){const D=u[t];if(D!==void 0)switch(D){case 1:return i[t];case 2:return n[t];case 4:return s[t];case 3:return a[t]}else{if(xl(i,t))return u[t]=1,i[t];if(n!==st&&Ye(n,t))return u[t]=2,n[t];if((m=e.propsOptions[0])&&Ye(m,t))return u[t]=3,a[t];if(s!==st&&Ye(s,t))return u[t]=4,s[t];Sl&&(u[t]=0)}}const p=nn[t];let v,w;if(p)return t==="$attrs"?(Tt(e.attrs,"get",""),{}.NODE_ENV!=="production"&&Pi()):{}.NODE_ENV!=="production"&&t==="$slots"&&Tt(e,"get",t),p(e);if((v=c.__cssModules)&&(v=v[t]))return v;if(s!==st&&Ye(s,t))return u[t]=4,s[t];if(w=h.config.globalProperties,Ye(w,t))return w[t];({}).NODE_ENV!=="production"&&xt&&(!dt(t)||t.indexOf("__v")!==0)&&(n!==st&&Dl(t[0])&&Ye(n,t)?Y(`Property ${JSON.stringify(t)} must be accessed via $data because it starts with a reserved character ("$" or "_") and is not proxied on the render context.`):e===xt&&Y(`Property ${JSON.stringify(t)} was accessed during render but is not defined on instance.`))},set({_:e},t,s){const{data:i,setupState:n,ctx:a}=e;return xl(n,t)?(n[t]=s,!0):{}.NODE_ENV!=="production"&&n.__isScriptSetup&&Ye(n,t)?(Y(`Cannot mutate <script setup> binding "${t}" from Options API.`),!1):i!==st&&Ye(i,t)?(i[t]=s,!0):Ye(e.props,t)?({}.NODE_ENV!=="production"&&Y(`Attempting to mutate prop "${t}". Props are readonly.`),!1):t[0]==="$"&&t.slice(1)in e?({}.NODE_ENV!=="production"&&Y(`Attempting to mutate public property "${t}". Properties starting with $ are reserved and readonly.`),!1):({}.NODE_ENV!=="production"&&t in e.appContext.config.globalProperties?Object.defineProperty(a,t,{enumerable:!0,configurable:!0,value:s}):a[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:i,appContext:n,propsOptions:a}},u){let c;return!!s[u]||e!==st&&Ye(e,u)||xl(t,u)||(c=a[0])&&Ye(c,u)||Ye(i,u)||Ye(nn,u)||Ye(n.config.globalProperties,u)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:Ye(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};({}).NODE_ENV!=="production"&&(Gd.ownKeys=e=>(Y("Avoid app logic that relies on enumerating keys on a component instance. The keys will be empty in production mode to avoid performance overhead."),Reflect.ownKeys(e)));function m_(e){const t={};return Object.defineProperty(t,"_",{configurable:!0,enumerable:!1,get:()=>e}),Object.keys(nn).forEach(s=>{Object.defineProperty(t,s,{configurable:!0,enumerable:!1,get:()=>nn[s](e),set:Ot})}),t}function g_(e){const{ctx:t,propsOptions:[s]}=e;s&&Object.keys(s).forEach(i=>{Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>e.props[i],set:Ot})})}function v_(e){const{ctx:t,setupState:s}=e;Object.keys(Me(s)).forEach(i=>{if(!s.__isScriptSetup){if(Dl(i[0])){Y(`setup() return property ${JSON.stringify(i)} should not start with "$" or "_" which are reserved prefixes for Vue internals.`);return}Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>s[i],set:Ot})}})}function Kd(e){return ge(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}function __(){const e=Object.create(null);return(t,s)=>{e[s]?Y(`${t} property "${s}" is already defined in ${e[s]}.`):e[s]=t}}let Sl=!0;function b_(e){const t=Ol(e),s=e.proxy,i=e.ctx;Sl=!1,t.beforeCreate&&Qd(t.beforeCreate,e,"bc");const{data:n,computed:a,methods:u,watch:c,provide:h,inject:m,created:p,beforeMount:v,mounted:w,beforeUpdate:D,updated:k,activated:L,deactivated:re,beforeDestroy:I,beforeUnmount:ne,destroyed:Q,unmounted:ye,render:Z,renderTracked:he,renderTriggered:be,errorCaptured:Ae,serverPrefetch:ue,expose:ae,inheritAttrs:V,components:te,directives:Ke,filters:vt}=t,mt={}.NODE_ENV!=="production"?__():null;if({}.NODE_ENV!=="production"){const[Se]=e.propsOptions;if(Se)for(const we in Se)mt("Props",we)}if(m&&y_(m,i,mt),u)for(const Se in u){const we=u[Se];xe(we)?({}.NODE_ENV!=="production"?Object.defineProperty(i,Se,{value:we.bind(s),configurable:!0,enumerable:!0,writable:!0}):i[Se]=we.bind(s),{}.NODE_ENV!=="production"&&mt("Methods",Se)):{}.NODE_ENV!=="production"&&Y(`Method "${Se}" has type "${typeof we}" in the component definition. Did you reference the function correctly?`)}if(n){({}).NODE_ENV!=="production"&&!xe(n)&&Y("The data option must be a function. Plain object usage is no longer supported.");const Se=n.call(s,s);if({}.NODE_ENV!=="production"&&Ka(Se)&&Y("data() returned a Promise - note data() cannot be async; If you intend to perform data fetching before component renders, use async setup() + <Suspense>."),!Je(Se))({}).NODE_ENV!=="production"&&Y("data() should return an object.");else if(e.data=fi(Se),{}.NODE_ENV!=="production")for(const we in Se)mt("Data",we),Dl(we[0])||Object.defineProperty(i,we,{configurable:!0,enumerable:!0,get:()=>Se[we],set:Ot})}if(Sl=!0,a)for(const Se in a){const we=a[Se],Ft=xe(we)?we.bind(s,s):xe(we.get)?we.get.bind(s,s):Ot;({}).NODE_ENV!=="production"&&Ft===Ot&&Y(`Computed property "${Se}" has no getter.`);const es=!xe(we)&&xe(we.set)?we.set.bind(s):{}.NODE_ENV!=="production"?()=>{Y(`Write operation failed: computed property "${Se}" is readonly.`)}:Ot,bt=Us({get:Ft,set:es});Object.defineProperty(i,Se,{enumerable:!0,configurable:!0,get:()=>bt.value,set:de=>bt.value=de}),{}.NODE_ENV!=="production"&&mt("Computed",Se)}if(c)for(const Se in c)Yd(c[Se],i,s,Se);if(h){const Se=xe(h)?h.call(s):h;Reflect.ownKeys(Se).forEach(we=>{Ii(we,Se[we])})}p&&Qd(p,e,"c");function ft(Se,we){ge(we)?we.forEach(Ft=>Se(Ft.bind(s))):we&&Se(we.bind(s))}if(ft(o_,v),ft($d,w),ft(i_,D),ft(a_,k),ft(s_,L),ft(r_,re),ft(d_,Ae),ft(c_,he),ft(u_,be),ft(jd,ne),ft(Hd,ye),ft(l_,ue),ge(ae))if(ae.length){const Se=e.exposed||(e.exposed={});ae.forEach(we=>{Object.defineProperty(Se,we,{get:()=>s[we],set:Ft=>s[we]=Ft})})}else e.exposed||(e.exposed={});Z&&e.render===Ot&&(e.render=Z),V!=null&&(e.inheritAttrs=V),te&&(e.components=te),Ke&&(e.directives=Ke),ue&&Ld(e)}function y_(e,t,s=Ot){ge(e)&&(e=Tl(e));for(const i in e){const n=e[i];let a;Je(n)?"default"in n?a=Vs(n.from||i,n.default,!0):a=Vs(n.from||i):a=Vs(n),Dt(a)?Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>a.value,set:u=>a.value=u}):t[i]=a,{}.NODE_ENV!=="production"&&s("Inject",i)}}function Qd(e,t,s){Ps(ge(e)?e.map(i=>i.bind(t.proxy)):e.bind(t.proxy),t,s)}function Yd(e,t,s,i){let n=i.includes(".")?mf(s,i):()=>s[i];if(dt(e)){const a=t[e];xe(a)?Pn(n,a):{}.NODE_ENV!=="production"&&Y(`Invalid watch handler specified by key "${e}"`,a)}else if(xe(e))Pn(n,e.bind(s));else if(Je(e))if(ge(e))e.forEach(a=>Yd(a,t,s,i));else{const a=xe(e.handler)?e.handler.bind(s):t[e.handler];xe(a)?Pn(n,a,e):{}.NODE_ENV!=="production"&&Y(`Invalid watch handler specified by key "${e.handler}"`,a)}else({}).NODE_ENV!=="production"&&Y(`Invalid watch option: "${i}"`,e)}function Ol(e){const t=e.type,{mixins:s,extends:i}=t,{mixins:n,optionsCache:a,config:{optionMergeStrategies:u}}=e.appContext,c=a.get(t);let h;return c?h=c:!n.length&&!s&&!i?h=t:(h={},n.length&&n.forEach(m=>Ni(h,m,u,!0)),Ni(h,t,u)),Je(t)&&a.set(t,h),h}function Ni(e,t,s,i=!1){const{mixins:n,extends:a}=t;a&&Ni(e,a,s,!0),n&&n.forEach(u=>Ni(e,u,s,!0));for(const u in t)if(i&&u==="expose")({}).NODE_ENV!=="production"&&Y('"expose" option is ignored when declared in mixins or extends. It should only be declared in the base component itself.');else{const c=w_[u]||s&&s[u];e[u]=c?c(e[u],t[u]):t[u]}return e}const w_={data:Zd,props:Jd,emits:Jd,methods:_o,computed:_o,beforeCreate:Zt,created:Zt,beforeMount:Zt,mounted:Zt,beforeUpdate:Zt,updated:Zt,beforeDestroy:Zt,beforeUnmount:Zt,destroyed:Zt,unmounted:Zt,activated:Zt,deactivated:Zt,errorCaptured:Zt,serverPrefetch:Zt,components:_o,directives:_o,watch:C_,provide:Zd,inject:E_};function Zd(e,t){return t?e?function(){return pt(xe(e)?e.call(this,this):e,xe(t)?t.call(this,this):t)}:t:e}function E_(e,t){return _o(Tl(e),Tl(t))}function Tl(e){if(ge(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function Zt(e,t){return e?[...new Set([].concat(e,t))]:t}function _o(e,t){return e?pt(Object.create(null),e,t):t}function Jd(e,t){return e?ge(e)&&ge(t)?[...new Set([...e,...t])]:pt(Object.create(null),Kd(e),Kd(t??{})):t}function C_(e,t){if(!e)return t;if(!t)return e;const s=pt(Object.create(null),e);for(const i in t)s[i]=Zt(e[i],t[i]);return s}function Xd(){return{app:null,config:{isNativeTag:Lg,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let D_=0;function x_(e,t){return function(i,n=null){xe(i)||(i=pt({},i)),n!=null&&!Je(n)&&({}.NODE_ENV!=="production"&&Y("root props passed to app.mount() must be an object."),n=null);const a=Xd(),u=new WeakSet,c=[];let h=!1;const m=a.app={_uid:D_++,_component:i,_props:n,_container:null,_context:a,_instance:null,version:Mf,get config(){return a.config},set config(p){({}).NODE_ENV!=="production"&&Y("app.config cannot be replaced. Modify individual options instead.")},use(p,...v){return u.has(p)?{}.NODE_ENV!=="production"&&Y("Plugin has already been applied to target app."):p&&xe(p.install)?(u.add(p),p.install(m,...v)):xe(p)?(u.add(p),p(m,...v)):{}.NODE_ENV!=="production"&&Y('A plugin must either be a function or an object with an "install" function.'),m},mixin(p){return a.mixins.includes(p)?{}.NODE_ENV!=="production"&&Y("Mixin has already been applied to target app"+(p.name?`: ${p.name}`:"")):a.mixins.push(p),m},component(p,v){return{}.NODE_ENV!=="production"&&Ll(p,a.config),v?({}.NODE_ENV!=="production"&&a.components[p]&&Y(`Component "${p}" has already been registered in target app.`),a.components[p]=v,m):a.components[p]},directive(p,v){return{}.NODE_ENV!=="production"&&Dd(p),v?({}.NODE_ENV!=="production"&&a.directives[p]&&Y(`Directive "${p}" has already been registered in target app.`),a.directives[p]=v,m):a.directives[p]},mount(p,v,w){if(h)({}).NODE_ENV!=="production"&&Y("App has already been mounted.\nIf you want to remount the same app, move your app creation logic into a factory function and create fresh app instances for each mount - e.g. `const createMyApp = () => createApp(App)`");else{({}).NODE_ENV!=="production"&&p.__vue_app__&&Y("There is already an app instance mounted on the host container.\n If you want to mount another app on the same host container, you need to unmount the previous app by calling `app.unmount()` first.");const D=m._ceVNode||M(i,n);return D.appContext=a,w===!0?w="svg":w===!1&&(w=void 0),{}.NODE_ENV!=="production"&&(a.reload=()=>{e(Ks(D),p,w)}),v&&t?t(D,p):e(D,p,w),h=!0,m._container=p,p.__vue_app__=m,{}.NODE_ENV!=="production"&&(m._instance=D.component,jv(m,Mf)),Ui(D.component)}},onUnmount(p){({}).NODE_ENV!=="production"&&typeof p!="function"&&Y(`Expected function as first argument to app.onUnmount(), but got ${typeof p}`),c.push(p)},unmount(){h?(Ps(c,m._instance,16),e(null,m._container),{}.NODE_ENV!=="production"&&(m._instance=null,Hv(m)),delete m._container.__vue_app__):{}.NODE_ENV!=="production"&&Y("Cannot unmount an app that is not mounted.")},provide(p,v){return{}.NODE_ENV!=="production"&&p in a.provides&&Y(`App already provides property with key "${String(p)}". It will be overwritten with the new value.`),a.provides[p]=v,m},runWithContext(p){const v=Mn;Mn=m;try{return p()}finally{Mn=v}}};return m}}let Mn=null;function Ii(e,t){if(!Nt)({}).NODE_ENV!=="production"&&Y("provide() can only be used inside setup().");else{let s=Nt.provides;const i=Nt.parent&&Nt.parent.provides;i===s&&(s=Nt.provides=Object.create(i)),s[e]=t}}function Vs(e,t,s=!1){const i=Nt||xt;if(i||Mn){const n=Mn?Mn._context.provides:i?i.parent==null?i.vnode.appContext&&i.vnode.appContext.provides:i.parent.provides:void 0;if(n&&e in n)return n[e];if(arguments.length>1)return s&&xe(t)?t.call(i&&i.proxy):t;({}).NODE_ENV!=="production"&&Y(`injection "${String(e)}" not found.`)}else({}).NODE_ENV!=="production"&&Y("inject() can only be used inside setup() or functional components.")}const ef={},tf=()=>Object.create(ef),sf=e=>Object.getPrototypeOf(e)===ef;function S_(e,t,s,i=!1){const n={},a=tf();e.propsDefaults=Object.create(null),rf(e,t,n,a);for(const u in e.propsOptions[0])u in n||(n[u]=void 0);({}).NODE_ENV!=="production"&&af(t||{},n,e),s?e.props=i?n:id(n):e.type.props?e.props=n:e.props=a,e.attrs=a}function O_(e){for(;e;){if(e.type.__hmrId)return!0;e=e.parent}}function T_(e,t,s,i){const{props:n,attrs:a,vnode:{patchFlag:u}}=e,c=Me(n),[h]=e.propsOptions;let m=!1;if(!({}.NODE_ENV!=="production"&&O_(e))&&(i||u>0)&&!(u&16)){if(u&8){const p=e.vnode.dynamicProps;for(let v=0;v<p.length;v++){let w=p[v];if(Mi(e.emitsOptions,w))continue;const D=t[w];if(h)if(Ye(a,w))D!==a[w]&&(a[w]=D,m=!0);else{const k=Kt(w);n[k]=Nl(h,c,k,D,e,!1)}else D!==a[w]&&(a[w]=D,m=!0)}}}else{rf(e,t,n,a)&&(m=!0);let p;for(const v in c)(!t||!Ye(t,v)&&((p=Sr(v))===v||!Ye(t,p)))&&(h?s&&(s[v]!==void 0||s[p]!==void 0)&&(n[v]=Nl(h,c,v,void 0,e,!0)):delete n[v]);if(a!==c)for(const v in a)(!t||!Ye(t,v))&&(delete a[v],m=!0)}m&&qs(e.attrs,"set",""),{}.NODE_ENV!=="production"&&af(t||{},n,e)}function rf(e,t,s,i){const[n,a]=e.propsOptions;let u=!1,c;if(t)for(let h in t){if(ro(h))continue;const m=t[h];let p;n&&Ye(n,p=Kt(h))?!a||!a.includes(p)?s[p]=m:(c||(c={}))[p]=m:Mi(e.emitsOptions,h)||(!(h in i)||m!==i[h])&&(i[h]=m,u=!0)}if(a){const h=Me(s),m=c||st;for(let p=0;p<a.length;p++){const v=a[p];s[v]=Nl(n,h,v,m[v],e,!Ye(m,v))}}return u}function Nl(e,t,s,i,n,a){const u=e[s];if(u!=null){const c=Ye(u,"default");if(c&&i===void 0){const h=u.default;if(u.type!==Function&&!u.skipFactory&&xe(h)){const{propsDefaults:m}=n;if(s in m)i=m[s];else{const p=xo(n);i=m[s]=h.call(null,t),p()}}else i=h;n.ce&&n.ce._setProp(s,i)}u[0]&&(a&&!c?i=!1:u[1]&&(i===""||i===Sr(s))&&(i=!0))}return i}const N_=new WeakMap;function nf(e,t,s=!1){const i=s?N_:t.propsCache,n=i.get(e);if(n)return n;const a=e.props,u={},c=[];let h=!1;if(!xe(e)){const p=v=>{h=!0;const[w,D]=nf(v,t,!0);pt(u,w),D&&c.push(...D)};!s&&t.mixins.length&&t.mixins.forEach(p),e.extends&&p(e.extends),e.mixins&&e.mixins.forEach(p)}if(!a&&!h)return Je(e)&&i.set(e,Dn),Dn;if(ge(a))for(let p=0;p<a.length;p++){({}).NODE_ENV!=="production"&&!dt(a[p])&&Y("props must be strings when using array syntax.",a[p]);const v=Kt(a[p]);of(v)&&(u[v]=st)}else if(a){({}).NODE_ENV!=="production"&&!Je(a)&&Y("invalid props options",a);for(const p in a){const v=Kt(p);if(of(v)){const w=a[p],D=u[v]=ge(w)||xe(w)?{type:w}:pt({},w),k=D.type;let L=!1,re=!0;if(ge(k))for(let I=0;I<k.length;++I){const ne=k[I],Q=xe(ne)&&ne.name;if(Q==="Boolean"){L=!0;break}else Q==="String"&&(re=!1)}else L=xe(k)&&k.name==="Boolean";D[0]=L,D[1]=re,(L||Ye(D,"default"))&&c.push(v)}}}const m=[u,c];return Je(e)&&i.set(e,m),m}function of(e){return e[0]!=="$"&&!ro(e)?!0:({}.NODE_ENV!=="production"&&Y(`Invalid prop name: "${e}" is a reserved property.`),!1)}function I_(e){return e===null?"null":typeof e=="function"?e.name||"":typeof e=="object"&&e.constructor&&e.constructor.name||""}function af(e,t,s){const i=Me(t),n=s.propsOptions[0],a=Object.keys(e).map(u=>Kt(u));for(const u in n){let c=n[u];c!=null&&A_(u,i[u],c,{}.NODE_ENV!=="production"?zs(i):i,!a.includes(u))}}function A_(e,t,s,i,n){const{type:a,required:u,validator:c,skipCheck:h}=s;if(u&&n){Y('Missing required prop: "'+e+'"');return}if(!(t==null&&!u)){if(a!=null&&a!==!0&&!h){let m=!1;const p=ge(a)?a:[a],v=[];for(let w=0;w<p.length&&!m;w++){const{valid:D,expectedType:k}=P_(t,p[w]);v.push(k||""),m=D}if(!m){Y(k_(e,t,v));return}}c&&!c(t,i)&&Y('Invalid prop: custom validator check failed for prop "'+e+'".')}}const M_=er("String,Number,Boolean,Function,Symbol,BigInt");function P_(e,t){let s;const i=I_(t);if(i==="null")s=e===null;else if(M_(i)){const n=typeof e;s=n===i.toLowerCase(),!s&&n==="object"&&(s=e instanceof t)}else i==="Object"?s=Je(e):i==="Array"?s=ge(e):s=e instanceof t;return{valid:s,expectedType:i}}function k_(e,t,s){if(s.length===0)return`Prop type [] for prop "${e}" won't match anything. Did you mean to use type Array instead?`;let i=`Invalid prop: type check failed for prop "${e}". Expected ${s.map(Qr).join(" | ")}`;const n=s[0],a=Qa(t),u=lf(t,n),c=lf(t,a);return s.length===1&&uf(n)&&!V_(n,a)&&(i+=` with value ${u}`),i+=`, got ${a} `,uf(a)&&(i+=`with value ${c}.`),i}function lf(e,t){return t==="String"?`"${e}"`:t==="Number"?`${Number(e)}`:`${e}`}function uf(e){return["string","number","boolean"].some(s=>e.toLowerCase()===s)}function V_(...e){return e.some(t=>t.toLowerCase()==="boolean")}const cf=e=>e[0]==="_"||e==="$stable",Il=e=>ge(e)?e.map(Rs):[Rs(e)],R_=(e,t,s)=>{if(t._n)return t;const i=Ne((...n)=>({}.NODE_ENV!=="production"&&Nt&&(!s||s.root===Nt.root)&&Y(`Slot "${e}" invoked outside of the render function: this will not track dependencies used in the slot. Invoke the slot function inside the render function instead.`),Il(t(...n))),s);return i._c=!1,i},df=(e,t,s)=>{const i=e._ctx;for(const n in e){if(cf(n))continue;const a=e[n];if(xe(a))t[n]=R_(n,a,i);else if(a!=null){({}).NODE_ENV!=="production"&&Y(`Non-function value encountered for slot "${n}". Prefer function slots for better performance.`);const u=Il(a);t[n]=()=>u}}},ff=(e,t)=>{({}).NODE_ENV!=="production"&&!vo(e.vnode)&&Y("Non-function value encountered for default slot. Prefer function slots for better performance.");const s=Il(t);e.slots.default=()=>s},Al=(e,t,s)=>{for(const i in t)(s||i!=="_")&&(e[i]=t[i])},U_=(e,t,s)=>{const i=e.slots=tf();if(e.vnode.shapeFlag&32){const n=t._;n?(Al(i,t,s),s&&ii(i,"_",n,!0)):df(t,i)}else t&&ff(e,t)},F_=(e,t,s)=>{const{vnode:i,slots:n}=e;let a=!0,u=st;if(i.shapeFlag&32){const c=t._;c?{}.NODE_ENV!=="production"&&ks?(Al(n,t,s),qs(e,"set","$slots")):s&&c===1?a=!1:Al(n,t,s):(a=!t.$stable,df(t,n)),u=t}else t&&(ff(e,t),u={default:1});if(a)for(const c in n)!cf(c)&&u[c]==null&&delete n[c]};let bo,Ar;function ar(e,t){e.appContext.config.performance&&Ai()&&Ar.mark(`vue-${t}-${e.uid}`),{}.NODE_ENV!=="production"&&Gv(e,t,Ai()?Ar.now():Date.now())}function lr(e,t){if(e.appContext.config.performance&&Ai()){const s=`vue-${t}-${e.uid}`,i=s+":end";Ar.mark(i),Ar.measure(`<${Fi(e,e.type)}> ${t}`,s,i),Ar.clearMarks(s),Ar.clearMarks(i)}({}).NODE_ENV!=="production"&&Kv(e,t,Ai()?Ar.now():Date.now())}function Ai(){return bo!==void 0||(typeof window<"u"&&window.performance?(bo=!0,Ar=window.performance):bo=!1),bo}function L_(){const e=[];if({}.NODE_ENV!=="production"&&e.length){const t=e.length>1;console.warn(`Feature flag${t?"s":""} ${e.join(", ")} ${t?"are":"is"} not explicitly defined. You are running the esm-bundler build of Vue, which expects these compile-time feature flags to be globally injected via the bundler config in order to get better tree-shaking in the production bundle.

For more details, see https://link.vuejs.org/feature-flags.`)}}const Jt=eb;function B_(e){return $_(e)}function $_(e,t){L_();const s=no();s.__VUE__=!0,{}.NODE_ENV!=="production"&&yd(s.__VUE_DEVTOOLS_GLOBAL_HOOK__,s);const{insert:i,remove:n,patchProp:a,createElement:u,createText:c,createComment:h,setText:m,setElementText:p,parentNode:v,nextSibling:w,setScopeId:D=Ot,insertStaticContent:k}=e,L=(y,C,P,F=null,H=null,W=null,se=void 0,K=null,J={}.NODE_ENV!=="production"&&ks?!1:!!C.dynamicChildren)=>{if(y===C)return;y&&!ln(y,C)&&(F=le(y),We(y,H,W,!0),y=null),C.patchFlag===-2&&(J=!1,C.dynamicChildren=null);const{type:G,ref:Ee,shapeFlag:oe}=C;switch(G){case wo:re(y,C,P,F);break;case wt:I(y,C,P,F);break;case Eo:y==null?ne(C,P,F,se):{}.NODE_ENV!=="production"&&Q(y,C,P,se);break;case Ie:Ke(y,C,P,F,H,W,se,K,J);break;default:oe&1?he(y,C,P,F,H,W,se,K,J):oe&6?vt(y,C,P,F,H,W,se,K,J):oe&64||oe&128?G.process(y,C,P,F,H,W,se,K,J,ke):{}.NODE_ENV!=="production"&&Y("Invalid VNode type:",G,`(${typeof G})`)}Ee!=null&&H&&Oi(Ee,y&&y.ref,W,C||y,!C)},re=(y,C,P,F)=>{if(y==null)i(C.el=c(C.children),P,F);else{const H=C.el=y.el;C.children!==y.children&&m(H,C.children)}},I=(y,C,P,F)=>{y==null?i(C.el=h(C.children||""),P,F):C.el=y.el},ne=(y,C,P,F)=>{[y.el,y.anchor]=k(y.children,C,P,F,y.el,y.anchor)},Q=(y,C,P,F)=>{if(C.children!==y.children){const H=w(y.anchor);Z(y),[C.el,C.anchor]=k(C.children,P,H,F)}else C.el=y.el,C.anchor=y.anchor},ye=({el:y,anchor:C},P,F)=>{let H;for(;y&&y!==C;)H=w(y),i(y,P,F),y=H;i(C,P,F)},Z=({el:y,anchor:C})=>{let P;for(;y&&y!==C;)P=w(y),n(y),y=P;n(C)},he=(y,C,P,F,H,W,se,K,J)=>{C.type==="svg"?se="svg":C.type==="math"&&(se="mathml"),y==null?be(C,P,F,H,W,se,K,J):ae(y,C,H,W,se,K,J)},be=(y,C,P,F,H,W,se,K)=>{let J,G;const{props:Ee,shapeFlag:oe,transition:_e,dirs:Ce}=y;if(J=y.el=u(y.type,W,Ee&&Ee.is,Ee),oe&8?p(J,y.children):oe&16&&ue(y.children,J,null,F,H,Ml(y,W),se,K),Ce&&sn(y,null,F,"created"),Ae(J,y,y.scopeId,se,F),Ee){for(const et in Ee)et!=="value"&&!ro(et)&&a(J,et,null,Ee[et],W,F);"value"in Ee&&a(J,"value",null,Ee.value,W),(G=Ee.onVnodeBeforeMount)&&Qs(G,F,y)}({}).NODE_ENV!=="production"&&(ii(J,"__vnode",y,!0),ii(J,"__vueParentComponent",F,!0)),Ce&&sn(y,null,F,"beforeMount");const Le=j_(H,_e);Le&&_e.beforeEnter(J),i(J,C,P),((G=Ee&&Ee.onVnodeMounted)||Le||Ce)&&Jt(()=>{G&&Qs(G,F,y),Le&&_e.enter(J),Ce&&sn(y,null,F,"mounted")},H)},Ae=(y,C,P,F,H)=>{if(P&&D(y,P),F)for(let W=0;W<F.length;W++)D(y,F[W]);if(H){let W=H.subTree;if({}.NODE_ENV!=="production"&&W.patchFlag>0&&W.patchFlag&2048&&(W=Rl(W.children)||W),C===W||yf(W.type)&&(W.ssContent===C||W.ssFallback===C)){const se=H.vnode;Ae(y,se,se.scopeId,se.slotScopeIds,H.parent)}}},ue=(y,C,P,F,H,W,se,K,J=0)=>{for(let G=J;G<y.length;G++){const Ee=y[G]=K?Mr(y[G]):Rs(y[G]);L(null,Ee,C,P,F,H,W,se,K)}},ae=(y,C,P,F,H,W,se)=>{const K=C.el=y.el;({}).NODE_ENV!=="production"&&(K.__vnode=C);let{patchFlag:J,dynamicChildren:G,dirs:Ee}=C;J|=y.patchFlag&16;const oe=y.props||st,_e=C.props||st;let Ce;if(P&&on(P,!1),(Ce=_e.onVnodeBeforeUpdate)&&Qs(Ce,P,C,y),Ee&&sn(C,y,P,"beforeUpdate"),P&&on(P,!0),{}.NODE_ENV!=="production"&&ks&&(J=0,se=!1,G=null),(oe.innerHTML&&_e.innerHTML==null||oe.textContent&&_e.textContent==null)&&p(K,""),G?(V(y.dynamicChildren,G,K,P,F,Ml(C,H),W),{}.NODE_ENV!=="production"&&yo(y,C)):se||Ft(y,C,K,null,P,F,Ml(C,H),W,!1),J>0){if(J&16)te(K,oe,_e,P,H);else if(J&2&&oe.class!==_e.class&&a(K,"class",null,_e.class,H),J&4&&a(K,"style",oe.style,_e.style,H),J&8){const Le=C.dynamicProps;for(let et=0;et<Le.length;et++){const Ze=Le[et],Lt=oe[Ze],St=_e[Ze];(St!==Lt||Ze==="value")&&a(K,Ze,Lt,St,H,P)}}J&1&&y.children!==C.children&&p(K,C.children)}else!se&&G==null&&te(K,oe,_e,P,H);((Ce=_e.onVnodeUpdated)||Ee)&&Jt(()=>{Ce&&Qs(Ce,P,C,y),Ee&&sn(C,y,P,"updated")},F)},V=(y,C,P,F,H,W,se)=>{for(let K=0;K<C.length;K++){const J=y[K],G=C[K],Ee=J.el&&(J.type===Ie||!ln(J,G)||J.shapeFlag&70)?v(J.el):P;L(J,G,Ee,null,F,H,W,se,!0)}},te=(y,C,P,F,H)=>{if(C!==P){if(C!==st)for(const W in C)!ro(W)&&!(W in P)&&a(y,W,C[W],null,H,F);for(const W in P){if(ro(W))continue;const se=P[W],K=C[W];se!==K&&W!=="value"&&a(y,W,K,se,H,F)}"value"in P&&a(y,"value",C.value,P.value,H)}},Ke=(y,C,P,F,H,W,se,K,J)=>{const G=C.el=y?y.el:c(""),Ee=C.anchor=y?y.anchor:c("");let{patchFlag:oe,dynamicChildren:_e,slotScopeIds:Ce}=C;({}).NODE_ENV!=="production"&&(ks||oe&2048)&&(oe=0,J=!1,_e=null),Ce&&(K=K?K.concat(Ce):Ce),y==null?(i(G,P,F),i(Ee,P,F),ue(C.children||[],P,Ee,H,W,se,K,J)):oe>0&&oe&64&&_e&&y.dynamicChildren?(V(y.dynamicChildren,_e,P,H,W,se,K),{}.NODE_ENV!=="production"?yo(y,C):(C.key!=null||H&&C===H.subTree)&&yo(y,C,!0)):Ft(y,C,P,Ee,H,W,se,K,J)},vt=(y,C,P,F,H,W,se,K,J)=>{C.slotScopeIds=K,y==null?C.shapeFlag&512?H.ctx.activate(C,P,F,se,J):mt(C,P,F,H,W,se,J):ft(y,C,J)},mt=(y,C,P,F,H,W,se)=>{const K=y.component=lb(y,F,H);if({}.NODE_ENV!=="production"&&K.type.__hmrId&&Fv(K),{}.NODE_ENV!=="production"&&(vi(y),ar(K,"mount")),vo(y)&&(K.ctx.renderer=ke),{}.NODE_ENV!=="production"&&ar(K,"init"),cb(K,!1,se),{}.NODE_ENV!=="production"&&lr(K,"init"),K.asyncDep){if({}.NODE_ENV!=="production"&&ks&&(y.el=null),H&&H.registerDep(K,Se,se),!y.el){const J=K.subTree=M(wt);I(null,J,C,P)}}else Se(K,y,C,P,H,W,se);({}).NODE_ENV!=="production"&&(_i(),lr(K,"mount"))},ft=(y,C,P)=>{const F=C.component=y.component;if(J_(y,C,P))if(F.asyncDep&&!F.asyncResolved){({}).NODE_ENV!=="production"&&vi(C),we(F,C,P),{}.NODE_ENV!=="production"&&_i();return}else F.next=C,F.update();else C.el=y.el,F.vnode=C},Se=(y,C,P,F,H,W,se)=>{const K=()=>{if(y.isMounted){let{next:oe,bu:_e,u:Ce,parent:Le,vnode:et}=y;{const Bt=hf(y);if(Bt){oe&&(oe.el=et.el,we(y,oe,se)),Bt.asyncDep.then(()=>{y.isUnmounted||K()});return}}let Ze=oe,Lt;({}).NODE_ENV!=="production"&&vi(oe||y.vnode),on(y,!1),oe?(oe.el=et.el,we(y,oe,se)):oe=et,_e&&Sn(_e),(Lt=oe.props&&oe.props.onVnodeBeforeUpdate)&&Qs(Lt,Le,oe,et),on(y,!0),{}.NODE_ENV!=="production"&&ar(y,"render");const St=Vl(y);({}).NODE_ENV!=="production"&&lr(y,"render");const ts=y.subTree;y.subTree=St,{}.NODE_ENV!=="production"&&ar(y,"patch"),L(ts,St,v(ts.el),le(ts),y,H,W),{}.NODE_ENV!=="production"&&lr(y,"patch"),oe.el=St.el,Ze===null&&X_(y,St.el),Ce&&Jt(Ce,H),(Lt=oe.props&&oe.props.onVnodeUpdated)&&Jt(()=>Qs(Lt,Le,oe,et),H),{}.NODE_ENV!=="production"&&wd(y),{}.NODE_ENV!=="production"&&_i()}else{let oe;const{el:_e,props:Ce}=C,{bm:Le,m:et,parent:Ze,root:Lt,type:St}=y,ts=An(C);if(on(y,!1),Le&&Sn(Le),!ts&&(oe=Ce&&Ce.onVnodeBeforeMount)&&Qs(oe,Ze,C),on(y,!0),_e&&Ve){const Bt=()=>{({}).NODE_ENV!=="production"&&ar(y,"render"),y.subTree=Vl(y),{}.NODE_ENV!=="production"&&lr(y,"render"),{}.NODE_ENV!=="production"&&ar(y,"hydrate"),Ve(_e,y.subTree,y,H,null),{}.NODE_ENV!=="production"&&lr(y,"hydrate")};ts&&St.__asyncHydrate?St.__asyncHydrate(_e,y,Bt):Bt()}else{Lt.ce&&Lt.ce._injectChildStyle(St),{}.NODE_ENV!=="production"&&ar(y,"render");const Bt=y.subTree=Vl(y);({}).NODE_ENV!=="production"&&lr(y,"render"),{}.NODE_ENV!=="production"&&ar(y,"patch"),L(null,Bt,P,F,y,H,W),{}.NODE_ENV!=="production"&&lr(y,"patch"),C.el=Bt.el}if(et&&Jt(et,H),!ts&&(oe=Ce&&Ce.onVnodeMounted)){const Bt=C;Jt(()=>Qs(oe,Ze,Bt),H)}(C.shapeFlag&256||Ze&&An(Ze.vnode)&&Ze.vnode.shapeFlag&256)&&y.a&&Jt(y.a,H),y.isMounted=!0,{}.NODE_ENV!=="production"&&qv(y),C=P=F=null}};y.scope.on();const J=y.effect=new $c(K);y.scope.off();const G=y.update=J.run.bind(J),Ee=y.job=J.runIfDirty.bind(J);Ee.i=y,Ee.id=y.uid,J.scheduler=()=>yi(Ee),on(y,!0),{}.NODE_ENV!=="production"&&(J.onTrack=y.rtc?oe=>Sn(y.rtc,oe):void 0,J.onTrigger=y.rtg?oe=>Sn(y.rtg,oe):void 0),G()},we=(y,C,P)=>{C.component=y;const F=y.vnode.props;y.vnode=C,y.next=null,T_(y,C.props,F,P),F_(y,C.children,P),tr(),md(y),sr()},Ft=(y,C,P,F,H,W,se,K,J=!1)=>{const G=y&&y.children,Ee=y?y.shapeFlag:0,oe=C.children,{patchFlag:_e,shapeFlag:Ce}=C;if(_e>0){if(_e&128){bt(G,oe,P,F,H,W,se,K,J);return}else if(_e&256){es(G,oe,P,F,H,W,se,K,J);return}}Ce&8?(Ee&16&&R(G,H,W),oe!==G&&p(P,oe)):Ee&16?Ce&16?bt(G,oe,P,F,H,W,se,K,J):R(G,H,W,!0):(Ee&8&&p(P,""),Ce&16&&ue(oe,P,F,H,W,se,K,J))},es=(y,C,P,F,H,W,se,K,J)=>{y=y||Dn,C=C||Dn;const G=y.length,Ee=C.length,oe=Math.min(G,Ee);let _e;for(_e=0;_e<oe;_e++){const Ce=C[_e]=J?Mr(C[_e]):Rs(C[_e]);L(y[_e],Ce,P,null,H,W,se,K,J)}G>Ee?R(y,H,W,!0,!1,oe):ue(C,P,F,H,W,se,K,J,oe)},bt=(y,C,P,F,H,W,se,K,J)=>{let G=0;const Ee=C.length;let oe=y.length-1,_e=Ee-1;for(;G<=oe&&G<=_e;){const Ce=y[G],Le=C[G]=J?Mr(C[G]):Rs(C[G]);if(ln(Ce,Le))L(Ce,Le,P,null,H,W,se,K,J);else break;G++}for(;G<=oe&&G<=_e;){const Ce=y[oe],Le=C[_e]=J?Mr(C[_e]):Rs(C[_e]);if(ln(Ce,Le))L(Ce,Le,P,null,H,W,se,K,J);else break;oe--,_e--}if(G>oe){if(G<=_e){const Ce=_e+1,Le=Ce<Ee?C[Ce].el:F;for(;G<=_e;)L(null,C[G]=J?Mr(C[G]):Rs(C[G]),P,Le,H,W,se,K,J),G++}}else if(G>_e)for(;G<=oe;)We(y[G],H,W,!0),G++;else{const Ce=G,Le=G,et=new Map;for(G=Le;G<=_e;G++){const It=C[G]=J?Mr(C[G]):Rs(C[G]);It.key!=null&&({}.NODE_ENV!=="production"&&et.has(It.key)&&Y("Duplicate keys found during update:",JSON.stringify(It.key),"Make sure keys are unique."),et.set(It.key,G))}let Ze,Lt=0;const St=_e-Le+1;let ts=!1,Bt=0;const gr=new Array(St);for(G=0;G<St;G++)gr[G]=0;for(G=Ce;G<=oe;G++){const It=y[G];if(Lt>=St){We(It,H,W,!0);continue}let Es;if(It.key!=null)Es=et.get(It.key);else for(Ze=Le;Ze<=_e;Ze++)if(gr[Ze-Le]===0&&ln(It,C[Ze])){Es=Ze;break}Es===void 0?We(It,H,W,!0):(gr[Es-Le]=G+1,Es>=Bt?Bt=Es:ts=!0,L(It,C[Es],P,null,H,W,se,K,J),Lt++)}const Bn=ts?H_(gr):Dn;for(Ze=Bn.length-1,G=St-1;G>=0;G--){const It=Le+G,Es=C[It],na=It+1<Ee?C[It+1].el:F;gr[G]===0?L(null,Es,P,na,H,W,se,K,J):ts&&(Ze<0||G!==Bn[Ze]?de(Es,P,na,2):Ze--)}}},de=(y,C,P,F,H=null)=>{const{el:W,type:se,transition:K,children:J,shapeFlag:G}=y;if(G&6){de(y.component.subTree,C,P,F);return}if(G&128){y.suspense.move(C,P,F);return}if(G&64){se.move(y,C,P,ke);return}if(se===Ie){i(W,C,P);for(let oe=0;oe<J.length;oe++)de(J[oe],C,P,F);i(y.anchor,C,P);return}if(se===Eo){ye(y,C,P);return}if(F!==2&&G&1&&K)if(F===0)K.beforeEnter(W),i(W,C,P),Jt(()=>K.enter(W),H);else{const{leave:oe,delayLeave:_e,afterLeave:Ce}=K,Le=()=>i(W,C,P),et=()=>{oe(W,()=>{Le(),Ce&&Ce()})};_e?_e(W,Le,et):et()}else i(W,C,P)},We=(y,C,P,F=!1,H=!1)=>{const{type:W,props:se,ref:K,children:J,dynamicChildren:G,shapeFlag:Ee,patchFlag:oe,dirs:_e,cacheIndex:Ce}=y;if(oe===-2&&(H=!1),K!=null&&Oi(K,null,P,y,!0),Ce!=null&&(C.renderCache[Ce]=void 0),Ee&256){C.ctx.deactivate(y);return}const Le=Ee&1&&_e,et=!An(y);let Ze;if(et&&(Ze=se&&se.onVnodeBeforeUnmount)&&Qs(Ze,C,y),Ee&6)ps(y.component,P,F);else{if(Ee&128){y.suspense.unmount(P,F);return}Le&&sn(y,null,C,"beforeUnmount"),Ee&64?y.type.remove(y,C,P,ke,F):G&&!G.hasOnce&&(W!==Ie||oe>0&&oe&64)?R(G,C,P,!1,!0):(W===Ie&&oe&384||!H&&Ee&16)&&R(J,C,P),F&&hs(y)}(et&&(Ze=se&&se.onVnodeUnmounted)||Le)&&Jt(()=>{Ze&&Qs(Ze,C,y),Le&&sn(y,null,C,"unmounted")},P)},hs=y=>{const{type:C,el:P,anchor:F,transition:H}=y;if(C===Ie){({}).NODE_ENV!=="production"&&y.patchFlag>0&&y.patchFlag&2048&&H&&!H.persisted?y.children.forEach(se=>{se.type===wt?n(se.el):hs(se)}):zt(P,F);return}if(C===Eo){Z(y);return}const W=()=>{n(P),H&&!H.persisted&&H.afterLeave&&H.afterLeave()};if(y.shapeFlag&1&&H&&!H.persisted){const{leave:se,delayLeave:K}=H,J=()=>se(P,W);K?K(y.el,W,J):J()}else W()},zt=(y,C)=>{let P;for(;y!==C;)P=w(y),n(y),y=P;n(C)},ps=(y,C,P)=>{({}).NODE_ENV!=="production"&&y.type.__hmrId&&Lv(y);const{bum:F,scope:H,job:W,subTree:se,um:K,m:J,a:G}=y;pf(J),pf(G),F&&Sn(F),H.stop(),W&&(W.flags|=8,We(se,y,C,P)),K&&Jt(K,C),Jt(()=>{y.isUnmounted=!0},C),C&&C.pendingBranch&&!C.isUnmounted&&y.asyncDep&&!y.asyncResolved&&y.suspenseId===C.pendingId&&(C.deps--,C.deps===0&&C.resolve()),{}.NODE_ENV!=="production"&&Wv(y)},R=(y,C,P,F=!1,H=!1,W=0)=>{for(let se=W;se<y.length;se++)We(y[se],C,P,F,H)},le=y=>{if(y.shapeFlag&6)return le(y.component.subTree);if(y.shapeFlag&128)return y.suspense.next();const C=w(y.anchor||y.el),P=C&&C[xd];return P?w(P):C};let ie=!1;const me=(y,C,P)=>{y==null?C._vnode&&We(C._vnode,null,null,!0):L(C._vnode||null,y,C,null,null,null,P),C._vnode=y,ie||(ie=!0,md(),gd(),ie=!1)},ke={p:L,um:We,m:de,r:hs,mt,mc:ue,pc:Ft,pbc:V,n:le,o:e};let ot,Ve;return t&&([ot,Ve]=t(ke)),{render:me,hydrate:ot,createApp:x_(me,ot)}}function Ml({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function on({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function j_(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function yo(e,t,s=!1){const i=e.children,n=t.children;if(ge(i)&&ge(n))for(let a=0;a<i.length;a++){const u=i[a];let c=n[a];c.shapeFlag&1&&!c.dynamicChildren&&((c.patchFlag<=0||c.patchFlag===32)&&(c=n[a]=Mr(n[a]),c.el=u.el),!s&&c.patchFlag!==-2&&yo(u,c)),c.type===wo&&(c.el=u.el),{}.NODE_ENV!=="production"&&c.type===wt&&!c.el&&(c.el=u.el)}}function H_(e){const t=e.slice(),s=[0];let i,n,a,u,c;const h=e.length;for(i=0;i<h;i++){const m=e[i];if(m!==0){if(n=s[s.length-1],e[n]<m){t[i]=n,s.push(i);continue}for(a=0,u=s.length-1;a<u;)c=a+u>>1,e[s[c]]<m?a=c+1:u=c;m<e[s[a]]&&(a>0&&(t[i]=s[a-1]),s[a]=i)}}for(a=s.length,u=s[a-1];a-- >0;)s[a]=u,u=t[u];return s}function hf(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:hf(t)}function pf(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const q_=Symbol.for("v-scx"),z_=()=>{{const e=Vs(q_);return e||{}.NODE_ENV!=="production"&&Y("Server rendering context not provided. Make sure to only call useSSRContext() conditionally in the server build."),e}};function W_(e,t){return Pl(e,null,t)}function Pn(e,t,s){return{}.NODE_ENV!=="production"&&!xe(t)&&Y("`watch(fn, options?)` signature has been moved to a separate API. Use `watchEffect(fn, options?)` instead. `watch` now only supports `watch(source, cb, options?) signature."),Pl(e,t,s)}function Pl(e,t,s=st){const{immediate:i,deep:n,flush:a,once:u}=s;({}).NODE_ENV!=="production"&&!t&&(i!==void 0&&Y('watch() "immediate" option is only respected when using the watch(source, callback, options?) signature.'),n!==void 0&&Y('watch() "deep" option is only respected when using the watch(source, callback, options?) signature.'),u!==void 0&&Y('watch() "once" option is only respected when using the watch(source, callback, options?) signature.'));const c=pt({},s);({}).NODE_ENV!=="production"&&(c.onWarn=Y);const h=t&&i||!t&&a!=="post";let m;if(So){if(a==="sync"){const D=z_();m=D.__watcherHandles||(D.__watcherHandles=[])}else if(!h){const D=()=>{};return D.stop=Ot,D.resume=Ot,D.pause=Ot,D}}const p=Nt;c.call=(D,k,L)=>Ps(D,p,k,L);let v=!1;a==="post"?c.scheduler=D=>{Jt(D,p&&p.suspense)}:a!=="sync"&&(v=!0,c.scheduler=(D,k)=>{k?D():yi(D)}),c.augmentJob=D=>{t&&(D.flags|=4),v&&(D.flags|=2,p&&(D.id=p.uid,D.i=p))};const w=Nv(e,t,c);return So&&(m?m.push(w):h&&w()),w}function G_(e,t,s){const i=this.proxy,n=dt(e)?e.includes(".")?mf(i,e):()=>i[e]:e.bind(i,i);let a;xe(t)?a=t:(a=t.handler,s=t);const u=xo(this),c=Pl(n,a.bind(i),s);return u(),c}function mf(e,t){const s=t.split(".");return()=>{let i=e;for(let n=0;n<s.length&&i;n++)i=i[s[n]];return i}}const K_=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Kt(t)}Modifiers`]||e[`${Sr(t)}Modifiers`];function Q_(e,t,...s){if(e.isUnmounted)return;const i=e.vnode.props||st;if({}.NODE_ENV!=="production"){const{emitsOptions:p,propsOptions:[v]}=e;if(p)if(!(t in p))(!v||!(Yr(Kt(t))in v))&&Y(`Component emitted event "${t}" but it is neither declared in the emits option nor as an "${Yr(Kt(t))}" prop.`);else{const w=p[t];xe(w)&&(w(...s)||Y(`Invalid event arguments: event validation failed for event "${t}".`))}}let n=s;const a=t.startsWith("update:"),u=a&&K_(i,t.slice(7));if(u&&(u.trim&&(n=s.map(p=>dt(p)?p.trim():p)),u.number&&(n=s.map(ai))),{}.NODE_ENV!=="production"&&Qv(e,t,n),{}.NODE_ENV!=="production"){const p=t.toLowerCase();p!==t&&i[Yr(p)]&&Y(`Event "${p}" is emitted in component ${Fi(e,e.type)} but the handler is registered for "${t}". Note that HTML attributes are case-insensitive and you cannot use v-on to listen to camelCase events when using in-DOM templates. You should probably use "${Sr(t)}" instead of "${t}".`)}let c,h=i[c=Yr(t)]||i[c=Yr(Kt(t))];!h&&a&&(h=i[c=Yr(Sr(t))]),h&&Ps(h,e,6,n);const m=i[c+"Once"];if(m){if(!e.emitted)e.emitted={};else if(e.emitted[c])return;e.emitted[c]=!0,Ps(m,e,6,n)}}function gf(e,t,s=!1){const i=t.emitsCache,n=i.get(e);if(n!==void 0)return n;const a=e.emits;let u={},c=!1;if(!xe(e)){const h=m=>{const p=gf(m,t,!0);p&&(c=!0,pt(u,p))};!s&&t.mixins.length&&t.mixins.forEach(h),e.extends&&h(e.extends),e.mixins&&e.mixins.forEach(h)}return!a&&!c?(Je(e)&&i.set(e,null),null):(ge(a)?a.forEach(h=>u[h]=null):pt(u,a),Je(e)&&i.set(e,u),u)}function Mi(e,t){return!e||!to(t)?!1:(t=t.slice(2).replace(/Once$/,""),Ye(e,t[0].toLowerCase()+t.slice(1))||Ye(e,Sr(t))||Ye(e,t))}let kl=!1;function Pi(){kl=!0}function Vl(e){const{type:t,vnode:s,proxy:i,withProxy:n,propsOptions:[a],slots:u,attrs:c,emit:h,render:m,renderCache:p,props:v,data:w,setupState:D,ctx:k,inheritAttrs:L}=e,re=Ci(e);let I,ne;({}).NODE_ENV!=="production"&&(kl=!1);try{if(s.shapeFlag&4){const Z=n||i,he={}.NODE_ENV!=="production"&&D.__isScriptSetup?new Proxy(Z,{get(be,Ae,ue){return Y(`Property '${String(Ae)}' was accessed via 'this'. Avoid using 'this' in templates.`),Reflect.get(be,Ae,ue)}}):Z;I=Rs(m.call(he,Z,p,{}.NODE_ENV!=="production"?zs(v):v,D,w,k)),ne=c}else{const Z=t;({}).NODE_ENV!=="production"&&c===v&&Pi(),I=Rs(Z.length>1?Z({}.NODE_ENV!=="production"?zs(v):v,{}.NODE_ENV!=="production"?{get attrs(){return Pi(),zs(c)},slots:u,emit:h}:{attrs:c,slots:u,emit:h}):Z({}.NODE_ENV!=="production"?zs(v):v,null)),ne=t.props?c:Y_(c)}}catch(Z){Co.length=0,fo(Z,e,1),I=M(wt)}let Q=I,ye;if({}.NODE_ENV!=="production"&&I.patchFlag>0&&I.patchFlag&2048&&([Q,ye]=vf(I)),ne&&L!==!1){const Z=Object.keys(ne),{shapeFlag:he}=Q;if(Z.length){if(he&7)a&&Z.some(ni)&&(ne=Z_(ne,a)),Q=Ks(Q,ne,!1,!0);else if({}.NODE_ENV!=="production"&&!kl&&Q.type!==wt){const be=Object.keys(c),Ae=[],ue=[];for(let ae=0,V=be.length;ae<V;ae++){const te=be[ae];to(te)?ni(te)||Ae.push(te[2].toLowerCase()+te.slice(3)):ue.push(te)}ue.length&&Y(`Extraneous non-props attributes (${ue.join(", ")}) were passed to component but could not be automatically inherited because component renders fragment or text or teleport root nodes.`),Ae.length&&Y(`Extraneous non-emits event listeners (${Ae.join(", ")}) were passed to component but could not be automatically inherited because component renders fragment or text root nodes. If the listener is intended to be a component custom event listener only, declare it using the "emits" option.`)}}}return s.dirs&&({}.NODE_ENV!=="production"&&!_f(Q)&&Y("Runtime directive used on component with non-element root node. The directives will not function as intended."),Q=Ks(Q,null,!1,!0),Q.dirs=Q.dirs?Q.dirs.concat(s.dirs):s.dirs),s.transition&&({}.NODE_ENV!=="production"&&!_f(Q)&&Y("Component inside <Transition> renders non-element root node that cannot be animated."),go(Q,s.transition)),{}.NODE_ENV!=="production"&&ye?ye(Q):I=Q,Ci(re),I}const vf=e=>{const t=e.children,s=e.dynamicChildren,i=Rl(t,!1);if(i){if({}.NODE_ENV!=="production"&&i.patchFlag>0&&i.patchFlag&2048)return vf(i)}else return[e,void 0];const n=t.indexOf(i),a=s?s.indexOf(i):-1,u=c=>{t[n]=c,s&&(a>-1?s[a]=c:c.patchFlag>0&&(e.dynamicChildren=[...s,c]))};return[Rs(i),u]};function Rl(e,t=!0){let s;for(let i=0;i<e.length;i++){const n=e[i];if(an(n)){if(n.type!==wt||n.children==="v-if"){if(s)return;if(s=n,{}.NODE_ENV!=="production"&&t&&s.patchFlag>0&&s.patchFlag&2048)return Rl(s.children)}}else return}return s}const Y_=e=>{let t;for(const s in e)(s==="class"||s==="style"||to(s))&&((t||(t={}))[s]=e[s]);return t},Z_=(e,t)=>{const s={};for(const i in e)(!ni(i)||!(i.slice(9)in t))&&(s[i]=e[i]);return s},_f=e=>e.shapeFlag&7||e.type===wt;function J_(e,t,s){const{props:i,children:n,component:a}=e,{props:u,children:c,patchFlag:h}=t,m=a.emitsOptions;if({}.NODE_ENV!=="production"&&(n||c)&&ks||t.dirs||t.transition)return!0;if(s&&h>=0){if(h&1024)return!0;if(h&16)return i?bf(i,u,m):!!u;if(h&8){const p=t.dynamicProps;for(let v=0;v<p.length;v++){const w=p[v];if(u[w]!==i[w]&&!Mi(m,w))return!0}}}else return(n||c)&&(!c||!c.$stable)?!0:i===u?!1:i?u?bf(i,u,m):!0:!!u;return!1}function bf(e,t,s){const i=Object.keys(t);if(i.length!==Object.keys(e).length)return!0;for(let n=0;n<i.length;n++){const a=i[n];if(t[a]!==e[a]&&!Mi(s,a))return!0}return!1}function X_({vnode:e,parent:t},s){for(;t;){const i=t.subTree;if(i.suspense&&i.suspense.activeBranch===e&&(i.el=e.el),i===e)(e=t.vnode).el=s,t=t.parent;else break}}const yf=e=>e.__isSuspense;function eb(e,t){t&&t.pendingBranch?ge(e)?t.effects.push(...e):t.effects.push(e):pd(e)}const Ie=Symbol.for("v-fgt"),wo=Symbol.for("v-txt"),wt=Symbol.for("v-cmt"),Eo=Symbol.for("v-stc"),Co=[];let ds=null;function S(e=!1){Co.push(ds=e?null:[])}function tb(){Co.pop(),ds=Co[Co.length-1]||null}let Do=1;function wf(e,t=!1){Do+=e,e<0&&ds&&t&&(ds.hasOnce=!0)}function Ef(e){return e.dynamicChildren=Do>0?ds||Dn:null,tb(),Do>0&&ds&&ds.push(e),e}function O(e,t,s,i,n,a){return Ef(d(e,t,s,i,n,a,!0))}function Ut(e,t,s,i,n){return Ef(M(e,t,s,i,n,!0))}function an(e){return e?e.__v_isVNode===!0:!1}function ln(e,t){if({}.NODE_ENV!=="production"&&t.shapeFlag&6&&e.component){const s=wi.get(t.type);if(s&&s.has(e.component))return e.shapeFlag&=-257,t.shapeFlag&=-513,!1}return e.type===t.type&&e.key===t.key}const sb=(...e)=>Df(...e),Cf=({key:e})=>e??null,ki=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?dt(e)||Dt(e)||xe(e)?{i:xt,r:e,k:t,f:!!s}:e:null);function d(e,t=null,s=null,i=0,n=null,a=e===Ie?0:1,u=!1,c=!1){const h={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Cf(t),ref:t&&ki(t),scopeId:Cd,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:a,patchFlag:i,dynamicProps:n,dynamicChildren:null,appContext:null,ctx:xt};return c?(Ul(h,s),a&128&&e.normalize(h)):s&&(h.shapeFlag|=dt(s)?8:16),{}.NODE_ENV!=="production"&&h.key!==h.key&&Y("VNode created with invalid key (NaN). VNode type:",h.type),Do>0&&!u&&ds&&(h.patchFlag>0||a&6)&&h.patchFlag!==32&&ds.push(h),h}const M={}.NODE_ENV!=="production"?sb:Df;function Df(e,t=null,s=null,i=0,n=null,a=!1){if((!e||e===h_)&&({}.NODE_ENV!=="production"&&!e&&Y(`Invalid vnode type when creating vnode: ${e}.`),e=wt),an(e)){const c=Ks(e,t,!0);return s&&Ul(c,s),Do>0&&!a&&ds&&(c.shapeFlag&6?ds[ds.indexOf(e)]=c:ds.push(c)),c.patchFlag=-2,c}if(Af(e)&&(e=e.__vccOpts),t){t=rb(t);let{class:c,style:h}=t;c&&!dt(c)&&(t.class=pe(c)),Je(h)&&(pi(h)&&!ge(h)&&(h=pt({},h)),t.style=us(h))}const u=dt(e)?1:yf(e)?128:Sd(e)?64:Je(e)?4:xe(e)?2:0;return{}.NODE_ENV!=="production"&&u&4&&pi(e)&&(e=Me(e),Y("Vue received a Component that was made a reactive object. This can lead to unnecessary performance overhead and should be avoided by marking the component with `markRaw` or using `shallowRef` instead of `ref`.",`
Component that was made reactive: `,e)),d(e,t,s,i,n,u,a,!0)}function rb(e){return e?pi(e)||sf(e)?pt({},e):e:null}function Ks(e,t,s=!1,i=!1){const{props:n,ref:a,patchFlag:u,children:c,transition:h}=e,m=t?ob(n||{},t):n,p={__v_isVNode:!0,__v_skip:!0,type:e.type,props:m,key:m&&Cf(m),ref:t&&t.ref?s&&a?ge(a)?a.concat(ki(t)):[a,ki(t)]:ki(t):a,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:{}.NODE_ENV!=="production"&&u===-1&&ge(c)?c.map(xf):c,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ie?u===-1?16:u|16:u,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:h,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Ks(e.ssContent),ssFallback:e.ssFallback&&Ks(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return h&&i&&go(p,h.clone(p)),p}function xf(e){const t=Ks(e);return ge(e.children)&&(t.children=e.children.map(xf)),t}function Ue(e=" ",t=0){return M(wo,null,e,t)}function nb(e,t){const s=M(Eo,null,e);return s.staticCount=t,s}function ee(e="",t=!1){return t?(S(),Ut(wt,null,e)):M(wt,null,e)}function Rs(e){return e==null||typeof e=="boolean"?M(wt):ge(e)?M(Ie,null,e.slice()):an(e)?Mr(e):M(wo,null,String(e))}function Mr(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Ks(e)}function Ul(e,t){let s=0;const{shapeFlag:i}=e;if(t==null)t=null;else if(ge(t))s=16;else if(typeof t=="object")if(i&65){const n=t.default;n&&(n._c&&(n._d=!1),Ul(e,n()),n._c&&(n._d=!0));return}else{s=32;const n=t._;!n&&!sf(t)?t._ctx=xt:n===3&&xt&&(xt.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else xe(t)?(t={default:t,_ctx:xt},s=32):(t=String(t),i&64?(s=16,t=[Ue(t)]):s=8);e.children=t,e.shapeFlag|=s}function ob(...e){const t={};for(let s=0;s<e.length;s++){const i=e[s];for(const n in i)if(n==="class")t.class!==i.class&&(t.class=pe([t.class,i.class]));else if(n==="style")t.style=us([t.style,i.style]);else if(to(n)){const a=t[n],u=i[n];u&&a!==u&&!(ge(a)&&a.includes(u))&&(t[n]=a?[].concat(a,u):u)}else n!==""&&(t[n]=i[n])}return t}function Qs(e,t,s,i=null){Ps(e,t,7,[s,i])}const ib=Xd();let ab=0;function lb(e,t,s){const i=e.type,n=(t?t.appContext:e.appContext)||ib,a={uid:ab++,vnode:e,type:i,parent:t,appContext:n,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Bc(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(n.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:nf(i,n),emitsOptions:gf(i,n),emit:null,emitted:null,propsDefaults:st,inheritAttrs:i.inheritAttrs,ctx:st,data:st,props:st,attrs:st,slots:st,refs:st,setupState:st,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return{}.NODE_ENV!=="production"?a.ctx=m_(a):a.ctx={_:a},a.root=t?t.root:a,a.emit=Q_.bind(null,a),e.ce&&e.ce(a),a}let Nt=null;const Vi=()=>Nt||xt;let Ri,Fl;{const e=no(),t=(s,i)=>{let n;return(n=e[s])||(n=e[s]=[]),n.push(i),a=>{n.length>1?n.forEach(u=>u(a)):n[0](a)}};Ri=t("__VUE_INSTANCE_SETTERS__",s=>Nt=s),Fl=t("__VUE_SSR_SETTERS__",s=>So=s)}const xo=e=>{const t=Nt;return Ri(e),e.scope.on(),()=>{e.scope.off(),Ri(t)}},Sf=()=>{Nt&&Nt.scope.off(),Ri(null)},ub=er("slot,component");function Ll(e,{isNativeTag:t}){(ub(e)||t(e))&&Y("Do not use built-in or reserved HTML elements as component id: "+e)}function Of(e){return e.vnode.shapeFlag&4}let So=!1;function cb(e,t=!1,s=!1){t&&Fl(t);const{props:i,children:n}=e.vnode,a=Of(e);S_(e,i,a,t),U_(e,n,s);const u=a?db(e,t):void 0;return t&&Fl(!1),u}function db(e,t){var s;const i=e.type;if({}.NODE_ENV!=="production"){if(i.name&&Ll(i.name,e.appContext.config),i.components){const a=Object.keys(i.components);for(let u=0;u<a.length;u++)Ll(a[u],e.appContext.config)}if(i.directives){const a=Object.keys(i.directives);for(let u=0;u<a.length;u++)Dd(a[u])}i.compilerOptions&&fb()&&Y('"compilerOptions" is only supported when using a build of Vue that includes the runtime compiler. Since you are using a runtime-only build, the options should be passed via your build tool config instead.')}e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Gd),{}.NODE_ENV!=="production"&&g_(e);const{setup:n}=i;if(n){tr();const a=e.setupContext=n.length>1?pb(e):null,u=xo(e),c=Tn(n,e,0,[{}.NODE_ENV!=="production"?zs(e.props):e.props,a]),h=Ka(c);if(sr(),u(),(h||e.sp)&&!An(e)&&Ld(e),h){if(c.then(Sf,Sf),t)return c.then(m=>{Tf(e,m,t)}).catch(m=>{fo(m,e,0)});if(e.asyncDep=c,{}.NODE_ENV!=="production"&&!e.suspense){const m=(s=i.name)!=null?s:"Anonymous";Y(`Component <${m}>: setup function returned a promise, but no <Suspense> boundary was found in the parent component tree. A component with async setup() must be nested in a <Suspense> in order to be rendered.`)}}else Tf(e,c,t)}else Nf(e,t)}function Tf(e,t,s){xe(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Je(t)?({}.NODE_ENV!=="production"&&an(t)&&Y("setup() should not return VNodes directly - return a render function instead."),{}.NODE_ENV!=="production"&&(e.devtoolsRawSetupState=t),e.setupState=cd(t),{}.NODE_ENV!=="production"&&v_(e)):{}.NODE_ENV!=="production"&&t!==void 0&&Y(`setup() should return an object. Received: ${t===null?"null":typeof t}`),Nf(e,s)}let Bl;const fb=()=>!Bl;function Nf(e,t,s){const i=e.type;if(!e.render){if(!t&&Bl&&!i.render){const n=i.template||Ol(e).template;if(n){({}).NODE_ENV!=="production"&&ar(e,"compile");const{isCustomElement:a,compilerOptions:u}=e.appContext.config,{delimiters:c,compilerOptions:h}=i,m=pt(pt({isCustomElement:a,delimiters:c},u),h);i.render=Bl(n,m),{}.NODE_ENV!=="production"&&lr(e,"compile")}}e.render=i.render||Ot}{const n=xo(e);tr();try{b_(e)}finally{sr(),n()}}({}).NODE_ENV!=="production"&&!i.render&&e.render===Ot&&!t&&(i.template?Y('Component provided template option but runtime compilation is not supported in this build of Vue. Configure your bundler to alias "vue" to "vue/dist/vue.esm-bundler.js".'):Y("Component is missing template or render function: ",i))}const If={}.NODE_ENV!=="production"?{get(e,t){return Pi(),Tt(e,"get",""),e[t]},set(){return Y("setupContext.attrs is readonly."),!1},deleteProperty(){return Y("setupContext.attrs is readonly."),!1}}:{get(e,t){return Tt(e,"get",""),e[t]}};function hb(e){return new Proxy(e.slots,{get(t,s){return Tt(e,"get","$slots"),t[s]}})}function pb(e){const t=s=>{if({}.NODE_ENV!=="production"&&(e.exposed&&Y("expose() should be called only once per setup()."),s!=null)){let i=typeof s;i==="object"&&(ge(s)?i="array":Dt(s)&&(i="ref")),i!=="object"&&Y(`expose() should be passed a plain object, received ${i}.`)}e.exposed=s||{}};if({}.NODE_ENV!=="production"){let s,i;return Object.freeze({get attrs(){return s||(s=new Proxy(e.attrs,If))},get slots(){return i||(i=hb(e))},get emit(){return(n,...a)=>e.emit(n,...a)},expose:t})}else return{attrs:new Proxy(e.attrs,If),slots:e.slots,emit:e.emit,expose:t}}function Ui(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(cd(cl(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in nn)return nn[s](e)},has(t,s){return s in t||s in nn}})):e.proxy}const mb=/(?:^|[-_])(\w)/g,gb=e=>e.replace(mb,t=>t.toUpperCase()).replace(/[-_]/g,"");function $l(e,t=!0){return xe(e)?e.displayName||e.name:e.name||t&&e.__name}function Fi(e,t,s=!1){let i=$l(t);if(!i&&t.__file){const n=t.__file.match(/([^/\\]+)\.\w+$/);n&&(i=n[1])}if(!i&&e&&e.parent){const n=a=>{for(const u in a)if(a[u]===t)return u};i=n(e.components||e.parent.type.components)||n(e.appContext.components)}return i?gb(i):s?"App":"Anonymous"}function Af(e){return xe(e)&&"__vccOpts"in e}const Us=(e,t)=>{const s=Ov(e,t,So);if({}.NODE_ENV!=="production"){const i=Vi();i&&i.appContext.config.warnRecursiveComputed&&(s._warnRecursive=!0)}return s};function jl(e,t,s){const i=arguments.length;return i===2?Je(t)&&!ge(t)?an(t)?M(e,null,[t]):M(e,t):M(e,null,t):(i>3?s=Array.prototype.slice.call(arguments,2):i===3&&an(s)&&(s=[s]),M(e,t,s))}function vb(){if({}.NODE_ENV==="production"||typeof window>"u")return;const e={style:"color:#3ba776"},t={style:"color:#1677ff"},s={style:"color:#f5222d"},i={style:"color:#eb2f96"},n={__vue_custom_formatter:!0,header(v){return Je(v)?v.__isVue?["div",e,"VueInstance"]:Dt(v)?["div",{},["span",e,p(v)],"<",c("_value"in v?v._value:v),">"]:Jr(v)?["div",{},["span",e,Qt(v)?"ShallowReactive":"Reactive"],"<",c(v),`>${nr(v)?" (readonly)":""}`]:nr(v)?["div",{},["span",e,Qt(v)?"ShallowReadonly":"Readonly"],"<",c(v),">"]:null:null},hasBody(v){return v&&v.__isVue},body(v){if(v&&v.__isVue)return["div",{},...a(v.$)]}};function a(v){const w=[];v.type.props&&v.props&&w.push(u("props",Me(v.props))),v.setupState!==st&&w.push(u("setup",v.setupState)),v.data!==st&&w.push(u("data",Me(v.data)));const D=h(v,"computed");D&&w.push(u("computed",D));const k=h(v,"inject");return k&&w.push(u("injected",k)),w.push(["div",{},["span",{style:i.style+";opacity:0.66"},"$ (internal): "],["object",{object:v}]]),w}function u(v,w){return w=pt({},w),Object.keys(w).length?["div",{style:"line-height:1.25em;margin-bottom:0.6em"},["div",{style:"color:#476582"},v],["div",{style:"padding-left:1.25em"},...Object.keys(w).map(D=>["div",{},["span",i,D+": "],c(w[D],!1)])]]:["span",{}]}function c(v,w=!0){return typeof v=="number"?["span",t,v]:typeof v=="string"?["span",s,JSON.stringify(v)]:typeof v=="boolean"?["span",i,v]:Je(v)?["object",{object:w?Me(v):v}]:["span",s,String(v)]}function h(v,w){const D=v.type;if(xe(D))return;const k={};for(const L in v.ctx)m(D,L,w)&&(k[L]=v.ctx[L]);return k}function m(v,w,D){const k=v[D];if(ge(k)&&k.includes(w)||Je(k)&&w in k||v.extends&&m(v.extends,w,D)||v.mixins&&v.mixins.some(L=>m(L,w,D)))return!0}function p(v){return Qt(v)?"ShallowRef":v.effect?"ComputedRef":"Ref"}window.devtoolsFormatters?window.devtoolsFormatters.push(n):window.devtoolsFormatters=[n]}const Mf="3.5.13",Ys={}.NODE_ENV!=="production"?Y:Ot;/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Hl;const Pf=typeof window<"u"&&window.trustedTypes;if(Pf)try{Hl=Pf.createPolicy("vue",{createHTML:e=>e})}catch(e){({}).NODE_ENV!=="production"&&Ys(`Error creating trusted types policy: ${e}`)}const kf=Hl?e=>Hl.createHTML(e):e=>e,_b="http://www.w3.org/2000/svg",bb="http://www.w3.org/1998/Math/MathML",ur=typeof document<"u"?document:null,Vf=ur&&ur.createElement("template"),yb={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,i)=>{const n=t==="svg"?ur.createElementNS(_b,e):t==="mathml"?ur.createElementNS(bb,e):s?ur.createElement(e,{is:s}):ur.createElement(e);return e==="select"&&i&&i.multiple!=null&&n.setAttribute("multiple",i.multiple),n},createText:e=>ur.createTextNode(e),createComment:e=>ur.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>ur.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,i,n,a){const u=s?s.previousSibling:t.lastChild;if(n&&(n===a||n.nextSibling))for(;t.insertBefore(n.cloneNode(!0),s),!(n===a||!(n=n.nextSibling)););else{Vf.innerHTML=kf(i==="svg"?`<svg>${e}</svg>`:i==="mathml"?`<math>${e}</math>`:e);const c=Vf.content;if(i==="svg"||i==="mathml"){const h=c.firstChild;for(;h.firstChild;)c.appendChild(h.firstChild);c.removeChild(h)}t.insertBefore(c,s)}return[u?u.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},Pr="transition",Oo="animation",To=Symbol("_vtc"),Rf={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},wb=pt({},Md,Rf),Uf=(e=>(e.displayName="Transition",e.props=wb,e))((e,{slots:t})=>jl(e_,Eb(e),t)),un=(e,t=[])=>{ge(e)?e.forEach(s=>s(...t)):e&&e(...t)},Ff=e=>e?ge(e)?e.some(t=>t.length>1):e.length>1:!1;function Eb(e){const t={};for(const te in e)te in Rf||(t[te]=e[te]);if(e.css===!1)return t;const{name:s="v",type:i,duration:n,enterFromClass:a=`${s}-enter-from`,enterActiveClass:u=`${s}-enter-active`,enterToClass:c=`${s}-enter-to`,appearFromClass:h=a,appearActiveClass:m=u,appearToClass:p=c,leaveFromClass:v=`${s}-leave-from`,leaveActiveClass:w=`${s}-leave-active`,leaveToClass:D=`${s}-leave-to`}=e,k=Cb(n),L=k&&k[0],re=k&&k[1],{onBeforeEnter:I,onEnter:ne,onEnterCancelled:Q,onLeave:ye,onLeaveCancelled:Z,onBeforeAppear:he=I,onAppear:be=ne,onAppearCancelled:Ae=Q}=t,ue=(te,Ke,vt,mt)=>{te._enterCancelled=mt,cn(te,Ke?p:c),cn(te,Ke?m:u),vt&&vt()},ae=(te,Ke)=>{te._isLeaving=!1,cn(te,v),cn(te,D),cn(te,w),Ke&&Ke()},V=te=>(Ke,vt)=>{const mt=te?be:ne,ft=()=>ue(Ke,te,vt);un(mt,[Ke,ft]),Lf(()=>{cn(Ke,te?h:a),cr(Ke,te?p:c),Ff(mt)||Bf(Ke,i,L,ft)})};return pt(t,{onBeforeEnter(te){un(I,[te]),cr(te,a),cr(te,u)},onBeforeAppear(te){un(he,[te]),cr(te,h),cr(te,m)},onEnter:V(!1),onAppear:V(!0),onLeave(te,Ke){te._isLeaving=!0;const vt=()=>ae(te,Ke);cr(te,v),te._enterCancelled?(cr(te,w),Hf()):(Hf(),cr(te,w)),Lf(()=>{te._isLeaving&&(cn(te,v),cr(te,D),Ff(ye)||Bf(te,i,re,vt))}),un(ye,[te,vt])},onEnterCancelled(te){ue(te,!1,void 0,!0),un(Q,[te])},onAppearCancelled(te){ue(te,!0,void 0,!0),un(Ae,[te])},onLeaveCancelled(te){ae(te),un(Z,[te])}})}function Cb(e){if(e==null)return null;if(Je(e))return[ql(e.enter),ql(e.leave)];{const t=ql(e);return[t,t]}}function ql(e){const t=qg(e);return{}.NODE_ENV!=="production"&&kv(t,"<transition> explicit duration"),t}function cr(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.add(s)),(e[To]||(e[To]=new Set)).add(t)}function cn(e,t){t.split(/\s+/).forEach(i=>i&&e.classList.remove(i));const s=e[To];s&&(s.delete(t),s.size||(e[To]=void 0))}function Lf(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Db=0;function Bf(e,t,s,i){const n=e._endId=++Db,a=()=>{n===e._endId&&i()};if(s!=null)return setTimeout(a,s);const{type:u,timeout:c,propCount:h}=xb(e,t);if(!u)return i();const m=u+"end";let p=0;const v=()=>{e.removeEventListener(m,w),a()},w=D=>{D.target===e&&++p>=h&&v()};setTimeout(()=>{p<h&&v()},c+1),e.addEventListener(m,w)}function xb(e,t){const s=window.getComputedStyle(e),i=k=>(s[k]||"").split(", "),n=i(`${Pr}Delay`),a=i(`${Pr}Duration`),u=$f(n,a),c=i(`${Oo}Delay`),h=i(`${Oo}Duration`),m=$f(c,h);let p=null,v=0,w=0;t===Pr?u>0&&(p=Pr,v=u,w=a.length):t===Oo?m>0&&(p=Oo,v=m,w=h.length):(v=Math.max(u,m),p=v>0?u>m?Pr:Oo:null,w=p?p===Pr?a.length:h.length:0);const D=p===Pr&&/\b(transform|all)(,|$)/.test(i(`${Pr}Property`).toString());return{type:p,timeout:v,propCount:w,hasTransform:D}}function $f(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((s,i)=>jf(s)+jf(e[i])))}function jf(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function Hf(){return document.body.offsetHeight}function Sb(e,t,s){const i=e[To];i&&(t=(t?[t,...i]:[...i]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const Li=Symbol("_vod"),qf=Symbol("_vsh"),zl={beforeMount(e,{value:t},{transition:s}){e[Li]=e.style.display==="none"?"":e.style.display,s&&t?s.beforeEnter(e):No(e,t)},mounted(e,{value:t},{transition:s}){s&&t&&s.enter(e)},updated(e,{value:t,oldValue:s},{transition:i}){!t!=!s&&(i?t?(i.beforeEnter(e),No(e,!0),i.enter(e)):i.leave(e,()=>{No(e,!1)}):No(e,t))},beforeUnmount(e,{value:t}){No(e,t)}};({}).NODE_ENV!=="production"&&(zl.name="show");function No(e,t){e.style.display=t?e[Li]:"none",e[qf]=!t}const Ob=Symbol({}.NODE_ENV!=="production"?"CSS_VAR_TEXT":""),Tb=/(^|;)\s*display\s*:/;function Nb(e,t,s){const i=e.style,n=dt(s);let a=!1;if(s&&!n){if(t)if(dt(t))for(const u of t.split(";")){const c=u.slice(0,u.indexOf(":")).trim();s[c]==null&&Bi(i,c,"")}else for(const u in t)s[u]==null&&Bi(i,u,"");for(const u in s)u==="display"&&(a=!0),Bi(i,u,s[u])}else if(n){if(t!==s){const u=i[Ob];u&&(s+=";"+u),i.cssText=s,a=Tb.test(s)}}else t&&e.removeAttribute("style");Li in e&&(e[Li]=a?i.display:"",e[qf]&&(i.display="none"))}const Ib=/[^\\];\s*$/,zf=/\s*!important$/;function Bi(e,t,s){if(ge(s))s.forEach(i=>Bi(e,t,i));else if(s==null&&(s=""),{}.NODE_ENV!=="production"&&Ib.test(s)&&Ys(`Unexpected semicolon at the end of '${t}' style value: '${s}'`),t.startsWith("--"))e.setProperty(t,s);else{const i=Ab(e,t);zf.test(s)?e.setProperty(Sr(i),s.replace(zf,""),"important"):e[i]=s}}const Wf=["Webkit","Moz","ms"],Wl={};function Ab(e,t){const s=Wl[t];if(s)return s;let i=Kt(t);if(i!=="filter"&&i in e)return Wl[t]=i;i=Qr(i);for(let n=0;n<Wf.length;n++){const a=Wf[n]+i;if(a in e)return Wl[t]=a}return t}const Gf="http://www.w3.org/1999/xlink";function Kf(e,t,s,i,n,a=tv(t)){i&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(Gf,t.slice(6,t.length)):e.setAttributeNS(Gf,t,s):s==null||a&&!Uc(s)?e.removeAttribute(t):e.setAttribute(t,a?"":As(s)?String(s):s)}function Qf(e,t,s,i,n){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?kf(s):s);return}const a=e.tagName;if(t==="value"&&a!=="PROGRESS"&&!a.includes("-")){const c=a==="OPTION"?e.getAttribute("value")||"":e.value,h=s==null?e.type==="checkbox"?"on":"":String(s);(c!==h||!("_value"in e))&&(e.value=h),s==null&&e.removeAttribute(t),e._value=s;return}let u=!1;if(s===""||s==null){const c=typeof e[t];c==="boolean"?s=Uc(s):s==null&&c==="string"?(s="",u=!0):c==="number"&&(s=0,u=!0)}try{e[t]=s}catch(c){({}).NODE_ENV!=="production"&&!u&&Ys(`Failed setting prop "${t}" on <${a.toLowerCase()}>: value ${s} is invalid.`,c)}u&&e.removeAttribute(n||t)}function kr(e,t,s,i){e.addEventListener(t,s,i)}function Mb(e,t,s,i){e.removeEventListener(t,s,i)}const Yf=Symbol("_vei");function Pb(e,t,s,i,n=null){const a=e[Yf]||(e[Yf]={}),u=a[t];if(i&&u)u.value={}.NODE_ENV!=="production"?Jf(i,t):i;else{const[c,h]=kb(t);if(i){const m=a[t]=Ub({}.NODE_ENV!=="production"?Jf(i,t):i,n);kr(e,c,m,h)}else u&&(Mb(e,c,u,h),a[t]=void 0)}}const Zf=/(?:Once|Passive|Capture)$/;function kb(e){let t;if(Zf.test(e)){t={};let i;for(;i=e.match(Zf);)e=e.slice(0,e.length-i[0].length),t[i[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Sr(e.slice(2)),t]}let Gl=0;const Vb=Promise.resolve(),Rb=()=>Gl||(Vb.then(()=>Gl=0),Gl=Date.now());function Ub(e,t){const s=i=>{if(!i._vts)i._vts=Date.now();else if(i._vts<=s.attached)return;Ps(Fb(i,s.value),t,5,[i])};return s.value=e,s.attached=Rb(),s}function Jf(e,t){return xe(e)||ge(e)?e:(Ys(`Wrong type passed as event handler to ${t} - did you forget @ or : in front of your prop?
Expected function or array of functions, received type ${typeof e}.`),Ot)}function Fb(e,t){if(ge(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(i=>n=>!n._stopped&&i&&i(n))}else return t}const Xf=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Lb=(e,t,s,i,n,a)=>{const u=n==="svg";t==="class"?Sb(e,i,u):t==="style"?Nb(e,s,i):to(t)?ni(t)||Pb(e,t,s,i,a):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Bb(e,t,i,u))?(Qf(e,t,i),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Kf(e,t,i,u,a,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!dt(i))?Qf(e,Kt(t),i,a,t):(t==="true-value"?e._trueValue=i:t==="false-value"&&(e._falseValue=i),Kf(e,t,i,u))};function Bb(e,t,s,i){if(i)return!!(t==="innerHTML"||t==="textContent"||t in e&&Xf(t)&&xe(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const n=e.tagName;if(n==="IMG"||n==="VIDEO"||n==="CANVAS"||n==="SOURCE")return!1}return Xf(t)&&dt(s)?!1:t in e}const kn=e=>{const t=e.props["onUpdate:modelValue"]||!1;return ge(t)?s=>Sn(t,s):t};function $b(e){e.target.composing=!0}function eh(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const dr=Symbol("_assign"),Xt={created(e,{modifiers:{lazy:t,trim:s,number:i}},n){e[dr]=kn(n);const a=i||n.props&&n.props.type==="number";kr(e,t?"change":"input",u=>{if(u.target.composing)return;let c=e.value;s&&(c=c.trim()),a&&(c=ai(c)),e[dr](c)}),s&&kr(e,"change",()=>{e.value=e.value.trim()}),t||(kr(e,"compositionstart",$b),kr(e,"compositionend",eh),kr(e,"change",eh))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:s,modifiers:{lazy:i,trim:n,number:a}},u){if(e[dr]=kn(u),e.composing)return;const c=(a||e.type==="number")&&!/^0\d/.test(e.value)?ai(e.value):e.value,h=t??"";c!==h&&(document.activeElement===e&&e.type!=="range"&&(i&&t===s||n&&e.value.trim()===h)||(e.value=h))}},$i={deep:!0,created(e,t,s){e[dr]=kn(s),kr(e,"change",()=>{const i=e._modelValue,n=Io(e),a=e.checked,u=e[dr];if(ge(i)){const c=Za(i,n),h=c!==-1;if(a&&!h)u(i.concat(n));else if(!a&&h){const m=[...i];m.splice(c,1),u(m)}}else if(xn(i)){const c=new Set(i);a?c.add(n):c.delete(n),u(c)}else u(rh(e,a))})},mounted:th,beforeUpdate(e,t,s){e[dr]=kn(s),th(e,t,s)}};function th(e,{value:t,oldValue:s},i){e._modelValue=t;let n;if(ge(t))n=Za(t,i.props.value)>-1;else if(xn(t))n=t.has(i.props.value);else{if(t===s)return;n=oo(t,rh(e,!0))}e.checked!==n&&(e.checked=n)}const Kl={deep:!0,created(e,{value:t,modifiers:{number:s}},i){const n=xn(t);kr(e,"change",()=>{const a=Array.prototype.filter.call(e.options,u=>u.selected).map(u=>s?ai(Io(u)):Io(u));e[dr](e.multiple?n?new Set(a):a:a[0]),e._assigning=!0,pl(()=>{e._assigning=!1})}),e[dr]=kn(i)},mounted(e,{value:t}){sh(e,t)},beforeUpdate(e,t,s){e[dr]=kn(s)},updated(e,{value:t}){e._assigning||sh(e,t)}};function sh(e,t){const s=e.multiple,i=ge(t);if(s&&!i&&!xn(t)){({}).NODE_ENV!=="production"&&Ys(`<select multiple v-model> expects an Array or Set value for its binding, but got ${Object.prototype.toString.call(t).slice(8,-1)}.`);return}for(let n=0,a=e.options.length;n<a;n++){const u=e.options[n],c=Io(u);if(s)if(i){const h=typeof c;h==="string"||h==="number"?u.selected=t.some(m=>String(m)===String(c)):u.selected=Za(t,c)>-1}else u.selected=t.has(c);else if(oo(Io(u),t)){e.selectedIndex!==n&&(e.selectedIndex=n);return}}!s&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}function Io(e){return"_value"in e?e._value:e.value}function rh(e,t){const s=t?"_trueValue":"_falseValue";return s in e?e[s]:t}const jb=["ctrl","shift","alt","meta"],Hb={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>jb.some(s=>e[`${s}Key`]&&!t.includes(s))},Pt=(e,t)=>{const s=e._withMods||(e._withMods={}),i=t.join(".");return s[i]||(s[i]=(n,...a)=>{for(let u=0;u<t.length;u++){const c=Hb[t[u]];if(c&&c(n,t))return}return e(n,...a)})},qb=pt({patchProp:Lb},yb);let nh;function zb(){return nh||(nh=B_(qb))}const Wb=(...e)=>{const t=zb().createApp(...e);({}).NODE_ENV!=="production"&&(Kb(t),Qb(t));const{mount:s}=t;return t.mount=i=>{const n=Yb(i);if(!n)return;const a=t._component;!xe(a)&&!a.render&&!a.template&&(a.template=n.innerHTML),n.nodeType===1&&(n.textContent="");const u=s(n,!1,Gb(n));return n instanceof Element&&(n.removeAttribute("v-cloak"),n.setAttribute("data-v-app","")),u},t};function Gb(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Kb(e){Object.defineProperty(e.config,"isNativeTag",{value:t=>Jg(t)||Xg(t)||ev(t),writable:!1})}function Qb(e){{const t=e.config.isCustomElement;Object.defineProperty(e.config,"isCustomElement",{get(){return t},set(){Ys("The `isCustomElement` config option is deprecated. Use `compilerOptions.isCustomElement` instead.")}});const s=e.config.compilerOptions,i='The `compilerOptions` config option is only respected when using a build of Vue.js that includes the runtime compiler (aka "full build"). Since you are using the runtime-only build, `compilerOptions` must be passed to `@vue/compiler-dom` in the build setup instead.\n- For vue-loader: pass it via vue-loader\'s `compilerOptions` loader option.\n- For vue-cli: see https://cli.vuejs.org/guide/webpack.html#modifying-options-of-a-loader\n- For vite: pass it via @vitejs/plugin-vue options. See https://github.com/vitejs/vite-plugin-vue/tree/main/packages/plugin-vue#example-for-passing-options-to-vuecompiler-sfc';Object.defineProperty(e.config,"compilerOptions",{get(){return Ys(i),s},set(){Ys(i)}})}}function Yb(e){if(dt(e)){const t=document.querySelector(e);return{}.NODE_ENV!=="production"&&!t&&Ys(`Failed to mount app: mount target selector "${e}" returned null.`),t}return{}.NODE_ENV!=="production"&&window.ShadowRoot&&e instanceof window.ShadowRoot&&e.mode==="closed"&&Ys('mounting on a ShadowRoot with `{mode: "closed"}` may lead to unpredictable bugs'),e}/**
* vue v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Zb(){vb()}({}).NODE_ENV!=="production"&&Zb();var Jb=!1;function Xb(){return oh().__VUE_DEVTOOLS_GLOBAL_HOOK__}function oh(){return typeof navigator<"u"&&typeof window<"u"?window:typeof globalThis<"u"?globalThis:{}}const ey=typeof Proxy=="function",ty="devtools-plugin:setup",sy="plugin:settings:set";let Vn,Ql;function ry(){var e;return Vn!==void 0||(typeof window<"u"&&window.performance?(Vn=!0,Ql=window.performance):typeof globalThis<"u"&&(!((e=globalThis.perf_hooks)===null||e===void 0)&&e.performance)?(Vn=!0,Ql=globalThis.perf_hooks.performance):Vn=!1),Vn}function ny(){return ry()?Ql.now():Date.now()}class oy{constructor(t,s){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=t,this.hook=s;const i={};if(t.settings)for(const u in t.settings){const c=t.settings[u];i[u]=c.defaultValue}const n=`__vue-devtools-plugin-settings__${t.id}`;let a=Object.assign({},i);try{const u=localStorage.getItem(n),c=JSON.parse(u);Object.assign(a,c)}catch{}this.fallbacks={getSettings(){return a},setSettings(u){try{localStorage.setItem(n,JSON.stringify(u))}catch{}a=u},now(){return ny()}},s&&s.on(sy,(u,c)=>{u===this.plugin.id&&this.fallbacks.setSettings(c)}),this.proxiedOn=new Proxy({},{get:(u,c)=>this.target?this.target.on[c]:(...h)=>{this.onQueue.push({method:c,args:h})}}),this.proxiedTarget=new Proxy({},{get:(u,c)=>this.target?this.target[c]:c==="on"?this.proxiedOn:Object.keys(this.fallbacks).includes(c)?(...h)=>(this.targetQueue.push({method:c,args:h,resolve:()=>{}}),this.fallbacks[c](...h)):(...h)=>new Promise(m=>{this.targetQueue.push({method:c,args:h,resolve:m})})})}async setRealTarget(t){this.target=t;for(const s of this.onQueue)this.target.on[s.method](...s.args);for(const s of this.targetQueue)s.resolve(await this.target[s.method](...s.args))}}function Yl(e,t){const s=e,i=oh(),n=Xb(),a=ey&&s.enableEarlyProxy;if(n&&(i.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__||!a))n.emit(ty,e,t);else{const u=a?new oy(s,n):null;(i.__VUE_DEVTOOLS_PLUGINS__=i.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:s,setupFn:t,proxy:u}),u&&t(u.proxiedTarget)}}/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const iy={}.NODE_ENV!=="production"?Symbol("pinia"):Symbol();var dn;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(dn||(dn={}));const Zl=typeof window<"u",ih=(()=>typeof window=="object"&&window.window===window?window:typeof self=="object"&&self.self===self?self:typeof global=="object"&&global.global===global?global:typeof globalThis=="object"?globalThis:{HTMLElement:null})();function ay(e,{autoBom:t=!1}={}){return t&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob([String.fromCharCode(65279),e],{type:e.type}):e}function Jl(e,t,s){const i=new XMLHttpRequest;i.open("GET",e),i.responseType="blob",i.onload=function(){uh(i.response,t,s)},i.onerror=function(){console.error("could not download file")},i.send()}function ah(e){const t=new XMLHttpRequest;t.open("HEAD",e,!1);try{t.send()}catch{}return t.status>=200&&t.status<=299}function ji(e){try{e.dispatchEvent(new MouseEvent("click"))}catch{const s=document.createEvent("MouseEvents");s.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(s)}}const Hi=typeof navigator=="object"?navigator:{userAgent:""},lh=(()=>/Macintosh/.test(Hi.userAgent)&&/AppleWebKit/.test(Hi.userAgent)&&!/Safari/.test(Hi.userAgent))(),uh=Zl?typeof HTMLAnchorElement<"u"&&"download"in HTMLAnchorElement.prototype&&!lh?ly:"msSaveOrOpenBlob"in Hi?uy:cy:()=>{};function ly(e,t="download",s){const i=document.createElement("a");i.download=t,i.rel="noopener",typeof e=="string"?(i.href=e,i.origin!==location.origin?ah(i.href)?Jl(e,t,s):(i.target="_blank",ji(i)):ji(i)):(i.href=URL.createObjectURL(e),setTimeout(function(){URL.revokeObjectURL(i.href)},4e4),setTimeout(function(){ji(i)},0))}function uy(e,t="download",s){if(typeof e=="string")if(ah(e))Jl(e,t,s);else{const i=document.createElement("a");i.href=e,i.target="_blank",setTimeout(function(){ji(i)})}else navigator.msSaveOrOpenBlob(ay(e,s),t)}function cy(e,t,s,i){if(i=i||open("","_blank"),i&&(i.document.title=i.document.body.innerText="downloading..."),typeof e=="string")return Jl(e,t,s);const n=e.type==="application/octet-stream",a=/constructor/i.test(String(ih.HTMLElement))||"safari"in ih,u=/CriOS\/[\d]+/.test(navigator.userAgent);if((u||n&&a||lh)&&typeof FileReader<"u"){const c=new FileReader;c.onloadend=function(){let h=c.result;if(typeof h!="string")throw i=null,new Error("Wrong reader.result type");h=u?h:h.replace(/^data:[^;]*;/,"data:attachment/file;"),i?i.location.href=h:location.assign(h),i=null},c.readAsDataURL(e)}else{const c=URL.createObjectURL(e);i?i.location.assign(c):location.href=c,i=null,setTimeout(function(){URL.revokeObjectURL(c)},4e4)}}function kt(e,t){const s="🍍 "+e;typeof __VUE_DEVTOOLS_TOAST__=="function"?__VUE_DEVTOOLS_TOAST__(s,t):t==="error"?console.error(s):t==="warn"?console.warn(s):console.log(s)}function Xl(e){return"_a"in e&&"install"in e}function ch(){if(!("clipboard"in navigator))return kt("Your browser doesn't support the Clipboard API","error"),!0}function dh(e){return e instanceof Error&&e.message.toLowerCase().includes("document is not focused")?(kt('You need to activate the "Emulate a focused page" setting in the "Rendering" panel of devtools.',"warn"),!0):!1}async function dy(e){if(!ch())try{await navigator.clipboard.writeText(JSON.stringify(e.state.value)),kt("Global state copied to clipboard.")}catch(t){if(dh(t))return;kt("Failed to serialize the state. Check the console for more details.","error"),console.error(t)}}async function fy(e){if(!ch())try{fh(e,JSON.parse(await navigator.clipboard.readText())),kt("Global state pasted from clipboard.")}catch(t){if(dh(t))return;kt("Failed to deserialize the state from clipboard. Check the console for more details.","error"),console.error(t)}}async function hy(e){try{uh(new Blob([JSON.stringify(e.state.value)],{type:"text/plain;charset=utf-8"}),"pinia-state.json")}catch(t){kt("Failed to export the state as JSON. Check the console for more details.","error"),console.error(t)}}let fr;function py(){fr||(fr=document.createElement("input"),fr.type="file",fr.accept=".json");function e(){return new Promise((t,s)=>{fr.onchange=async()=>{const i=fr.files;if(!i)return t(null);const n=i.item(0);return t(n?{text:await n.text(),file:n}:null)},fr.oncancel=()=>t(null),fr.onerror=s,fr.click()})}return e}async function my(e){try{const s=await py()();if(!s)return;const{text:i,file:n}=s;fh(e,JSON.parse(i)),kt(`Global state imported from "${n.name}".`)}catch(t){kt("Failed to import the state from JSON. Check the console for more details.","error"),console.error(t)}}function fh(e,t){for(const s in t){const i=e.state.value[s];i?Object.assign(i,t[s]):e.state.value[s]=t[s]}}function Fs(e){return{_custom:{display:e}}}const hh="🍍 Pinia (root)",qi="_root";function gy(e){return Xl(e)?{id:qi,label:hh}:{id:e.$id,label:e.$id}}function vy(e){if(Xl(e)){const s=Array.from(e._s.keys()),i=e._s;return{state:s.map(a=>({editable:!0,key:a,value:e.state.value[a]})),getters:s.filter(a=>i.get(a)._getters).map(a=>{const u=i.get(a);return{editable:!1,key:a,value:u._getters.reduce((c,h)=>(c[h]=u[h],c),{})}})}}const t={state:Object.keys(e.$state).map(s=>({editable:!0,key:s,value:e.$state[s]}))};return e._getters&&e._getters.length&&(t.getters=e._getters.map(s=>({editable:!1,key:s,value:e[s]}))),e._customProperties.size&&(t.customProperties=Array.from(e._customProperties).map(s=>({editable:!0,key:s,value:e[s]}))),t}function _y(e){return e?Array.isArray(e)?e.reduce((t,s)=>(t.keys.push(s.key),t.operations.push(s.type),t.oldValue[s.key]=s.oldValue,t.newValue[s.key]=s.newValue,t),{oldValue:{},keys:[],operations:[],newValue:{}}):{operation:Fs(e.type),key:Fs(e.key),oldValue:e.oldValue,newValue:e.newValue}:{}}function by(e){switch(e){case dn.direct:return"mutation";case dn.patchFunction:return"$patch";case dn.patchObject:return"$patch";default:return"unknown"}}let Rn=!0;const zi=[],fn="pinia:mutations",qt="pinia",{assign:yy}=Object,Wi=e=>"🍍 "+e;function wy(e,t){Yl({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:zi,app:e},s=>{typeof s.now!="function"&&kt("You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),s.addTimelineLayer({id:fn,label:"Pinia 🍍",color:15064968}),s.addInspector({id:qt,label:"Pinia 🍍",icon:"storage",treeFilterPlaceholder:"Search stores",actions:[{icon:"content_copy",action:()=>{dy(t)},tooltip:"Serialize and copy the state"},{icon:"content_paste",action:async()=>{await fy(t),s.sendInspectorTree(qt),s.sendInspectorState(qt)},tooltip:"Replace the state with the content of your clipboard"},{icon:"save",action:()=>{hy(t)},tooltip:"Save the state as a JSON file"},{icon:"folder_open",action:async()=>{await my(t),s.sendInspectorTree(qt),s.sendInspectorState(qt)},tooltip:"Import the state from a JSON file"}],nodeActions:[{icon:"restore",tooltip:'Reset the state (with "$reset")',action:i=>{const n=t._s.get(i);n?typeof n.$reset!="function"?kt(`Cannot reset "${i}" store because it doesn't have a "$reset" method implemented.`,"warn"):(n.$reset(),kt(`Store "${i}" reset.`)):kt(`Cannot reset "${i}" store because it wasn't found.`,"warn")}}]}),s.on.inspectComponent((i,n)=>{const a=i.componentInstance&&i.componentInstance.proxy;if(a&&a._pStores){const u=i.componentInstance.proxy._pStores;Object.values(u).forEach(c=>{i.instanceData.state.push({type:Wi(c.$id),key:"state",editable:!0,value:c._isOptionsAPI?{_custom:{value:Me(c.$state),actions:[{icon:"restore",tooltip:"Reset the state of this store",action:()=>c.$reset()}]}}:Object.keys(c.$state).reduce((h,m)=>(h[m]=c.$state[m],h),{})}),c._getters&&c._getters.length&&i.instanceData.state.push({type:Wi(c.$id),key:"getters",editable:!1,value:c._getters.reduce((h,m)=>{try{h[m]=c[m]}catch(p){h[m]=p}return h},{})})})}}),s.on.getInspectorTree(i=>{if(i.app===e&&i.inspectorId===qt){let n=[t];n=n.concat(Array.from(t._s.values())),i.rootNodes=(i.filter?n.filter(a=>"$id"in a?a.$id.toLowerCase().includes(i.filter.toLowerCase()):hh.toLowerCase().includes(i.filter.toLowerCase())):n).map(gy)}}),globalThis.$pinia=t,s.on.getInspectorState(i=>{if(i.app===e&&i.inspectorId===qt){const n=i.nodeId===qi?t:t._s.get(i.nodeId);if(!n)return;n&&(i.nodeId!==qi&&(globalThis.$store=Me(n)),i.state=vy(n))}}),s.on.editInspectorState((i,n)=>{if(i.app===e&&i.inspectorId===qt){const a=i.nodeId===qi?t:t._s.get(i.nodeId);if(!a)return kt(`store "${i.nodeId}" not found`,"error");const{path:u}=i;Xl(a)?u.unshift("state"):(u.length!==1||!a._customProperties.has(u[0])||u[0]in a.$state)&&u.unshift("$state"),Rn=!1,i.set(a,u,i.state.value),Rn=!0}}),s.on.editComponentState(i=>{if(i.type.startsWith("🍍")){const n=i.type.replace(/^🍍\s*/,""),a=t._s.get(n);if(!a)return kt(`store "${n}" not found`,"error");const{path:u}=i;if(u[0]!=="state")return kt(`Invalid path for store "${n}":
${u}
Only state can be modified.`);u[0]="$state",Rn=!1,i.set(a,u,i.state.value),Rn=!0}})})}function Ey(e,t){zi.includes(Wi(t.$id))||zi.push(Wi(t.$id)),Yl({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:zi,app:e,settings:{logStoreChanges:{label:"Notify about new/deleted stores",type:"boolean",defaultValue:!0}}},s=>{const i=typeof s.now=="function"?s.now.bind(s):Date.now;t.$onAction(({after:u,onError:c,name:h,args:m})=>{const p=ph++;s.addTimelineEvent({layerId:fn,event:{time:i(),title:"🛫 "+h,subtitle:"start",data:{store:Fs(t.$id),action:Fs(h),args:m},groupId:p}}),u(v=>{Vr=void 0,s.addTimelineEvent({layerId:fn,event:{time:i(),title:"🛬 "+h,subtitle:"end",data:{store:Fs(t.$id),action:Fs(h),args:m,result:v},groupId:p}})}),c(v=>{Vr=void 0,s.addTimelineEvent({layerId:fn,event:{time:i(),logType:"error",title:"💥 "+h,subtitle:"end",data:{store:Fs(t.$id),action:Fs(h),args:m,error:v},groupId:p}})})},!0),t._customProperties.forEach(u=>{Pn(()=>Tr(t[u]),(c,h)=>{s.notifyComponentUpdate(),s.sendInspectorState(qt),Rn&&s.addTimelineEvent({layerId:fn,event:{time:i(),title:"Change",subtitle:u,data:{newValue:c,oldValue:h},groupId:Vr}})},{deep:!0})}),t.$subscribe(({events:u,type:c},h)=>{if(s.notifyComponentUpdate(),s.sendInspectorState(qt),!Rn)return;const m={time:i(),title:by(c),data:yy({store:Fs(t.$id)},_y(u)),groupId:Vr};c===dn.patchFunction?m.subtitle="⤵️":c===dn.patchObject?m.subtitle="🧩":u&&!Array.isArray(u)&&(m.subtitle=u.type),u&&(m.data["rawEvent(s)"]={_custom:{display:"DebuggerEvent",type:"object",tooltip:"raw DebuggerEvent[]",value:u}}),s.addTimelineEvent({layerId:fn,event:m})},{detached:!0,flush:"sync"});const n=t._hotUpdate;t._hotUpdate=cl(u=>{n(u),s.addTimelineEvent({layerId:fn,event:{time:i(),title:"🔥 "+t.$id,subtitle:"HMR update",data:{store:Fs(t.$id),info:Fs("HMR update")}}}),s.notifyComponentUpdate(),s.sendInspectorTree(qt),s.sendInspectorState(qt)});const{$dispose:a}=t;t.$dispose=()=>{a(),s.notifyComponentUpdate(),s.sendInspectorTree(qt),s.sendInspectorState(qt),s.getSettings().logStoreChanges&&kt(`Disposed "${t.$id}" store 🗑`)},s.notifyComponentUpdate(),s.sendInspectorTree(qt),s.sendInspectorState(qt),s.getSettings().logStoreChanges&&kt(`"${t.$id}" store installed 🆕`)})}let ph=0,Vr;function mh(e,t,s){const i=t.reduce((n,a)=>(n[a]=Me(e)[a],n),{});for(const n in i)e[n]=function(){const a=ph,u=s?new Proxy(e,{get(...h){return Vr=a,Reflect.get(...h)},set(...h){return Vr=a,Reflect.set(...h)}}):e;Vr=a;const c=i[n].apply(u,arguments);return Vr=void 0,c}}function Cy({app:e,store:t,options:s}){if(!t.$id.startsWith("__hot:")){if(t._isOptionsAPI=!!s.state,!t._p._testing){mh(t,Object.keys(s.actions),t._isOptionsAPI);const i=t._hotUpdate;Me(t)._hotUpdate=function(n){i.apply(this,arguments),mh(t,Object.keys(n._hmrPayload.actions),!!t._isOptionsAPI)}}Ey(e,t)}}function Dy(){const e=rv(!0),t=e.run(()=>ld({}));let s=[],i=[];const n=cl({install(a){n._a=a,a.provide(iy,n),a.config.globalProperties.$pinia=n,{}.NODE_ENV!=="production"&&{}.NODE_ENV!=="test"&&Zl&&wy(a,n),i.forEach(u=>s.push(u)),i=[]},use(a){return!this._a&&!Jb?i.push(a):s.push(a),this},_p:s,_a:null,_e:e,_s:new Map,state:t});return{}.NODE_ENV!=="production"&&{}.NODE_ENV!=="test"&&Zl&&typeof Proxy<"u"&&n.use(Cy),n}const S3="",je=(e,t)=>{const s=e.__vccOpts||e;for(const[i,n]of t)s[i]=n;return s},xy={name:"App",mounted(){if(!document.querySelector('link[href*="font-awesome"]')){const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)}}},Sy={id:"app"};function Oy(e,t,s,i,n,a){const u=X("router-view");return S(),O("div",Sy,[M(u)])}const Ty=je(xy,[["render",Oy]]);/*!
  * vue-router v4.5.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */const hr=typeof document<"u";function gh(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Ny(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&gh(e.default)}const Xe=Object.assign;function eu(e,t){const s={};for(const i in t){const n=t[i];s[i]=fs(n)?n.map(e):e(n)}return s}const Ao=()=>{},fs=Array.isArray;function He(e){const t=Array.from(arguments).slice(1);console.warn.apply(console,["[Vue Router warn]: "+e].concat(t))}const vh=/#/g,Iy=/&/g,Ay=/\//g,My=/=/g,Py=/\?/g,_h=/\+/g,ky=/%5B/g,Vy=/%5D/g,bh=/%5E/g,Ry=/%60/g,yh=/%7B/g,Uy=/%7C/g,wh=/%7D/g,Fy=/%20/g;function tu(e){return encodeURI(""+e).replace(Uy,"|").replace(ky,"[").replace(Vy,"]")}function Ly(e){return tu(e).replace(yh,"{").replace(wh,"}").replace(bh,"^")}function su(e){return tu(e).replace(_h,"%2B").replace(Fy,"+").replace(vh,"%23").replace(Iy,"%26").replace(Ry,"`").replace(yh,"{").replace(wh,"}").replace(bh,"^")}function By(e){return su(e).replace(My,"%3D")}function $y(e){return tu(e).replace(vh,"%23").replace(Py,"%3F")}function jy(e){return e==null?"":$y(e).replace(Ay,"%2F")}function Un(e){try{return decodeURIComponent(""+e)}catch{({}).NODE_ENV!=="production"&&He(`Error decoding "${e}". Using original value`)}return""+e}const Hy=/\/$/,qy=e=>e.replace(Hy,"");function ru(e,t,s="/"){let i,n={},a="",u="";const c=t.indexOf("#");let h=t.indexOf("?");return c<h&&c>=0&&(h=-1),h>-1&&(i=t.slice(0,h),a=t.slice(h+1,c>-1?c:t.length),n=e(a)),c>-1&&(i=i||t.slice(0,c),u=t.slice(c,t.length)),i=Gy(i??t,s),{fullPath:i+(a&&"?")+a+u,path:i,query:n,hash:Un(u)}}function zy(e,t){const s=t.query?e(t.query):"";return t.path+(s&&"?")+s+(t.hash||"")}function Eh(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Ch(e,t,s){const i=t.matched.length-1,n=s.matched.length-1;return i>-1&&i===n&&Rr(t.matched[i],s.matched[n])&&Dh(t.params,s.params)&&e(t.query)===e(s.query)&&t.hash===s.hash}function Rr(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Dh(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(!Wy(e[s],t[s]))return!1;return!0}function Wy(e,t){return fs(e)?xh(e,t):fs(t)?xh(t,e):e===t}function xh(e,t){return fs(t)?e.length===t.length&&e.every((s,i)=>s===t[i]):e.length===1&&e[0]===t}function Gy(e,t){if(e.startsWith("/"))return e;if({}.NODE_ENV!=="production"&&!t.startsWith("/"))return He(`Cannot resolve a relative location without an absolute path. Trying to resolve "${e}" from "${t}". It should look like "/${t}".`),e;if(!e)return t;const s=t.split("/"),i=e.split("/"),n=i[i.length-1];(n===".."||n===".")&&i.push("");let a=s.length-1,u,c;for(u=0;u<i.length;u++)if(c=i[u],c!==".")if(c==="..")a>1&&a--;else break;return s.slice(0,a).join("/")+"/"+i.slice(u).join("/")}const Ur={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Mo;(function(e){e.pop="pop",e.push="push"})(Mo||(Mo={}));var Po;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Po||(Po={}));function Ky(e){if(!e)if(hr){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),qy(e)}const Qy=/^[^#]+#/;function Yy(e,t){return e.replace(Qy,"#")+t}function Zy(e,t){const s=document.documentElement.getBoundingClientRect(),i=e.getBoundingClientRect();return{behavior:t.behavior,left:i.left-s.left-(t.left||0),top:i.top-s.top-(t.top||0)}}const Gi=()=>({left:window.scrollX,top:window.scrollY});function Jy(e){let t;if("el"in e){const s=e.el,i=typeof s=="string"&&s.startsWith("#");if({}.NODE_ENV!=="production"&&typeof e.el=="string"&&(!i||!document.getElementById(e.el.slice(1))))try{const a=document.querySelector(e.el);if(i&&a){He(`The selector "${e.el}" should be passed as "el: document.querySelector('${e.el}')" because it starts with "#".`);return}}catch{He(`The selector "${e.el}" is invalid. If you are using an id selector, make sure to escape it. You can find more information about escaping characters in selectors at https://mathiasbynens.be/notes/css-escapes or use CSS.escape (https://developer.mozilla.org/en-US/docs/Web/API/CSS/escape).`);return}const n=typeof s=="string"?i?document.getElementById(s.slice(1)):document.querySelector(s):s;if(!n){({}).NODE_ENV!=="production"&&He(`Couldn't find element using selector "${e.el}" returned by scrollBehavior.`);return}t=Zy(n,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Sh(e,t){return(history.state?history.state.position-t:-1)+e}const nu=new Map;function Xy(e,t){nu.set(e,t)}function e0(e){const t=nu.get(e);return nu.delete(e),t}let t0=()=>location.protocol+"//"+location.host;function Oh(e,t){const{pathname:s,search:i,hash:n}=t,a=e.indexOf("#");if(a>-1){let c=n.includes(e.slice(a))?e.slice(a).length:1,h=n.slice(c);return h[0]!=="/"&&(h="/"+h),Eh(h,"")}return Eh(s,e)+i+n}function s0(e,t,s,i){let n=[],a=[],u=null;const c=({state:w})=>{const D=Oh(e,location),k=s.value,L=t.value;let re=0;if(w){if(s.value=D,t.value=w,u&&u===k){u=null;return}re=L?w.position-L.position:0}else i(D);n.forEach(I=>{I(s.value,k,{delta:re,type:Mo.pop,direction:re?re>0?Po.forward:Po.back:Po.unknown})})};function h(){u=s.value}function m(w){n.push(w);const D=()=>{const k=n.indexOf(w);k>-1&&n.splice(k,1)};return a.push(D),D}function p(){const{history:w}=window;w.state&&w.replaceState(Xe({},w.state,{scroll:Gi()}),"")}function v(){for(const w of a)w();a=[],window.removeEventListener("popstate",c),window.removeEventListener("beforeunload",p)}return window.addEventListener("popstate",c),window.addEventListener("beforeunload",p,{passive:!0}),{pauseListeners:h,listen:m,destroy:v}}function Th(e,t,s,i=!1,n=!1){return{back:e,current:t,forward:s,replaced:i,position:window.history.length,scroll:n?Gi():null}}function r0(e){const{history:t,location:s}=window,i={value:Oh(e,s)},n={value:t.state};n.value||a(i.value,{back:null,current:i.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function a(h,m,p){const v=e.indexOf("#"),w=v>-1?(s.host&&document.querySelector("base")?e:e.slice(v))+h:t0()+e+h;try{t[p?"replaceState":"pushState"](m,"",w),n.value=m}catch(D){({}).NODE_ENV!=="production"?He("Error with push/replace State",D):console.error(D),s[p?"replace":"assign"](w)}}function u(h,m){const p=Xe({},t.state,Th(n.value.back,h,n.value.forward,!0),m,{position:n.value.position});a(h,p,!0),i.value=h}function c(h,m){const p=Xe({},n.value,t.state,{forward:h,scroll:Gi()});({}).NODE_ENV!=="production"&&!t.state&&He(`history.state seems to have been manually replaced without preserving the necessary values. Make sure to preserve existing history state if you are manually calling history.replaceState:

history.replaceState(history.state, '', url)

You can find more information at https://router.vuejs.org/guide/migration/#Usage-of-history-state`),a(p.current,p,!0);const v=Xe({},Th(i.value,h,null),{position:p.position+1},m);a(h,v,!1),i.value=h}return{location:i,state:n,push:c,replace:u}}function n0(e){e=Ky(e);const t=r0(e),s=s0(e,t.state,t.location,t.replace);function i(a,u=!0){u||s.pauseListeners(),history.go(a)}const n=Xe({location:"",base:e,go:i,createHref:Yy.bind(null,e)},t,s);return Object.defineProperty(n,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(n,"state",{enumerable:!0,get:()=>t.state.value}),n}function Ki(e){return typeof e=="string"||e&&typeof e=="object"}function Nh(e){return typeof e=="string"||typeof e=="symbol"}const ou=Symbol({}.NODE_ENV!=="production"?"navigation failure":"");var Ih;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Ih||(Ih={}));const o0={1({location:e,currentLocation:t}){return`No match for
 ${JSON.stringify(e)}${t?`
while being at
`+JSON.stringify(t):""}`},2({from:e,to:t}){return`Redirected from "${e.fullPath}" to "${a0(t)}" via a navigation guard.`},4({from:e,to:t}){return`Navigation aborted from "${e.fullPath}" to "${t.fullPath}" via a navigation guard.`},8({from:e,to:t}){return`Navigation cancelled from "${e.fullPath}" to "${t.fullPath}" with a new navigation.`},16({from:e,to:t}){return`Avoided redundant navigation to current location: "${e.fullPath}".`}};function Fn(e,t){return{}.NODE_ENV!=="production"?Xe(new Error(o0[e](t)),{type:e,[ou]:!0},t):Xe(new Error,{type:e,[ou]:!0},t)}function pr(e,t){return e instanceof Error&&ou in e&&(t==null||!!(e.type&t))}const i0=["params","query","hash"];function a0(e){if(typeof e=="string")return e;if(e.path!=null)return e.path;const t={};for(const s of i0)s in e&&(t[s]=e[s]);return JSON.stringify(t,null,2)}const Ah="[^/]+?",l0={sensitive:!1,strict:!1,start:!0,end:!0},u0=/[.+*?^${}()[\]/\\]/g;function c0(e,t){const s=Xe({},l0,t),i=[];let n=s.start?"^":"";const a=[];for(const m of e){const p=m.length?[]:[90];s.strict&&!m.length&&(n+="/");for(let v=0;v<m.length;v++){const w=m[v];let D=40+(s.sensitive?.25:0);if(w.type===0)v||(n+="/"),n+=w.value.replace(u0,"\\$&"),D+=40;else if(w.type===1){const{value:k,repeatable:L,optional:re,regexp:I}=w;a.push({name:k,repeatable:L,optional:re});const ne=I||Ah;if(ne!==Ah){D+=10;try{new RegExp(`(${ne})`)}catch(ye){throw new Error(`Invalid custom RegExp for param "${k}" (${ne}): `+ye.message)}}let Q=L?`((?:${ne})(?:/(?:${ne}))*)`:`(${ne})`;v||(Q=re&&m.length<2?`(?:/${Q})`:"/"+Q),re&&(Q+="?"),n+=Q,D+=20,re&&(D+=-8),L&&(D+=-20),ne===".*"&&(D+=-50)}p.push(D)}i.push(p)}if(s.strict&&s.end){const m=i.length-1;i[m][i[m].length-1]+=.7000000000000001}s.strict||(n+="/?"),s.end?n+="$":s.strict&&!n.endsWith("/")&&(n+="(?:/|$)");const u=new RegExp(n,s.sensitive?"":"i");function c(m){const p=m.match(u),v={};if(!p)return null;for(let w=1;w<p.length;w++){const D=p[w]||"",k=a[w-1];v[k.name]=D&&k.repeatable?D.split("/"):D}return v}function h(m){let p="",v=!1;for(const w of e){(!v||!p.endsWith("/"))&&(p+="/"),v=!1;for(const D of w)if(D.type===0)p+=D.value;else if(D.type===1){const{value:k,repeatable:L,optional:re}=D,I=k in m?m[k]:"";if(fs(I)&&!L)throw new Error(`Provided param "${k}" is an array but it is not repeatable (* or + modifiers)`);const ne=fs(I)?I.join("/"):I;if(!ne)if(re)w.length<2&&(p.endsWith("/")?p=p.slice(0,-1):v=!0);else throw new Error(`Missing required param "${k}"`);p+=ne}}return p||"/"}return{re:u,score:i,keys:a,parse:c,stringify:h}}function d0(e,t){let s=0;for(;s<e.length&&s<t.length;){const i=t[s]-e[s];if(i)return i;s++}return e.length<t.length?e.length===1&&e[0]===40+40?-1:1:e.length>t.length?t.length===1&&t[0]===40+40?1:-1:0}function Mh(e,t){let s=0;const i=e.score,n=t.score;for(;s<i.length&&s<n.length;){const a=d0(i[s],n[s]);if(a)return a;s++}if(Math.abs(n.length-i.length)===1){if(Ph(i))return 1;if(Ph(n))return-1}return n.length-i.length}function Ph(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const f0={type:0,value:""},h0=/[a-zA-Z0-9_]/;function p0(e){if(!e)return[[]];if(e==="/")return[[f0]];if(!e.startsWith("/"))throw new Error({}.NODE_ENV!=="production"?`Route paths should start with a "/": "${e}" should be "/${e}".`:`Invalid path "${e}"`);function t(D){throw new Error(`ERR (${s})/"${m}": ${D}`)}let s=0,i=s;const n=[];let a;function u(){a&&n.push(a),a=[]}let c=0,h,m="",p="";function v(){m&&(s===0?a.push({type:0,value:m}):s===1||s===2||s===3?(a.length>1&&(h==="*"||h==="+")&&t(`A repeatable param (${m}) must be alone in its segment. eg: '/:ids+.`),a.push({type:1,value:m,regexp:p,repeatable:h==="*"||h==="+",optional:h==="*"||h==="?"})):t("Invalid state to consume buffer"),m="")}function w(){m+=h}for(;c<e.length;){if(h=e[c++],h==="\\"&&s!==2){i=s,s=4;continue}switch(s){case 0:h==="/"?(m&&v(),u()):h===":"?(v(),s=1):w();break;case 4:w(),s=i;break;case 1:h==="("?s=2:h0.test(h)?w():(v(),s=0,h!=="*"&&h!=="?"&&h!=="+"&&c--);break;case 2:h===")"?p[p.length-1]=="\\"?p=p.slice(0,-1)+h:s=3:p+=h;break;case 3:v(),s=0,h!=="*"&&h!=="?"&&h!=="+"&&c--,p="";break;default:t("Unknown state");break}}return s===2&&t(`Unfinished custom RegExp for param "${m}"`),v(),u(),n}function m0(e,t,s){const i=c0(p0(e.path),s);if({}.NODE_ENV!=="production"){const a=new Set;for(const u of i.keys)a.has(u.name)&&He(`Found duplicated params with name "${u.name}" for path "${e.path}". Only the last one will be available on "$route.params".`),a.add(u.name)}const n=Xe(i,{record:e,parent:t,children:[],alias:[]});return t&&!n.record.aliasOf==!t.record.aliasOf&&t.children.push(n),n}function g0(e,t){const s=[],i=new Map;t=Uh({strict:!1,end:!0,sensitive:!1},t);function n(v){return i.get(v)}function a(v,w,D){const k=!D,L=Vh(v);({}).NODE_ENV!=="production"&&y0(L,w),L.aliasOf=D&&D.record;const re=Uh(t,v),I=[L];if("alias"in v){const ye=typeof v.alias=="string"?[v.alias]:v.alias;for(const Z of ye)I.push(Vh(Xe({},L,{components:D?D.record.components:L.components,path:Z,aliasOf:D?D.record:L})))}let ne,Q;for(const ye of I){const{path:Z}=ye;if(w&&Z[0]!=="/"){const he=w.record.path,be=he[he.length-1]==="/"?"":"/";ye.path=w.record.path+(Z&&be+Z)}if({}.NODE_ENV!=="production"&&ye.path==="*")throw new Error(`Catch all routes ("*") must now be defined using a param with a custom regexp.
See more at https://router.vuejs.org/guide/migration/#Removed-star-or-catch-all-routes.`);if(ne=m0(ye,w,re),{}.NODE_ENV!=="production"&&w&&Z[0]==="/"&&E0(ne,w),D?(D.alias.push(ne),{}.NODE_ENV!=="production"&&b0(D,ne)):(Q=Q||ne,Q!==ne&&Q.alias.push(ne),k&&v.name&&!Rh(ne)&&({}.NODE_ENV!=="production"&&w0(v,w),u(v.name))),Fh(ne)&&h(ne),L.children){const he=L.children;for(let be=0;be<he.length;be++)a(he[be],ne,D&&D.children[be])}D=D||ne}return Q?()=>{u(Q)}:Ao}function u(v){if(Nh(v)){const w=i.get(v);w&&(i.delete(v),s.splice(s.indexOf(w),1),w.children.forEach(u),w.alias.forEach(u))}else{const w=s.indexOf(v);w>-1&&(s.splice(w,1),v.record.name&&i.delete(v.record.name),v.children.forEach(u),v.alias.forEach(u))}}function c(){return s}function h(v){const w=C0(v,s);s.splice(w,0,v),v.record.name&&!Rh(v)&&i.set(v.record.name,v)}function m(v,w){let D,k={},L,re;if("name"in v&&v.name){if(D=i.get(v.name),!D)throw Fn(1,{location:v});if({}.NODE_ENV!=="production"){const Q=Object.keys(v.params||{}).filter(ye=>!D.keys.find(Z=>Z.name===ye));Q.length&&He(`Discarded invalid param(s) "${Q.join('", "')}" when navigating. See https://github.com/vuejs/router/blob/main/packages/router/CHANGELOG.md#414-2022-08-22 for more details.`)}re=D.record.name,k=Xe(kh(w.params,D.keys.filter(Q=>!Q.optional).concat(D.parent?D.parent.keys.filter(Q=>Q.optional):[]).map(Q=>Q.name)),v.params&&kh(v.params,D.keys.map(Q=>Q.name))),L=D.stringify(k)}else if(v.path!=null)L=v.path,{}.NODE_ENV!=="production"&&!L.startsWith("/")&&He(`The Matcher cannot resolve relative paths but received "${L}". Unless you directly called \`matcher.resolve("${L}")\`, this is probably a bug in vue-router. Please open an issue at https://github.com/vuejs/router/issues/new/choose.`),D=s.find(Q=>Q.re.test(L)),D&&(k=D.parse(L),re=D.record.name);else{if(D=w.name?i.get(w.name):s.find(Q=>Q.re.test(w.path)),!D)throw Fn(1,{location:v,currentLocation:w});re=D.record.name,k=Xe({},w.params,v.params),L=D.stringify(k)}const I=[];let ne=D;for(;ne;)I.unshift(ne.record),ne=ne.parent;return{name:re,path:L,params:k,matched:I,meta:_0(I)}}e.forEach(v=>a(v));function p(){s.length=0,i.clear()}return{addRoute:a,resolve:m,removeRoute:u,clearRoutes:p,getRoutes:c,getRecordMatcher:n}}function kh(e,t){const s={};for(const i of t)i in e&&(s[i]=e[i]);return s}function Vh(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:v0(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function v0(e){const t={},s=e.props||!1;if("component"in e)t.default=s;else for(const i in e.components)t[i]=typeof s=="object"?s[i]:s;return t}function Rh(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function _0(e){return e.reduce((t,s)=>Xe(t,s.meta),{})}function Uh(e,t){const s={};for(const i in e)s[i]=i in t?t[i]:e[i];return s}function iu(e,t){return e.name===t.name&&e.optional===t.optional&&e.repeatable===t.repeatable}function b0(e,t){for(const s of e.keys)if(!s.optional&&!t.keys.find(iu.bind(null,s)))return He(`Alias "${t.record.path}" and the original record: "${e.record.path}" must have the exact same param named "${s.name}"`);for(const s of t.keys)if(!s.optional&&!e.keys.find(iu.bind(null,s)))return He(`Alias "${t.record.path}" and the original record: "${e.record.path}" must have the exact same param named "${s.name}"`)}function y0(e,t){t&&t.record.name&&!e.name&&!e.path&&He(`The route named "${String(t.record.name)}" has a child without a name and an empty path. Using that name won't render the empty path child so you probably want to move the name to the child instead. If this is intentional, add a name to the child route to remove the warning.`)}function w0(e,t){for(let s=t;s;s=s.parent)if(s.record.name===e.name)throw new Error(`A route named "${String(e.name)}" has been added as a ${t===s?"child":"descendant"} of a route with the same name. Route names must be unique and a nested route cannot use the same name as an ancestor.`)}function E0(e,t){for(const s of t.keys)if(!e.keys.find(iu.bind(null,s)))return He(`Absolute path "${e.record.path}" must have the exact same param named "${s.name}" as its parent "${t.record.path}".`)}function C0(e,t){let s=0,i=t.length;for(;s!==i;){const a=s+i>>1;Mh(e,t[a])<0?i=a:s=a+1}const n=D0(e);return n&&(i=t.lastIndexOf(n,i-1),{}.NODE_ENV!=="production"&&i<0&&He(`Finding ancestor route "${n.record.path}" failed for "${e.record.path}"`)),i}function D0(e){let t=e;for(;t=t.parent;)if(Fh(t)&&Mh(e,t)===0)return t}function Fh({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function x0(e){const t={};if(e===""||e==="?")return t;const i=(e[0]==="?"?e.slice(1):e).split("&");for(let n=0;n<i.length;++n){const a=i[n].replace(_h," "),u=a.indexOf("="),c=Un(u<0?a:a.slice(0,u)),h=u<0?null:Un(a.slice(u+1));if(c in t){let m=t[c];fs(m)||(m=t[c]=[m]),m.push(h)}else t[c]=h}return t}function Lh(e){let t="";for(let s in e){const i=e[s];if(s=By(s),i==null){i!==void 0&&(t+=(t.length?"&":"")+s);continue}(fs(i)?i.map(a=>a&&su(a)):[i&&su(i)]).forEach(a=>{a!==void 0&&(t+=(t.length?"&":"")+s,a!=null&&(t+="="+a))})}return t}function S0(e){const t={};for(const s in e){const i=e[s];i!==void 0&&(t[s]=fs(i)?i.map(n=>n==null?null:""+n):i==null?i:""+i)}return t}const O0=Symbol({}.NODE_ENV!=="production"?"router view location matched":""),Bh=Symbol({}.NODE_ENV!=="production"?"router view depth":""),Qi=Symbol({}.NODE_ENV!=="production"?"router":""),au=Symbol({}.NODE_ENV!=="production"?"route location":""),lu=Symbol({}.NODE_ENV!=="production"?"router view location":"");function ko(){let e=[];function t(i){return e.push(i),()=>{const n=e.indexOf(i);n>-1&&e.splice(n,1)}}function s(){e=[]}return{add:t,list:()=>e.slice(),reset:s}}function Fr(e,t,s,i,n,a=u=>u()){const u=i&&(i.enterCallbacks[n]=i.enterCallbacks[n]||[]);return()=>new Promise((c,h)=>{const m=w=>{w===!1?h(Fn(4,{from:s,to:t})):w instanceof Error?h(w):Ki(w)?h(Fn(2,{from:t,to:w})):(u&&i.enterCallbacks[n]===u&&typeof w=="function"&&u.push(w),c())},p=a(()=>e.call(i&&i.instances[n],t,s,{}.NODE_ENV!=="production"?T0(m,t,s):m));let v=Promise.resolve(p);if(e.length<3&&(v=v.then(m)),{}.NODE_ENV!=="production"&&e.length>2){const w=`The "next" callback was never called inside of ${e.name?'"'+e.name+'"':""}:
${e.toString()}
. If you are returning a value instead of calling "next", make sure to remove the "next" parameter from your function.`;if(typeof p=="object"&&"then"in p)v=v.then(D=>m._called?D:(He(w),Promise.reject(new Error("Invalid navigation guard"))));else if(p!==void 0&&!m._called){He(w),h(new Error("Invalid navigation guard"));return}}v.catch(w=>h(w))})}function T0(e,t,s){let i=0;return function(){i++===1&&He(`The "next" callback was called more than once in one navigation guard when going from "${s.fullPath}" to "${t.fullPath}". It should be called exactly one time in each navigation guard. This will fail in production.`),e._called=!0,i===1&&e.apply(null,arguments)}}function uu(e,t,s,i,n=a=>a()){const a=[];for(const u of e){({}).NODE_ENV!=="production"&&!u.components&&!u.children.length&&He(`Record with path "${u.path}" is either missing a "component(s)" or "children" property.`);for(const c in u.components){let h=u.components[c];if({}.NODE_ENV!=="production"){if(!h||typeof h!="object"&&typeof h!="function")throw He(`Component "${c}" in record with path "${u.path}" is not a valid component. Received "${String(h)}".`),new Error("Invalid route component");if("then"in h){He(`Component "${c}" in record with path "${u.path}" is a Promise instead of a function that returns a Promise. Did you write "import('./MyPage.vue')" instead of "() => import('./MyPage.vue')" ? This will break in production if not fixed.`);const m=h;h=()=>m}else h.__asyncLoader&&!h.__warnedDefineAsync&&(h.__warnedDefineAsync=!0,He(`Component "${c}" in record with path "${u.path}" is defined using "defineAsyncComponent()". Write "() => import('./MyPage.vue')" instead of "defineAsyncComponent(() => import('./MyPage.vue'))".`))}if(!(t!=="beforeRouteEnter"&&!u.instances[c]))if(gh(h)){const p=(h.__vccOpts||h)[t];p&&a.push(Fr(p,s,i,u,c,n))}else{let m=h();({}).NODE_ENV!=="production"&&!("catch"in m)&&(He(`Component "${c}" in record with path "${u.path}" is a function that does not return a Promise. If you were passing a functional component, make sure to add a "displayName" to the component. This will break in production if not fixed.`),m=Promise.resolve(m)),a.push(()=>m.then(p=>{if(!p)throw new Error(`Couldn't resolve component "${c}" at "${u.path}"`);const v=Ny(p)?p.default:p;u.mods[c]=p,u.components[c]=v;const D=(v.__vccOpts||v)[t];return D&&Fr(D,s,i,u,c,n)()}))}}}return a}function $h(e){const t=Vs(Qi),s=Vs(au);let i=!1,n=null;const a=Us(()=>{const p=Tr(e.to);return{}.NODE_ENV!=="production"&&(!i||p!==n)&&(Ki(p)||(i?He(`Invalid value for prop "to" in useLink()
- to:`,p,`
- previous to:`,n,`
- props:`,e):He(`Invalid value for prop "to" in useLink()
- to:`,p,`
- props:`,e)),n=p,i=!0),t.resolve(p)}),u=Us(()=>{const{matched:p}=a.value,{length:v}=p,w=p[v-1],D=s.matched;if(!w||!D.length)return-1;const k=D.findIndex(Rr.bind(null,w));if(k>-1)return k;const L=jh(p[v-2]);return v>1&&jh(w)===L&&D[D.length-1].path!==L?D.findIndex(Rr.bind(null,p[v-2])):k}),c=Us(()=>u.value>-1&&M0(s.params,a.value.params)),h=Us(()=>u.value>-1&&u.value===s.matched.length-1&&Dh(s.params,a.value.params));function m(p={}){if(A0(p)){const v=t[Tr(e.replace)?"replace":"push"](Tr(e.to)).catch(Ao);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>v),v}return Promise.resolve()}if({}.NODE_ENV!=="production"&&hr){const p=Vi();if(p){const v={route:a.value,isActive:c.value,isExactActive:h.value,error:null};p.__vrl_devtools=p.__vrl_devtools||[],p.__vrl_devtools.push(v),W_(()=>{v.route=a.value,v.isActive=c.value,v.isExactActive=h.value,v.error=Ki(Tr(e.to))?null:'Invalid "to" value'},{flush:"post"})}}return{route:a,href:Us(()=>a.value.href),isActive:c,isExactActive:h,navigate:m}}function N0(e){return e.length===1?e[0]:e}const I0=Fd({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:$h,setup(e,{slots:t}){const s=fi($h(e)),{options:i}=Vs(Qi),n=Us(()=>({[Hh(e.activeClass,i.linkActiveClass,"router-link-active")]:s.isActive,[Hh(e.exactActiveClass,i.linkExactActiveClass,"router-link-exact-active")]:s.isExactActive}));return()=>{const a=t.default&&N0(t.default(s));return e.custom?a:jl("a",{"aria-current":s.isExactActive?e.ariaCurrentValue:null,href:s.href,onClick:s.navigate,class:n.value},a)}}});function A0(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function M0(e,t){for(const s in t){const i=t[s],n=e[s];if(typeof i=="string"){if(i!==n)return!1}else if(!fs(n)||n.length!==i.length||i.some((a,u)=>a!==n[u]))return!1}return!0}function jh(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Hh=(e,t,s)=>e??t??s,P0=Fd({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:s}){({}).NODE_ENV!=="production"&&V0();const i=Vs(lu),n=Us(()=>e.route||i.value),a=Vs(Bh,0),u=Us(()=>{let m=Tr(a);const{matched:p}=n.value;let v;for(;(v=p[m])&&!v.components;)m++;return m}),c=Us(()=>n.value.matched[u.value]);Ii(Bh,Us(()=>u.value+1)),Ii(O0,c),Ii(lu,n);const h=ld();return Pn(()=>[h.value,c.value,e.name],([m,p,v],[w,D,k])=>{p&&(p.instances[v]=m,D&&D!==p&&m&&m===w&&(p.leaveGuards.size||(p.leaveGuards=D.leaveGuards),p.updateGuards.size||(p.updateGuards=D.updateGuards))),m&&p&&(!D||!Rr(p,D)||!w)&&(p.enterCallbacks[v]||[]).forEach(L=>L(m))},{flush:"post"}),()=>{const m=n.value,p=e.name,v=c.value,w=v&&v.components[p];if(!w)return qh(s.default,{Component:w,route:m});const D=v.props[p],k=D?D===!0?m.params:typeof D=="function"?D(m):D:null,re=jl(w,Xe({},k,t,{onVnodeUnmounted:I=>{I.component.isUnmounted&&(v.instances[p]=null)},ref:h}));if({}.NODE_ENV!=="production"&&hr&&re.ref){const I={depth:u.value,name:v.name,path:v.path,meta:v.meta};(fs(re.ref)?re.ref.map(Q=>Q.i):[re.ref.i]).forEach(Q=>{Q.__vrv_devtools=I})}return qh(s.default,{Component:re,route:m})||re}}});function qh(e,t){if(!e)return null;const s=e(t);return s.length===1?s[0]:s}const k0=P0;function V0(){const e=Vi(),t=e.parent&&e.parent.type.name,s=e.parent&&e.parent.subTree&&e.parent.subTree.type;if(t&&(t==="KeepAlive"||t.includes("Transition"))&&typeof s=="object"&&s.name==="RouterView"){const i=t==="KeepAlive"?"keep-alive":"transition";He(`<router-view> can no longer be used directly inside <transition> or <keep-alive>.
Use slot props instead:

<router-view v-slot="{ Component }">
  <${i}>
    <component :is="Component" />
  </${i}>
</router-view>`)}}function Vo(e,t){const s=Xe({},e,{matched:e.matched.map(i=>W0(i,["instances","children","aliasOf"]))});return{_custom:{type:null,readOnly:!0,display:e.fullPath,tooltip:t,value:s}}}function Yi(e){return{_custom:{display:e}}}let R0=0;function U0(e,t,s){if(t.__hasDevtools)return;t.__hasDevtools=!0;const i=R0++;Yl({id:"org.vuejs.router"+(i?"."+i:""),label:"Vue Router",packageName:"vue-router",homepage:"https://router.vuejs.org",logo:"https://router.vuejs.org/logo.png",componentStateTypes:["Routing"],app:e},n=>{typeof n.now!="function"&&console.warn("[Vue Router]: You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),n.on.inspectComponent((p,v)=>{p.instanceData&&p.instanceData.state.push({type:"Routing",key:"$route",editable:!1,value:Vo(t.currentRoute.value,"Current Route")})}),n.on.visitComponentTree(({treeNode:p,componentInstance:v})=>{if(v.__vrv_devtools){const w=v.__vrv_devtools;p.tags.push({label:(w.name?`${w.name.toString()}: `:"")+w.path,textColor:0,tooltip:"This component is rendered by &lt;router-view&gt;",backgroundColor:zh})}fs(v.__vrl_devtools)&&(v.__devtoolsApi=n,v.__vrl_devtools.forEach(w=>{let D=w.route.path,k=Kh,L="",re=0;w.error?(D=w.error,k=j0,re=H0):w.isExactActive?(k=Gh,L="This is exactly active"):w.isActive&&(k=Wh,L="This link is active"),p.tags.push({label:D,textColor:re,tooltip:L,backgroundColor:k})}))}),Pn(t.currentRoute,()=>{h(),n.notifyComponentUpdate(),n.sendInspectorTree(c),n.sendInspectorState(c)});const a="router:navigations:"+i;n.addTimelineLayer({id:a,label:`Router${i?" "+i:""} Navigations`,color:4237508}),t.onError((p,v)=>{n.addTimelineEvent({layerId:a,event:{title:"Error during Navigation",subtitle:v.fullPath,logType:"error",time:n.now(),data:{error:p},groupId:v.meta.__navigationId}})});let u=0;t.beforeEach((p,v)=>{const w={guard:Yi("beforeEach"),from:Vo(v,"Current Location during this navigation"),to:Vo(p,"Target location")};Object.defineProperty(p.meta,"__navigationId",{value:u++}),n.addTimelineEvent({layerId:a,event:{time:n.now(),title:"Start of navigation",subtitle:p.fullPath,data:w,groupId:p.meta.__navigationId}})}),t.afterEach((p,v,w)=>{const D={guard:Yi("afterEach")};w?(D.failure={_custom:{type:Error,readOnly:!0,display:w?w.message:"",tooltip:"Navigation Failure",value:w}},D.status=Yi("❌")):D.status=Yi("✅"),D.from=Vo(v,"Current Location during this navigation"),D.to=Vo(p,"Target location"),n.addTimelineEvent({layerId:a,event:{title:"End of navigation",subtitle:p.fullPath,time:n.now(),data:D,logType:w?"warning":"default",groupId:p.meta.__navigationId}})});const c="router-inspector:"+i;n.addInspector({id:c,label:"Routes"+(i?" "+i:""),icon:"book",treeFilterPlaceholder:"Search routes"});function h(){if(!m)return;const p=m;let v=s.getRoutes().filter(w=>!w.parent||!w.parent.record.components);v.forEach(Zh),p.filter&&(v=v.filter(w=>cu(w,p.filter.toLowerCase()))),v.forEach(w=>Yh(w,t.currentRoute.value)),p.rootNodes=v.map(Qh)}let m;n.on.getInspectorTree(p=>{m=p,p.app===e&&p.inspectorId===c&&h()}),n.on.getInspectorState(p=>{if(p.app===e&&p.inspectorId===c){const w=s.getRoutes().find(D=>D.record.__vd_id===p.nodeId);w&&(p.state={options:L0(w)})}}),n.sendInspectorTree(c),n.sendInspectorState(c)})}function F0(e){return e.optional?e.repeatable?"*":"?":e.repeatable?"+":""}function L0(e){const{record:t}=e,s=[{editable:!1,key:"path",value:t.path}];return t.name!=null&&s.push({editable:!1,key:"name",value:t.name}),s.push({editable:!1,key:"regexp",value:e.re}),e.keys.length&&s.push({editable:!1,key:"keys",value:{_custom:{type:null,readOnly:!0,display:e.keys.map(i=>`${i.name}${F0(i)}`).join(" "),tooltip:"Param keys",value:e.keys}}}),t.redirect!=null&&s.push({editable:!1,key:"redirect",value:t.redirect}),e.alias.length&&s.push({editable:!1,key:"aliases",value:e.alias.map(i=>i.record.path)}),Object.keys(e.record.meta).length&&s.push({editable:!1,key:"meta",value:e.record.meta}),s.push({key:"score",editable:!1,value:{_custom:{type:null,readOnly:!0,display:e.score.map(i=>i.join(", ")).join(" | "),tooltip:"Score used to sort routes",value:e.score}}}),s}const zh=15485081,Wh=2450411,Gh=8702998,B0=2282478,Kh=16486972,$0=6710886,j0=16704226,H0=12131356;function Qh(e){const t=[],{record:s}=e;s.name!=null&&t.push({label:String(s.name),textColor:0,backgroundColor:B0}),s.aliasOf&&t.push({label:"alias",textColor:0,backgroundColor:Kh}),e.__vd_match&&t.push({label:"matches",textColor:0,backgroundColor:zh}),e.__vd_exactActive&&t.push({label:"exact",textColor:0,backgroundColor:Gh}),e.__vd_active&&t.push({label:"active",textColor:0,backgroundColor:Wh}),s.redirect&&t.push({label:typeof s.redirect=="string"?`redirect: ${s.redirect}`:"redirects",textColor:16777215,backgroundColor:$0});let i=s.__vd_id;return i==null&&(i=String(q0++),s.__vd_id=i),{id:i,label:s.path,tags:t,children:e.children.map(Qh)}}let q0=0;const z0=/^\/(.*)\/([a-z]*)$/;function Yh(e,t){const s=t.matched.length&&Rr(t.matched[t.matched.length-1],e.record);e.__vd_exactActive=e.__vd_active=s,s||(e.__vd_active=t.matched.some(i=>Rr(i,e.record))),e.children.forEach(i=>Yh(i,t))}function Zh(e){e.__vd_match=!1,e.children.forEach(Zh)}function cu(e,t){const s=String(e.re).match(z0);if(e.__vd_match=!1,!s||s.length<3)return!1;if(new RegExp(s[1].replace(/\$$/,""),s[2]).test(t))return e.children.forEach(u=>cu(u,t)),e.record.path!=="/"||t==="/"?(e.__vd_match=e.re.test(t),!0):!1;const n=e.record.path.toLowerCase(),a=Un(n);return!t.startsWith("/")&&(a.includes(t)||n.includes(t))||a.startsWith(t)||n.startsWith(t)||e.record.name&&String(e.record.name).includes(t)?!0:e.children.some(u=>cu(u,t))}function W0(e,t){const s={};for(const i in e)t.includes(i)||(s[i]=e[i]);return s}function G0(e){const t=g0(e.routes,e),s=e.parseQuery||x0,i=e.stringifyQuery||Lh,n=e.history;if({}.NODE_ENV!=="production"&&!n)throw new Error('Provide the "history" option when calling "createRouter()": https://router.vuejs.org/api/interfaces/RouterOptions.html#history');const a=ko(),u=ko(),c=ko(),h=Cv(Ur);let m=Ur;hr&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const p=eu.bind(null,R=>""+R),v=eu.bind(null,jy),w=eu.bind(null,Un);function D(R,le){let ie,me;return Nh(R)?(ie=t.getRecordMatcher(R),{}.NODE_ENV!=="production"&&!ie&&He(`Parent route "${String(R)}" not found when adding child route`,le),me=le):me=R,t.addRoute(me,ie)}function k(R){const le=t.getRecordMatcher(R);le?t.removeRoute(le):{}.NODE_ENV!=="production"&&He(`Cannot remove non-existent route "${String(R)}"`)}function L(){return t.getRoutes().map(R=>R.record)}function re(R){return!!t.getRecordMatcher(R)}function I(R,le){if(le=Xe({},le||h.value),typeof R=="string"){const y=ru(s,R,le.path),C=t.resolve({path:y.path},le),P=n.createHref(y.fullPath);return{}.NODE_ENV!=="production"&&(P.startsWith("//")?He(`Location "${R}" resolved to "${P}". A resolved location cannot start with multiple slashes.`):C.matched.length||He(`No match found for location with path "${R}"`)),Xe(y,C,{params:w(C.params),hash:Un(y.hash),redirectedFrom:void 0,href:P})}if({}.NODE_ENV!=="production"&&!Ki(R))return He(`router.resolve() was passed an invalid location. This will fail in production.
- Location:`,R),I({});let ie;if(R.path!=null)({}).NODE_ENV!=="production"&&"params"in R&&!("name"in R)&&Object.keys(R.params).length&&He(`Path "${R.path}" was passed with params but they will be ignored. Use a named route alongside params instead.`),ie=Xe({},R,{path:ru(s,R.path,le.path).path});else{const y=Xe({},R.params);for(const C in y)y[C]==null&&delete y[C];ie=Xe({},R,{params:v(y)}),le.params=v(le.params)}const me=t.resolve(ie,le),ke=R.hash||"";({}).NODE_ENV!=="production"&&ke&&!ke.startsWith("#")&&He(`A \`hash\` should always start with the character "#". Replace "${ke}" with "#${ke}".`),me.params=p(w(me.params));const ot=zy(i,Xe({},R,{hash:Ly(ke),path:me.path})),Ve=n.createHref(ot);return{}.NODE_ENV!=="production"&&(Ve.startsWith("//")?He(`Location "${R}" resolved to "${Ve}". A resolved location cannot start with multiple slashes.`):me.matched.length||He(`No match found for location with path "${R.path!=null?R.path:R}"`)),Xe({fullPath:ot,hash:ke,query:i===Lh?S0(R.query):R.query||{}},me,{redirectedFrom:void 0,href:Ve})}function ne(R){return typeof R=="string"?ru(s,R,h.value.path):Xe({},R)}function Q(R,le){if(m!==R)return Fn(8,{from:le,to:R})}function ye(R){return be(R)}function Z(R){return ye(Xe(ne(R),{replace:!0}))}function he(R){const le=R.matched[R.matched.length-1];if(le&&le.redirect){const{redirect:ie}=le;let me=typeof ie=="function"?ie(R):ie;if(typeof me=="string"&&(me=me.includes("?")||me.includes("#")?me=ne(me):{path:me},me.params={}),{}.NODE_ENV!=="production"&&me.path==null&&!("name"in me))throw He(`Invalid redirect found:
${JSON.stringify(me,null,2)}
 when navigating to "${R.fullPath}". A redirect must contain a name or path. This will break in production.`),new Error("Invalid redirect");return Xe({query:R.query,hash:R.hash,params:me.path!=null?{}:R.params},me)}}function be(R,le){const ie=m=I(R),me=h.value,ke=R.state,ot=R.force,Ve=R.replace===!0,y=he(ie);if(y)return be(Xe(ne(y),{state:typeof y=="object"?Xe({},ke,y.state):ke,force:ot,replace:Ve}),le||ie);const C=ie;C.redirectedFrom=le;let P;return!ot&&Ch(i,me,ie)&&(P=Fn(16,{to:C,from:me}),bt(me,me,!0,!1)),(P?Promise.resolve(P):ae(C,me)).catch(F=>pr(F)?pr(F,2)?F:es(F):we(F,C,me)).then(F=>{if(F){if(pr(F,2))return{}.NODE_ENV!=="production"&&Ch(i,I(F.to),C)&&le&&(le._count=le._count?le._count+1:1)>30?(He(`Detected a possibly infinite redirection in a navigation guard when going from "${me.fullPath}" to "${C.fullPath}". Aborting to avoid a Stack Overflow.
 Are you always returning a new location within a navigation guard? That would lead to this error. Only return when redirecting or aborting, that should fix this. This might break in production if not fixed.`),Promise.reject(new Error("Infinite redirect in navigation guard"))):be(Xe({replace:Ve},ne(F.to),{state:typeof F.to=="object"?Xe({},ke,F.to.state):ke,force:ot}),le||C)}else F=te(C,me,!0,Ve,ke);return V(C,me,F),F})}function Ae(R,le){const ie=Q(R,le);return ie?Promise.reject(ie):Promise.resolve()}function ue(R){const le=hs.values().next().value;return le&&typeof le.runWithContext=="function"?le.runWithContext(R):R()}function ae(R,le){let ie;const[me,ke,ot]=K0(R,le);ie=uu(me.reverse(),"beforeRouteLeave",R,le);for(const y of me)y.leaveGuards.forEach(C=>{ie.push(Fr(C,R,le))});const Ve=Ae.bind(null,R,le);return ie.push(Ve),ps(ie).then(()=>{ie=[];for(const y of a.list())ie.push(Fr(y,R,le));return ie.push(Ve),ps(ie)}).then(()=>{ie=uu(ke,"beforeRouteUpdate",R,le);for(const y of ke)y.updateGuards.forEach(C=>{ie.push(Fr(C,R,le))});return ie.push(Ve),ps(ie)}).then(()=>{ie=[];for(const y of ot)if(y.beforeEnter)if(fs(y.beforeEnter))for(const C of y.beforeEnter)ie.push(Fr(C,R,le));else ie.push(Fr(y.beforeEnter,R,le));return ie.push(Ve),ps(ie)}).then(()=>(R.matched.forEach(y=>y.enterCallbacks={}),ie=uu(ot,"beforeRouteEnter",R,le,ue),ie.push(Ve),ps(ie))).then(()=>{ie=[];for(const y of u.list())ie.push(Fr(y,R,le));return ie.push(Ve),ps(ie)}).catch(y=>pr(y,8)?y:Promise.reject(y))}function V(R,le,ie){c.list().forEach(me=>ue(()=>me(R,le,ie)))}function te(R,le,ie,me,ke){const ot=Q(R,le);if(ot)return ot;const Ve=le===Ur,y=hr?history.state:{};ie&&(me||Ve?n.replace(R.fullPath,Xe({scroll:Ve&&y&&y.scroll},ke)):n.push(R.fullPath,ke)),h.value=R,bt(R,le,ie,Ve),es()}let Ke;function vt(){Ke||(Ke=n.listen((R,le,ie)=>{if(!zt.listening)return;const me=I(R),ke=he(me);if(ke){be(Xe(ke,{replace:!0,force:!0}),me).catch(Ao);return}m=me;const ot=h.value;hr&&Xy(Sh(ot.fullPath,ie.delta),Gi()),ae(me,ot).catch(Ve=>pr(Ve,12)?Ve:pr(Ve,2)?(be(Xe(ne(Ve.to),{force:!0}),me).then(y=>{pr(y,20)&&!ie.delta&&ie.type===Mo.pop&&n.go(-1,!1)}).catch(Ao),Promise.reject()):(ie.delta&&n.go(-ie.delta,!1),we(Ve,me,ot))).then(Ve=>{Ve=Ve||te(me,ot,!1),Ve&&(ie.delta&&!pr(Ve,8)?n.go(-ie.delta,!1):ie.type===Mo.pop&&pr(Ve,20)&&n.go(-1,!1)),V(me,ot,Ve)}).catch(Ao)}))}let mt=ko(),ft=ko(),Se;function we(R,le,ie){es(R);const me=ft.list();return me.length?me.forEach(ke=>ke(R,le,ie)):({}.NODE_ENV!=="production"&&He("uncaught error during route navigation:"),console.error(R)),Promise.reject(R)}function Ft(){return Se&&h.value!==Ur?Promise.resolve():new Promise((R,le)=>{mt.add([R,le])})}function es(R){return Se||(Se=!R,vt(),mt.list().forEach(([le,ie])=>R?ie(R):le()),mt.reset()),R}function bt(R,le,ie,me){const{scrollBehavior:ke}=e;if(!hr||!ke)return Promise.resolve();const ot=!ie&&e0(Sh(R.fullPath,0))||(me||!ie)&&history.state&&history.state.scroll||null;return pl().then(()=>ke(R,le,ot)).then(Ve=>Ve&&Jy(Ve)).catch(Ve=>we(Ve,R,le))}const de=R=>n.go(R);let We;const hs=new Set,zt={currentRoute:h,listening:!0,addRoute:D,removeRoute:k,clearRoutes:t.clearRoutes,hasRoute:re,getRoutes:L,resolve:I,options:e,push:ye,replace:Z,go:de,back:()=>de(-1),forward:()=>de(1),beforeEach:a.add,beforeResolve:u.add,afterEach:c.add,onError:ft.add,isReady:Ft,install(R){const le=this;R.component("RouterLink",I0),R.component("RouterView",k0),R.config.globalProperties.$router=le,Object.defineProperty(R.config.globalProperties,"$route",{enumerable:!0,get:()=>Tr(h)}),hr&&!We&&h.value===Ur&&(We=!0,ye(n.location).catch(ke=>{({}).NODE_ENV!=="production"&&He("Unexpected error when starting the router:",ke)}));const ie={};for(const ke in Ur)Object.defineProperty(ie,ke,{get:()=>h.value[ke],enumerable:!0});R.provide(Qi,le),R.provide(au,id(ie)),R.provide(lu,h);const me=R.unmount;hs.add(R),R.unmount=function(){hs.delete(R),hs.size<1&&(m=Ur,Ke&&Ke(),Ke=null,h.value=Ur,We=!1,Se=!1),me()},{}.NODE_ENV!=="production"&&hr&&U0(R,le,t)}};function ps(R){return R.reduce((le,ie)=>le.then(()=>ue(ie)),Promise.resolve())}return zt}function K0(e,t){const s=[],i=[],n=[],a=Math.max(t.matched.length,e.matched.length);for(let u=0;u<a;u++){const c=t.matched[u];c&&(e.matched.find(m=>Rr(m,c))?i.push(c):s.push(c));const h=e.matched[u];h&&(t.matched.find(m=>Rr(m,h))||n.push(h))}return[s,i,n]}function Zi(){return Vs(Qi)}function Jh(e){return Vs(au)}const Q0="data:image/svg+xml;base64,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";var Ro=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},Ji={exports:{}};/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */Ji.exports,function(e,t){(function(){var s,i="4.17.21",n=200,a="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",u="Expected a function",c="Invalid `variable` option passed into `_.template`",h="__lodash_hash_undefined__",m=500,p="__lodash_placeholder__",v=1,w=2,D=4,k=1,L=2,re=1,I=2,ne=4,Q=8,ye=16,Z=32,he=64,be=128,Ae=256,ue=512,ae=30,V="...",te=800,Ke=16,vt=1,mt=2,ft=3,Se=1/0,we=9007199254740991,Ft=17976931348623157e292,es=0/0,bt=**********,de=bt-1,We=bt>>>1,hs=[["ary",be],["bind",re],["bindKey",I],["curry",Q],["curryRight",ye],["flip",ue],["partial",Z],["partialRight",he],["rearg",Ae]],zt="[object Arguments]",ps="[object Array]",R="[object AsyncFunction]",le="[object Boolean]",ie="[object Date]",me="[object DOMException]",ke="[object Error]",ot="[object Function]",Ve="[object GeneratorFunction]",y="[object Map]",C="[object Number]",P="[object Null]",F="[object Object]",H="[object Promise]",W="[object Proxy]",se="[object RegExp]",K="[object Set]",J="[object String]",G="[object Symbol]",Ee="[object Undefined]",oe="[object WeakMap]",_e="[object WeakSet]",Ce="[object ArrayBuffer]",Le="[object DataView]",et="[object Float32Array]",Ze="[object Float64Array]",Lt="[object Int8Array]",St="[object Int16Array]",ts="[object Int32Array]",Bt="[object Uint8Array]",gr="[object Uint8ClampedArray]",Bn="[object Uint16Array]",It="[object Uint32Array]",Es=/\b__p \+= '';/g,na=/\b(__p \+=) '' \+/g,GN=/(__e\(.*?\)|\b__t\)) \+\n'';/g,cp=/&(?:amp|lt|gt|quot|#39);/g,dp=/[&<>"']/g,KN=RegExp(cp.source),QN=RegExp(dp.source),YN=/<%-([\s\S]+?)%>/g,ZN=/<%([\s\S]+?)%>/g,fp=/<%=([\s\S]+?)%>/g,JN=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,XN=/^\w*$/,eI=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,wu=/[\\^$.*+?()[\]{}|]/g,tI=RegExp(wu.source),Eu=/^\s+/,sI=/\s/,rI=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,nI=/\{\n\/\* \[wrapped with (.+)\] \*/,oI=/,? & /,iI=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,aI=/[()=,{}\[\]\/\s]/,lI=/\\(\\)?/g,uI=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,hp=/\w*$/,cI=/^[-+]0x[0-9a-f]+$/i,dI=/^0b[01]+$/i,fI=/^\[object .+?Constructor\]$/,hI=/^0o[0-7]+$/i,pI=/^(?:0|[1-9]\d*)$/,mI=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,oa=/($^)/,gI=/['\n\r\u2028\u2029\\]/g,ia="\\ud800-\\udfff",vI="\\u0300-\\u036f",_I="\\ufe20-\\ufe2f",bI="\\u20d0-\\u20ff",pp=vI+_I+bI,mp="\\u2700-\\u27bf",gp="a-z\\xdf-\\xf6\\xf8-\\xff",yI="\\xac\\xb1\\xd7\\xf7",wI="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",EI="\\u2000-\\u206f",CI=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",vp="A-Z\\xc0-\\xd6\\xd8-\\xde",_p="\\ufe0e\\ufe0f",bp=yI+wI+EI+CI,Cu="['’]",DI="["+ia+"]",yp="["+bp+"]",aa="["+pp+"]",wp="\\d+",xI="["+mp+"]",Ep="["+gp+"]",Cp="[^"+ia+bp+wp+mp+gp+vp+"]",Du="\\ud83c[\\udffb-\\udfff]",SI="(?:"+aa+"|"+Du+")",Dp="[^"+ia+"]",xu="(?:\\ud83c[\\udde6-\\uddff]){2}",Su="[\\ud800-\\udbff][\\udc00-\\udfff]",$n="["+vp+"]",xp="\\u200d",Sp="(?:"+Ep+"|"+Cp+")",OI="(?:"+$n+"|"+Cp+")",Op="(?:"+Cu+"(?:d|ll|m|re|s|t|ve))?",Tp="(?:"+Cu+"(?:D|LL|M|RE|S|T|VE))?",Np=SI+"?",Ip="["+_p+"]?",TI="(?:"+xp+"(?:"+[Dp,xu,Su].join("|")+")"+Ip+Np+")*",NI="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",II="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",Ap=Ip+Np+TI,AI="(?:"+[xI,xu,Su].join("|")+")"+Ap,MI="(?:"+[Dp+aa+"?",aa,xu,Su,DI].join("|")+")",PI=RegExp(Cu,"g"),kI=RegExp(aa,"g"),Ou=RegExp(Du+"(?="+Du+")|"+MI+Ap,"g"),VI=RegExp([$n+"?"+Ep+"+"+Op+"(?="+[yp,$n,"$"].join("|")+")",OI+"+"+Tp+"(?="+[yp,$n+Sp,"$"].join("|")+")",$n+"?"+Sp+"+"+Op,$n+"+"+Tp,II,NI,wp,AI].join("|"),"g"),RI=RegExp("["+xp+ia+pp+_p+"]"),UI=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,FI=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],LI=-1,ht={};ht[et]=ht[Ze]=ht[Lt]=ht[St]=ht[ts]=ht[Bt]=ht[gr]=ht[Bn]=ht[It]=!0,ht[zt]=ht[ps]=ht[Ce]=ht[le]=ht[Le]=ht[ie]=ht[ke]=ht[ot]=ht[y]=ht[C]=ht[F]=ht[se]=ht[K]=ht[J]=ht[oe]=!1;var ct={};ct[zt]=ct[ps]=ct[Ce]=ct[Le]=ct[le]=ct[ie]=ct[et]=ct[Ze]=ct[Lt]=ct[St]=ct[ts]=ct[y]=ct[C]=ct[F]=ct[se]=ct[K]=ct[J]=ct[G]=ct[Bt]=ct[gr]=ct[Bn]=ct[It]=!0,ct[ke]=ct[ot]=ct[oe]=!1;var BI={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},$I={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},jI={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},HI={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},qI=parseFloat,zI=parseInt,Mp=typeof Ro=="object"&&Ro&&Ro.Object===Object&&Ro,WI=typeof self=="object"&&self&&self.Object===Object&&self,$t=Mp||WI||Function("return this")(),Tu=t&&!t.nodeType&&t,mn=Tu&&!0&&e&&!e.nodeType&&e,Pp=mn&&mn.exports===Tu,Nu=Pp&&Mp.process,Cs=function(){try{var T=mn&&mn.require&&mn.require("util").types;return T||Nu&&Nu.binding&&Nu.binding("util")}catch{}}(),kp=Cs&&Cs.isArrayBuffer,Vp=Cs&&Cs.isDate,Rp=Cs&&Cs.isMap,Up=Cs&&Cs.isRegExp,Fp=Cs&&Cs.isSet,Lp=Cs&&Cs.isTypedArray;function ms(T,U,A){switch(A.length){case 0:return T.call(U);case 1:return T.call(U,A[0]);case 2:return T.call(U,A[0],A[1]);case 3:return T.call(U,A[0],A[1],A[2])}return T.apply(U,A)}function GI(T,U,A,fe){for(var Pe=-1,tt=T==null?0:T.length;++Pe<tt;){var At=T[Pe];U(fe,At,A(At),T)}return fe}function Ds(T,U){for(var A=-1,fe=T==null?0:T.length;++A<fe&&U(T[A],A,T)!==!1;);return T}function KI(T,U){for(var A=T==null?0:T.length;A--&&U(T[A],A,T)!==!1;);return T}function Bp(T,U){for(var A=-1,fe=T==null?0:T.length;++A<fe;)if(!U(T[A],A,T))return!1;return!0}function Lr(T,U){for(var A=-1,fe=T==null?0:T.length,Pe=0,tt=[];++A<fe;){var At=T[A];U(At,A,T)&&(tt[Pe++]=At)}return tt}function la(T,U){var A=T==null?0:T.length;return!!A&&jn(T,U,0)>-1}function Iu(T,U,A){for(var fe=-1,Pe=T==null?0:T.length;++fe<Pe;)if(A(U,T[fe]))return!0;return!1}function gt(T,U){for(var A=-1,fe=T==null?0:T.length,Pe=Array(fe);++A<fe;)Pe[A]=U(T[A],A,T);return Pe}function Br(T,U){for(var A=-1,fe=U.length,Pe=T.length;++A<fe;)T[Pe+A]=U[A];return T}function Au(T,U,A,fe){var Pe=-1,tt=T==null?0:T.length;for(fe&&tt&&(A=T[++Pe]);++Pe<tt;)A=U(A,T[Pe],Pe,T);return A}function QI(T,U,A,fe){var Pe=T==null?0:T.length;for(fe&&Pe&&(A=T[--Pe]);Pe--;)A=U(A,T[Pe],Pe,T);return A}function Mu(T,U){for(var A=-1,fe=T==null?0:T.length;++A<fe;)if(U(T[A],A,T))return!0;return!1}var YI=Pu("length");function ZI(T){return T.split("")}function JI(T){return T.match(iI)||[]}function $p(T,U,A){var fe;return A(T,function(Pe,tt,At){if(U(Pe,tt,At))return fe=tt,!1}),fe}function ua(T,U,A,fe){for(var Pe=T.length,tt=A+(fe?1:-1);fe?tt--:++tt<Pe;)if(U(T[tt],tt,T))return tt;return-1}function jn(T,U,A){return U===U?cA(T,U,A):ua(T,jp,A)}function XI(T,U,A,fe){for(var Pe=A-1,tt=T.length;++Pe<tt;)if(fe(T[Pe],U))return Pe;return-1}function jp(T){return T!==T}function Hp(T,U){var A=T==null?0:T.length;return A?Vu(T,U)/A:es}function Pu(T){return function(U){return U==null?s:U[T]}}function ku(T){return function(U){return T==null?s:T[U]}}function qp(T,U,A,fe,Pe){return Pe(T,function(tt,At,lt){A=fe?(fe=!1,tt):U(A,tt,At,lt)}),A}function eA(T,U){var A=T.length;for(T.sort(U);A--;)T[A]=T[A].value;return T}function Vu(T,U){for(var A,fe=-1,Pe=T.length;++fe<Pe;){var tt=U(T[fe]);tt!==s&&(A=A===s?tt:A+tt)}return A}function Ru(T,U){for(var A=-1,fe=Array(T);++A<T;)fe[A]=U(A);return fe}function tA(T,U){return gt(U,function(A){return[A,T[A]]})}function zp(T){return T&&T.slice(0,Qp(T)+1).replace(Eu,"")}function gs(T){return function(U){return T(U)}}function Uu(T,U){return gt(U,function(A){return T[A]})}function Ho(T,U){return T.has(U)}function Wp(T,U){for(var A=-1,fe=T.length;++A<fe&&jn(U,T[A],0)>-1;);return A}function Gp(T,U){for(var A=T.length;A--&&jn(U,T[A],0)>-1;);return A}function sA(T,U){for(var A=T.length,fe=0;A--;)T[A]===U&&++fe;return fe}var rA=ku(BI),nA=ku($I);function oA(T){return"\\"+HI[T]}function iA(T,U){return T==null?s:T[U]}function Hn(T){return RI.test(T)}function aA(T){return UI.test(T)}function lA(T){for(var U,A=[];!(U=T.next()).done;)A.push(U.value);return A}function Fu(T){var U=-1,A=Array(T.size);return T.forEach(function(fe,Pe){A[++U]=[Pe,fe]}),A}function Kp(T,U){return function(A){return T(U(A))}}function $r(T,U){for(var A=-1,fe=T.length,Pe=0,tt=[];++A<fe;){var At=T[A];(At===U||At===p)&&(T[A]=p,tt[Pe++]=A)}return tt}function ca(T){var U=-1,A=Array(T.size);return T.forEach(function(fe){A[++U]=fe}),A}function uA(T){var U=-1,A=Array(T.size);return T.forEach(function(fe){A[++U]=[fe,fe]}),A}function cA(T,U,A){for(var fe=A-1,Pe=T.length;++fe<Pe;)if(T[fe]===U)return fe;return-1}function dA(T,U,A){for(var fe=A+1;fe--;)if(T[fe]===U)return fe;return fe}function qn(T){return Hn(T)?hA(T):YI(T)}function Ls(T){return Hn(T)?pA(T):ZI(T)}function Qp(T){for(var U=T.length;U--&&sI.test(T.charAt(U)););return U}var fA=ku(jI);function hA(T){for(var U=Ou.lastIndex=0;Ou.test(T);)++U;return U}function pA(T){return T.match(Ou)||[]}function mA(T){return T.match(VI)||[]}var gA=function T(U){U=U==null?$t:zn.defaults($t.Object(),U,zn.pick($t,FI));var A=U.Array,fe=U.Date,Pe=U.Error,tt=U.Function,At=U.Math,lt=U.Object,Lu=U.RegExp,vA=U.String,xs=U.TypeError,da=A.prototype,_A=tt.prototype,Wn=lt.prototype,fa=U["__core-js_shared__"],ha=_A.toString,it=Wn.hasOwnProperty,bA=0,Yp=function(){var r=/[^.]+$/.exec(fa&&fa.keys&&fa.keys.IE_PROTO||"");return r?"Symbol(src)_1."+r:""}(),pa=Wn.toString,yA=ha.call(lt),wA=$t._,EA=Lu("^"+ha.call(it).replace(wu,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),ma=Pp?U.Buffer:s,jr=U.Symbol,ga=U.Uint8Array,Zp=ma?ma.allocUnsafe:s,va=Kp(lt.getPrototypeOf,lt),Jp=lt.create,Xp=Wn.propertyIsEnumerable,_a=da.splice,em=jr?jr.isConcatSpreadable:s,qo=jr?jr.iterator:s,gn=jr?jr.toStringTag:s,ba=function(){try{var r=wn(lt,"defineProperty");return r({},"",{}),r}catch{}}(),CA=U.clearTimeout!==$t.clearTimeout&&U.clearTimeout,DA=fe&&fe.now!==$t.Date.now&&fe.now,xA=U.setTimeout!==$t.setTimeout&&U.setTimeout,ya=At.ceil,wa=At.floor,Bu=lt.getOwnPropertySymbols,SA=ma?ma.isBuffer:s,tm=U.isFinite,OA=da.join,TA=Kp(lt.keys,lt),Mt=At.max,Wt=At.min,NA=fe.now,IA=U.parseInt,sm=At.random,AA=da.reverse,$u=wn(U,"DataView"),zo=wn(U,"Map"),ju=wn(U,"Promise"),Gn=wn(U,"Set"),Wo=wn(U,"WeakMap"),Go=wn(lt,"create"),Ea=Wo&&new Wo,Kn={},MA=En($u),PA=En(zo),kA=En(ju),VA=En(Gn),RA=En(Wo),Ca=jr?jr.prototype:s,Ko=Ca?Ca.valueOf:s,rm=Ca?Ca.toString:s;function _(r){if(yt(r)&&!Re(r)&&!(r instanceof Ge)){if(r instanceof Ss)return r;if(it.call(r,"__wrapped__"))return ng(r)}return new Ss(r)}var Qn=function(){function r(){}return function(o){if(!_t(o))return{};if(Jp)return Jp(o);r.prototype=o;var l=new r;return r.prototype=s,l}}();function Da(){}function Ss(r,o){this.__wrapped__=r,this.__actions__=[],this.__chain__=!!o,this.__index__=0,this.__values__=s}_.templateSettings={escape:YN,evaluate:ZN,interpolate:fp,variable:"",imports:{_}},_.prototype=Da.prototype,_.prototype.constructor=_,Ss.prototype=Qn(Da.prototype),Ss.prototype.constructor=Ss;function Ge(r){this.__wrapped__=r,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=bt,this.__views__=[]}function UA(){var r=new Ge(this.__wrapped__);return r.__actions__=os(this.__actions__),r.__dir__=this.__dir__,r.__filtered__=this.__filtered__,r.__iteratees__=os(this.__iteratees__),r.__takeCount__=this.__takeCount__,r.__views__=os(this.__views__),r}function FA(){if(this.__filtered__){var r=new Ge(this);r.__dir__=-1,r.__filtered__=!0}else r=this.clone(),r.__dir__*=-1;return r}function LA(){var r=this.__wrapped__.value(),o=this.__dir__,l=Re(r),f=o<0,g=l?r.length:0,b=ZM(0,g,this.__views__),E=b.start,x=b.end,N=x-E,B=f?x:E-1,j=this.__iteratees__,z=j.length,ce=0,ve=Wt(N,this.__takeCount__);if(!l||!f&&g==N&&ve==N)return Om(r,this.__actions__);var Oe=[];e:for(;N--&&ce<ve;){B+=o;for(var $e=-1,Te=r[B];++$e<z;){var ze=j[$e],Qe=ze.iteratee,bs=ze.type,ns=Qe(Te);if(bs==mt)Te=ns;else if(!ns){if(bs==vt)continue e;break e}}Oe[ce++]=Te}return Oe}Ge.prototype=Qn(Da.prototype),Ge.prototype.constructor=Ge;function vn(r){var o=-1,l=r==null?0:r.length;for(this.clear();++o<l;){var f=r[o];this.set(f[0],f[1])}}function BA(){this.__data__=Go?Go(null):{},this.size=0}function $A(r){var o=this.has(r)&&delete this.__data__[r];return this.size-=o?1:0,o}function jA(r){var o=this.__data__;if(Go){var l=o[r];return l===h?s:l}return it.call(o,r)?o[r]:s}function HA(r){var o=this.__data__;return Go?o[r]!==s:it.call(o,r)}function qA(r,o){var l=this.__data__;return this.size+=this.has(r)?0:1,l[r]=Go&&o===s?h:o,this}vn.prototype.clear=BA,vn.prototype.delete=$A,vn.prototype.get=jA,vn.prototype.has=HA,vn.prototype.set=qA;function vr(r){var o=-1,l=r==null?0:r.length;for(this.clear();++o<l;){var f=r[o];this.set(f[0],f[1])}}function zA(){this.__data__=[],this.size=0}function WA(r){var o=this.__data__,l=xa(o,r);if(l<0)return!1;var f=o.length-1;return l==f?o.pop():_a.call(o,l,1),--this.size,!0}function GA(r){var o=this.__data__,l=xa(o,r);return l<0?s:o[l][1]}function KA(r){return xa(this.__data__,r)>-1}function QA(r,o){var l=this.__data__,f=xa(l,r);return f<0?(++this.size,l.push([r,o])):l[f][1]=o,this}vr.prototype.clear=zA,vr.prototype.delete=WA,vr.prototype.get=GA,vr.prototype.has=KA,vr.prototype.set=QA;function _r(r){var o=-1,l=r==null?0:r.length;for(this.clear();++o<l;){var f=r[o];this.set(f[0],f[1])}}function YA(){this.size=0,this.__data__={hash:new vn,map:new(zo||vr),string:new vn}}function ZA(r){var o=Ua(this,r).delete(r);return this.size-=o?1:0,o}function JA(r){return Ua(this,r).get(r)}function XA(r){return Ua(this,r).has(r)}function eM(r,o){var l=Ua(this,r),f=l.size;return l.set(r,o),this.size+=l.size==f?0:1,this}_r.prototype.clear=YA,_r.prototype.delete=ZA,_r.prototype.get=JA,_r.prototype.has=XA,_r.prototype.set=eM;function _n(r){var o=-1,l=r==null?0:r.length;for(this.__data__=new _r;++o<l;)this.add(r[o])}function tM(r){return this.__data__.set(r,h),this}function sM(r){return this.__data__.has(r)}_n.prototype.add=_n.prototype.push=tM,_n.prototype.has=sM;function Bs(r){var o=this.__data__=new vr(r);this.size=o.size}function rM(){this.__data__=new vr,this.size=0}function nM(r){var o=this.__data__,l=o.delete(r);return this.size=o.size,l}function oM(r){return this.__data__.get(r)}function iM(r){return this.__data__.has(r)}function aM(r,o){var l=this.__data__;if(l instanceof vr){var f=l.__data__;if(!zo||f.length<n-1)return f.push([r,o]),this.size=++l.size,this;l=this.__data__=new _r(f)}return l.set(r,o),this.size=l.size,this}Bs.prototype.clear=rM,Bs.prototype.delete=nM,Bs.prototype.get=oM,Bs.prototype.has=iM,Bs.prototype.set=aM;function nm(r,o){var l=Re(r),f=!l&&Cn(r),g=!l&&!f&&Gr(r),b=!l&&!f&&!g&&Xn(r),E=l||f||g||b,x=E?Ru(r.length,vA):[],N=x.length;for(var B in r)(o||it.call(r,B))&&!(E&&(B=="length"||g&&(B=="offset"||B=="parent")||b&&(B=="buffer"||B=="byteLength"||B=="byteOffset")||Er(B,N)))&&x.push(B);return x}function om(r){var o=r.length;return o?r[Xu(0,o-1)]:s}function lM(r,o){return Fa(os(r),bn(o,0,r.length))}function uM(r){return Fa(os(r))}function Hu(r,o,l){(l!==s&&!$s(r[o],l)||l===s&&!(o in r))&&br(r,o,l)}function Qo(r,o,l){var f=r[o];(!(it.call(r,o)&&$s(f,l))||l===s&&!(o in r))&&br(r,o,l)}function xa(r,o){for(var l=r.length;l--;)if($s(r[l][0],o))return l;return-1}function cM(r,o,l,f){return Hr(r,function(g,b,E){o(f,g,l(g),E)}),f}function im(r,o){return r&&Js(o,Vt(o),r)}function dM(r,o){return r&&Js(o,as(o),r)}function br(r,o,l){o=="__proto__"&&ba?ba(r,o,{configurable:!0,enumerable:!0,value:l,writable:!0}):r[o]=l}function qu(r,o){for(var l=-1,f=o.length,g=A(f),b=r==null;++l<f;)g[l]=b?s:Dc(r,o[l]);return g}function bn(r,o,l){return r===r&&(l!==s&&(r=r<=l?r:l),o!==s&&(r=r>=o?r:o)),r}function Os(r,o,l,f,g,b){var E,x=o&v,N=o&w,B=o&D;if(l&&(E=g?l(r,f,g,b):l(r)),E!==s)return E;if(!_t(r))return r;var j=Re(r);if(j){if(E=XM(r),!x)return os(r,E)}else{var z=Gt(r),ce=z==ot||z==Ve;if(Gr(r))return Im(r,x);if(z==F||z==zt||ce&&!g){if(E=N||ce?{}:Qm(r),!x)return N?jM(r,dM(E,r)):$M(r,im(E,r))}else{if(!ct[z])return g?r:{};E=e2(r,z,x)}}b||(b=new Bs);var ve=b.get(r);if(ve)return ve;b.set(r,E),Dg(r)?r.forEach(function(Te){E.add(Os(Te,o,l,Te,r,b))}):Eg(r)&&r.forEach(function(Te,ze){E.set(ze,Os(Te,o,l,ze,r,b))});var Oe=B?N?cc:uc:N?as:Vt,$e=j?s:Oe(r);return Ds($e||r,function(Te,ze){$e&&(ze=Te,Te=r[ze]),Qo(E,ze,Os(Te,o,l,ze,r,b))}),E}function fM(r){var o=Vt(r);return function(l){return am(l,r,o)}}function am(r,o,l){var f=l.length;if(r==null)return!f;for(r=lt(r);f--;){var g=l[f],b=o[g],E=r[g];if(E===s&&!(g in r)||!b(E))return!1}return!0}function lm(r,o,l){if(typeof r!="function")throw new xs(u);return si(function(){r.apply(s,l)},o)}function Yo(r,o,l,f){var g=-1,b=la,E=!0,x=r.length,N=[],B=o.length;if(!x)return N;l&&(o=gt(o,gs(l))),f?(b=Iu,E=!1):o.length>=n&&(b=Ho,E=!1,o=new _n(o));e:for(;++g<x;){var j=r[g],z=l==null?j:l(j);if(j=f||j!==0?j:0,E&&z===z){for(var ce=B;ce--;)if(o[ce]===z)continue e;N.push(j)}else b(o,z,f)||N.push(j)}return N}var Hr=Vm(Zs),um=Vm(Wu,!0);function hM(r,o){var l=!0;return Hr(r,function(f,g,b){return l=!!o(f,g,b),l}),l}function Sa(r,o,l){for(var f=-1,g=r.length;++f<g;){var b=r[f],E=o(b);if(E!=null&&(x===s?E===E&&!_s(E):l(E,x)))var x=E,N=b}return N}function pM(r,o,l,f){var g=r.length;for(l=Be(l),l<0&&(l=-l>g?0:g+l),f=f===s||f>g?g:Be(f),f<0&&(f+=g),f=l>f?0:Sg(f);l<f;)r[l++]=o;return r}function cm(r,o){var l=[];return Hr(r,function(f,g,b){o(f,g,b)&&l.push(f)}),l}function jt(r,o,l,f,g){var b=-1,E=r.length;for(l||(l=s2),g||(g=[]);++b<E;){var x=r[b];o>0&&l(x)?o>1?jt(x,o-1,l,f,g):Br(g,x):f||(g[g.length]=x)}return g}var zu=Rm(),dm=Rm(!0);function Zs(r,o){return r&&zu(r,o,Vt)}function Wu(r,o){return r&&dm(r,o,Vt)}function Oa(r,o){return Lr(o,function(l){return Cr(r[l])})}function yn(r,o){o=zr(o,r);for(var l=0,f=o.length;r!=null&&l<f;)r=r[Xs(o[l++])];return l&&l==f?r:s}function fm(r,o,l){var f=o(r);return Re(r)?f:Br(f,l(r))}function ss(r){return r==null?r===s?Ee:P:gn&&gn in lt(r)?YM(r):u2(r)}function Gu(r,o){return r>o}function mM(r,o){return r!=null&&it.call(r,o)}function gM(r,o){return r!=null&&o in lt(r)}function vM(r,o,l){return r>=Wt(o,l)&&r<Mt(o,l)}function Ku(r,o,l){for(var f=l?Iu:la,g=r[0].length,b=r.length,E=b,x=A(b),N=1/0,B=[];E--;){var j=r[E];E&&o&&(j=gt(j,gs(o))),N=Wt(j.length,N),x[E]=!l&&(o||g>=120&&j.length>=120)?new _n(E&&j):s}j=r[0];var z=-1,ce=x[0];e:for(;++z<g&&B.length<N;){var ve=j[z],Oe=o?o(ve):ve;if(ve=l||ve!==0?ve:0,!(ce?Ho(ce,Oe):f(B,Oe,l))){for(E=b;--E;){var $e=x[E];if(!($e?Ho($e,Oe):f(r[E],Oe,l)))continue e}ce&&ce.push(Oe),B.push(ve)}}return B}function _M(r,o,l,f){return Zs(r,function(g,b,E){o(f,l(g),b,E)}),f}function Zo(r,o,l){o=zr(o,r),r=Xm(r,o);var f=r==null?r:r[Xs(Ns(o))];return f==null?s:ms(f,r,l)}function hm(r){return yt(r)&&ss(r)==zt}function bM(r){return yt(r)&&ss(r)==Ce}function yM(r){return yt(r)&&ss(r)==ie}function Jo(r,o,l,f,g){return r===o?!0:r==null||o==null||!yt(r)&&!yt(o)?r!==r&&o!==o:wM(r,o,l,f,Jo,g)}function wM(r,o,l,f,g,b){var E=Re(r),x=Re(o),N=E?ps:Gt(r),B=x?ps:Gt(o);N=N==zt?F:N,B=B==zt?F:B;var j=N==F,z=B==F,ce=N==B;if(ce&&Gr(r)){if(!Gr(o))return!1;E=!0,j=!1}if(ce&&!j)return b||(b=new Bs),E||Xn(r)?Wm(r,o,l,f,g,b):KM(r,o,N,l,f,g,b);if(!(l&k)){var ve=j&&it.call(r,"__wrapped__"),Oe=z&&it.call(o,"__wrapped__");if(ve||Oe){var $e=ve?r.value():r,Te=Oe?o.value():o;return b||(b=new Bs),g($e,Te,l,f,b)}}return ce?(b||(b=new Bs),QM(r,o,l,f,g,b)):!1}function EM(r){return yt(r)&&Gt(r)==y}function Qu(r,o,l,f){var g=l.length,b=g,E=!f;if(r==null)return!b;for(r=lt(r);g--;){var x=l[g];if(E&&x[2]?x[1]!==r[x[0]]:!(x[0]in r))return!1}for(;++g<b;){x=l[g];var N=x[0],B=r[N],j=x[1];if(E&&x[2]){if(B===s&&!(N in r))return!1}else{var z=new Bs;if(f)var ce=f(B,j,N,r,o,z);if(!(ce===s?Jo(j,B,k|L,f,z):ce))return!1}}return!0}function pm(r){if(!_t(r)||n2(r))return!1;var o=Cr(r)?EA:fI;return o.test(En(r))}function CM(r){return yt(r)&&ss(r)==se}function DM(r){return yt(r)&&Gt(r)==K}function xM(r){return yt(r)&&qa(r.length)&&!!ht[ss(r)]}function mm(r){return typeof r=="function"?r:r==null?ls:typeof r=="object"?Re(r)?_m(r[0],r[1]):vm(r):Ug(r)}function Yu(r){if(!ti(r))return TA(r);var o=[];for(var l in lt(r))it.call(r,l)&&l!="constructor"&&o.push(l);return o}function SM(r){if(!_t(r))return l2(r);var o=ti(r),l=[];for(var f in r)f=="constructor"&&(o||!it.call(r,f))||l.push(f);return l}function Zu(r,o){return r<o}function gm(r,o){var l=-1,f=is(r)?A(r.length):[];return Hr(r,function(g,b,E){f[++l]=o(g,b,E)}),f}function vm(r){var o=fc(r);return o.length==1&&o[0][2]?Zm(o[0][0],o[0][1]):function(l){return l===r||Qu(l,r,o)}}function _m(r,o){return pc(r)&&Ym(o)?Zm(Xs(r),o):function(l){var f=Dc(l,r);return f===s&&f===o?xc(l,r):Jo(o,f,k|L)}}function Ta(r,o,l,f,g){r!==o&&zu(o,function(b,E){if(g||(g=new Bs),_t(b))OM(r,o,E,l,Ta,f,g);else{var x=f?f(gc(r,E),b,E+"",r,o,g):s;x===s&&(x=b),Hu(r,E,x)}},as)}function OM(r,o,l,f,g,b,E){var x=gc(r,l),N=gc(o,l),B=E.get(N);if(B){Hu(r,l,B);return}var j=b?b(x,N,l+"",r,o,E):s,z=j===s;if(z){var ce=Re(N),ve=!ce&&Gr(N),Oe=!ce&&!ve&&Xn(N);j=N,ce||ve||Oe?Re(x)?j=x:Et(x)?j=os(x):ve?(z=!1,j=Im(N,!0)):Oe?(z=!1,j=Am(N,!0)):j=[]:ri(N)||Cn(N)?(j=x,Cn(x)?j=Og(x):(!_t(x)||Cr(x))&&(j=Qm(N))):z=!1}z&&(E.set(N,j),g(j,N,f,b,E),E.delete(N)),Hu(r,l,j)}function bm(r,o){var l=r.length;if(l)return o+=o<0?l:0,Er(o,l)?r[o]:s}function ym(r,o,l){o.length?o=gt(o,function(b){return Re(b)?function(E){return yn(E,b.length===1?b[0]:b)}:b}):o=[ls];var f=-1;o=gt(o,gs(De()));var g=gm(r,function(b,E,x){var N=gt(o,function(B){return B(b)});return{criteria:N,index:++f,value:b}});return eA(g,function(b,E){return BM(b,E,l)})}function TM(r,o){return wm(r,o,function(l,f){return xc(r,f)})}function wm(r,o,l){for(var f=-1,g=o.length,b={};++f<g;){var E=o[f],x=yn(r,E);l(x,E)&&Xo(b,zr(E,r),x)}return b}function NM(r){return function(o){return yn(o,r)}}function Ju(r,o,l,f){var g=f?XI:jn,b=-1,E=o.length,x=r;for(r===o&&(o=os(o)),l&&(x=gt(r,gs(l)));++b<E;)for(var N=0,B=o[b],j=l?l(B):B;(N=g(x,j,N,f))>-1;)x!==r&&_a.call(x,N,1),_a.call(r,N,1);return r}function Em(r,o){for(var l=r?o.length:0,f=l-1;l--;){var g=o[l];if(l==f||g!==b){var b=g;Er(g)?_a.call(r,g,1):sc(r,g)}}return r}function Xu(r,o){return r+wa(sm()*(o-r+1))}function IM(r,o,l,f){for(var g=-1,b=Mt(ya((o-r)/(l||1)),0),E=A(b);b--;)E[f?b:++g]=r,r+=l;return E}function ec(r,o){var l="";if(!r||o<1||o>we)return l;do o%2&&(l+=r),o=wa(o/2),o&&(r+=r);while(o);return l}function qe(r,o){return vc(Jm(r,o,ls),r+"")}function AM(r){return om(eo(r))}function MM(r,o){var l=eo(r);return Fa(l,bn(o,0,l.length))}function Xo(r,o,l,f){if(!_t(r))return r;o=zr(o,r);for(var g=-1,b=o.length,E=b-1,x=r;x!=null&&++g<b;){var N=Xs(o[g]),B=l;if(N==="__proto__"||N==="constructor"||N==="prototype")return r;if(g!=E){var j=x[N];B=f?f(j,N,x):s,B===s&&(B=_t(j)?j:Er(o[g+1])?[]:{})}Qo(x,N,B),x=x[N]}return r}var Cm=Ea?function(r,o){return Ea.set(r,o),r}:ls,PM=ba?function(r,o){return ba(r,"toString",{configurable:!0,enumerable:!1,value:Oc(o),writable:!0})}:ls;function kM(r){return Fa(eo(r))}function Ts(r,o,l){var f=-1,g=r.length;o<0&&(o=-o>g?0:g+o),l=l>g?g:l,l<0&&(l+=g),g=o>l?0:l-o>>>0,o>>>=0;for(var b=A(g);++f<g;)b[f]=r[f+o];return b}function VM(r,o){var l;return Hr(r,function(f,g,b){return l=o(f,g,b),!l}),!!l}function Na(r,o,l){var f=0,g=r==null?f:r.length;if(typeof o=="number"&&o===o&&g<=We){for(;f<g;){var b=f+g>>>1,E=r[b];E!==null&&!_s(E)&&(l?E<=o:E<o)?f=b+1:g=b}return g}return tc(r,o,ls,l)}function tc(r,o,l,f){var g=0,b=r==null?0:r.length;if(b===0)return 0;o=l(o);for(var E=o!==o,x=o===null,N=_s(o),B=o===s;g<b;){var j=wa((g+b)/2),z=l(r[j]),ce=z!==s,ve=z===null,Oe=z===z,$e=_s(z);if(E)var Te=f||Oe;else B?Te=Oe&&(f||ce):x?Te=Oe&&ce&&(f||!ve):N?Te=Oe&&ce&&!ve&&(f||!$e):ve||$e?Te=!1:Te=f?z<=o:z<o;Te?g=j+1:b=j}return Wt(b,de)}function Dm(r,o){for(var l=-1,f=r.length,g=0,b=[];++l<f;){var E=r[l],x=o?o(E):E;if(!l||!$s(x,N)){var N=x;b[g++]=E===0?0:E}}return b}function xm(r){return typeof r=="number"?r:_s(r)?es:+r}function vs(r){if(typeof r=="string")return r;if(Re(r))return gt(r,vs)+"";if(_s(r))return rm?rm.call(r):"";var o=r+"";return o=="0"&&1/r==-Se?"-0":o}function qr(r,o,l){var f=-1,g=la,b=r.length,E=!0,x=[],N=x;if(l)E=!1,g=Iu;else if(b>=n){var B=o?null:WM(r);if(B)return ca(B);E=!1,g=Ho,N=new _n}else N=o?[]:x;e:for(;++f<b;){var j=r[f],z=o?o(j):j;if(j=l||j!==0?j:0,E&&z===z){for(var ce=N.length;ce--;)if(N[ce]===z)continue e;o&&N.push(z),x.push(j)}else g(N,z,l)||(N!==x&&N.push(z),x.push(j))}return x}function sc(r,o){return o=zr(o,r),r=Xm(r,o),r==null||delete r[Xs(Ns(o))]}function Sm(r,o,l,f){return Xo(r,o,l(yn(r,o)),f)}function Ia(r,o,l,f){for(var g=r.length,b=f?g:-1;(f?b--:++b<g)&&o(r[b],b,r););return l?Ts(r,f?0:b,f?b+1:g):Ts(r,f?b+1:0,f?g:b)}function Om(r,o){var l=r;return l instanceof Ge&&(l=l.value()),Au(o,function(f,g){return g.func.apply(g.thisArg,Br([f],g.args))},l)}function rc(r,o,l){var f=r.length;if(f<2)return f?qr(r[0]):[];for(var g=-1,b=A(f);++g<f;)for(var E=r[g],x=-1;++x<f;)x!=g&&(b[g]=Yo(b[g]||E,r[x],o,l));return qr(jt(b,1),o,l)}function Tm(r,o,l){for(var f=-1,g=r.length,b=o.length,E={};++f<g;){var x=f<b?o[f]:s;l(E,r[f],x)}return E}function nc(r){return Et(r)?r:[]}function oc(r){return typeof r=="function"?r:ls}function zr(r,o){return Re(r)?r:pc(r,o)?[r]:rg(nt(r))}var RM=qe;function Wr(r,o,l){var f=r.length;return l=l===s?f:l,!o&&l>=f?r:Ts(r,o,l)}var Nm=CA||function(r){return $t.clearTimeout(r)};function Im(r,o){if(o)return r.slice();var l=r.length,f=Zp?Zp(l):new r.constructor(l);return r.copy(f),f}function ic(r){var o=new r.constructor(r.byteLength);return new ga(o).set(new ga(r)),o}function UM(r,o){var l=o?ic(r.buffer):r.buffer;return new r.constructor(l,r.byteOffset,r.byteLength)}function FM(r){var o=new r.constructor(r.source,hp.exec(r));return o.lastIndex=r.lastIndex,o}function LM(r){return Ko?lt(Ko.call(r)):{}}function Am(r,o){var l=o?ic(r.buffer):r.buffer;return new r.constructor(l,r.byteOffset,r.length)}function Mm(r,o){if(r!==o){var l=r!==s,f=r===null,g=r===r,b=_s(r),E=o!==s,x=o===null,N=o===o,B=_s(o);if(!x&&!B&&!b&&r>o||b&&E&&N&&!x&&!B||f&&E&&N||!l&&N||!g)return 1;if(!f&&!b&&!B&&r<o||B&&l&&g&&!f&&!b||x&&l&&g||!E&&g||!N)return-1}return 0}function BM(r,o,l){for(var f=-1,g=r.criteria,b=o.criteria,E=g.length,x=l.length;++f<E;){var N=Mm(g[f],b[f]);if(N){if(f>=x)return N;var B=l[f];return N*(B=="desc"?-1:1)}}return r.index-o.index}function Pm(r,o,l,f){for(var g=-1,b=r.length,E=l.length,x=-1,N=o.length,B=Mt(b-E,0),j=A(N+B),z=!f;++x<N;)j[x]=o[x];for(;++g<E;)(z||g<b)&&(j[l[g]]=r[g]);for(;B--;)j[x++]=r[g++];return j}function km(r,o,l,f){for(var g=-1,b=r.length,E=-1,x=l.length,N=-1,B=o.length,j=Mt(b-x,0),z=A(j+B),ce=!f;++g<j;)z[g]=r[g];for(var ve=g;++N<B;)z[ve+N]=o[N];for(;++E<x;)(ce||g<b)&&(z[ve+l[E]]=r[g++]);return z}function os(r,o){var l=-1,f=r.length;for(o||(o=A(f));++l<f;)o[l]=r[l];return o}function Js(r,o,l,f){var g=!l;l||(l={});for(var b=-1,E=o.length;++b<E;){var x=o[b],N=f?f(l[x],r[x],x,l,r):s;N===s&&(N=r[x]),g?br(l,x,N):Qo(l,x,N)}return l}function $M(r,o){return Js(r,hc(r),o)}function jM(r,o){return Js(r,Gm(r),o)}function Aa(r,o){return function(l,f){var g=Re(l)?GI:cM,b=o?o():{};return g(l,r,De(f,2),b)}}function Yn(r){return qe(function(o,l){var f=-1,g=l.length,b=g>1?l[g-1]:s,E=g>2?l[2]:s;for(b=r.length>3&&typeof b=="function"?(g--,b):s,E&&rs(l[0],l[1],E)&&(b=g<3?s:b,g=1),o=lt(o);++f<g;){var x=l[f];x&&r(o,x,f,b)}return o})}function Vm(r,o){return function(l,f){if(l==null)return l;if(!is(l))return r(l,f);for(var g=l.length,b=o?g:-1,E=lt(l);(o?b--:++b<g)&&f(E[b],b,E)!==!1;);return l}}function Rm(r){return function(o,l,f){for(var g=-1,b=lt(o),E=f(o),x=E.length;x--;){var N=E[r?x:++g];if(l(b[N],N,b)===!1)break}return o}}function HM(r,o,l){var f=o&re,g=ei(r);function b(){var E=this&&this!==$t&&this instanceof b?g:r;return E.apply(f?l:this,arguments)}return b}function Um(r){return function(o){o=nt(o);var l=Hn(o)?Ls(o):s,f=l?l[0]:o.charAt(0),g=l?Wr(l,1).join(""):o.slice(1);return f[r]()+g}}function Zn(r){return function(o){return Au(Vg(kg(o).replace(PI,"")),r,"")}}function ei(r){return function(){var o=arguments;switch(o.length){case 0:return new r;case 1:return new r(o[0]);case 2:return new r(o[0],o[1]);case 3:return new r(o[0],o[1],o[2]);case 4:return new r(o[0],o[1],o[2],o[3]);case 5:return new r(o[0],o[1],o[2],o[3],o[4]);case 6:return new r(o[0],o[1],o[2],o[3],o[4],o[5]);case 7:return new r(o[0],o[1],o[2],o[3],o[4],o[5],o[6])}var l=Qn(r.prototype),f=r.apply(l,o);return _t(f)?f:l}}function qM(r,o,l){var f=ei(r);function g(){for(var b=arguments.length,E=A(b),x=b,N=Jn(g);x--;)E[x]=arguments[x];var B=b<3&&E[0]!==N&&E[b-1]!==N?[]:$r(E,N);if(b-=B.length,b<l)return jm(r,o,Ma,g.placeholder,s,E,B,s,s,l-b);var j=this&&this!==$t&&this instanceof g?f:r;return ms(j,this,E)}return g}function Fm(r){return function(o,l,f){var g=lt(o);if(!is(o)){var b=De(l,3);o=Vt(o),l=function(x){return b(g[x],x,g)}}var E=r(o,l,f);return E>-1?g[b?o[E]:E]:s}}function Lm(r){return wr(function(o){var l=o.length,f=l,g=Ss.prototype.thru;for(r&&o.reverse();f--;){var b=o[f];if(typeof b!="function")throw new xs(u);if(g&&!E&&Ra(b)=="wrapper")var E=new Ss([],!0)}for(f=E?f:l;++f<l;){b=o[f];var x=Ra(b),N=x=="wrapper"?dc(b):s;N&&mc(N[0])&&N[1]==(be|Q|Z|Ae)&&!N[4].length&&N[9]==1?E=E[Ra(N[0])].apply(E,N[3]):E=b.length==1&&mc(b)?E[x]():E.thru(b)}return function(){var B=arguments,j=B[0];if(E&&B.length==1&&Re(j))return E.plant(j).value();for(var z=0,ce=l?o[z].apply(this,B):j;++z<l;)ce=o[z].call(this,ce);return ce}})}function Ma(r,o,l,f,g,b,E,x,N,B){var j=o&be,z=o&re,ce=o&I,ve=o&(Q|ye),Oe=o&ue,$e=ce?s:ei(r);function Te(){for(var ze=arguments.length,Qe=A(ze),bs=ze;bs--;)Qe[bs]=arguments[bs];if(ve)var ns=Jn(Te),ys=sA(Qe,ns);if(f&&(Qe=Pm(Qe,f,g,ve)),b&&(Qe=km(Qe,b,E,ve)),ze-=ys,ve&&ze<B){var Ct=$r(Qe,ns);return jm(r,o,Ma,Te.placeholder,l,Qe,Ct,x,N,B-ze)}var js=z?l:this,xr=ce?js[r]:r;return ze=Qe.length,x?Qe=c2(Qe,x):Oe&&ze>1&&Qe.reverse(),j&&N<ze&&(Qe.length=N),this&&this!==$t&&this instanceof Te&&(xr=$e||ei(xr)),xr.apply(js,Qe)}return Te}function Bm(r,o){return function(l,f){return _M(l,r,o(f),{})}}function Pa(r,o){return function(l,f){var g;if(l===s&&f===s)return o;if(l!==s&&(g=l),f!==s){if(g===s)return f;typeof l=="string"||typeof f=="string"?(l=vs(l),f=vs(f)):(l=xm(l),f=xm(f)),g=r(l,f)}return g}}function ac(r){return wr(function(o){return o=gt(o,gs(De())),qe(function(l){var f=this;return r(o,function(g){return ms(g,f,l)})})})}function ka(r,o){o=o===s?" ":vs(o);var l=o.length;if(l<2)return l?ec(o,r):o;var f=ec(o,ya(r/qn(o)));return Hn(o)?Wr(Ls(f),0,r).join(""):f.slice(0,r)}function zM(r,o,l,f){var g=o&re,b=ei(r);function E(){for(var x=-1,N=arguments.length,B=-1,j=f.length,z=A(j+N),ce=this&&this!==$t&&this instanceof E?b:r;++B<j;)z[B]=f[B];for(;N--;)z[B++]=arguments[++x];return ms(ce,g?l:this,z)}return E}function $m(r){return function(o,l,f){return f&&typeof f!="number"&&rs(o,l,f)&&(l=f=s),o=Dr(o),l===s?(l=o,o=0):l=Dr(l),f=f===s?o<l?1:-1:Dr(f),IM(o,l,f,r)}}function Va(r){return function(o,l){return typeof o=="string"&&typeof l=="string"||(o=Is(o),l=Is(l)),r(o,l)}}function jm(r,o,l,f,g,b,E,x,N,B){var j=o&Q,z=j?E:s,ce=j?s:E,ve=j?b:s,Oe=j?s:b;o|=j?Z:he,o&=~(j?he:Z),o&ne||(o&=~(re|I));var $e=[r,o,g,ve,z,Oe,ce,x,N,B],Te=l.apply(s,$e);return mc(r)&&eg(Te,$e),Te.placeholder=f,tg(Te,r,o)}function lc(r){var o=At[r];return function(l,f){if(l=Is(l),f=f==null?0:Wt(Be(f),292),f&&tm(l)){var g=(nt(l)+"e").split("e"),b=o(g[0]+"e"+(+g[1]+f));return g=(nt(b)+"e").split("e"),+(g[0]+"e"+(+g[1]-f))}return o(l)}}var WM=Gn&&1/ca(new Gn([,-0]))[1]==Se?function(r){return new Gn(r)}:Ic;function Hm(r){return function(o){var l=Gt(o);return l==y?Fu(o):l==K?uA(o):tA(o,r(o))}}function yr(r,o,l,f,g,b,E,x){var N=o&I;if(!N&&typeof r!="function")throw new xs(u);var B=f?f.length:0;if(B||(o&=~(Z|he),f=g=s),E=E===s?E:Mt(Be(E),0),x=x===s?x:Be(x),B-=g?g.length:0,o&he){var j=f,z=g;f=g=s}var ce=N?s:dc(r),ve=[r,o,l,f,g,j,z,b,E,x];if(ce&&a2(ve,ce),r=ve[0],o=ve[1],l=ve[2],f=ve[3],g=ve[4],x=ve[9]=ve[9]===s?N?0:r.length:Mt(ve[9]-B,0),!x&&o&(Q|ye)&&(o&=~(Q|ye)),!o||o==re)var Oe=HM(r,o,l);else o==Q||o==ye?Oe=qM(r,o,x):(o==Z||o==(re|Z))&&!g.length?Oe=zM(r,o,l,f):Oe=Ma.apply(s,ve);var $e=ce?Cm:eg;return tg($e(Oe,ve),r,o)}function qm(r,o,l,f){return r===s||$s(r,Wn[l])&&!it.call(f,l)?o:r}function zm(r,o,l,f,g,b){return _t(r)&&_t(o)&&(b.set(o,r),Ta(r,o,s,zm,b),b.delete(o)),r}function GM(r){return ri(r)?s:r}function Wm(r,o,l,f,g,b){var E=l&k,x=r.length,N=o.length;if(x!=N&&!(E&&N>x))return!1;var B=b.get(r),j=b.get(o);if(B&&j)return B==o&&j==r;var z=-1,ce=!0,ve=l&L?new _n:s;for(b.set(r,o),b.set(o,r);++z<x;){var Oe=r[z],$e=o[z];if(f)var Te=E?f($e,Oe,z,o,r,b):f(Oe,$e,z,r,o,b);if(Te!==s){if(Te)continue;ce=!1;break}if(ve){if(!Mu(o,function(ze,Qe){if(!Ho(ve,Qe)&&(Oe===ze||g(Oe,ze,l,f,b)))return ve.push(Qe)})){ce=!1;break}}else if(!(Oe===$e||g(Oe,$e,l,f,b))){ce=!1;break}}return b.delete(r),b.delete(o),ce}function KM(r,o,l,f,g,b,E){switch(l){case Le:if(r.byteLength!=o.byteLength||r.byteOffset!=o.byteOffset)return!1;r=r.buffer,o=o.buffer;case Ce:return!(r.byteLength!=o.byteLength||!b(new ga(r),new ga(o)));case le:case ie:case C:return $s(+r,+o);case ke:return r.name==o.name&&r.message==o.message;case se:case J:return r==o+"";case y:var x=Fu;case K:var N=f&k;if(x||(x=ca),r.size!=o.size&&!N)return!1;var B=E.get(r);if(B)return B==o;f|=L,E.set(r,o);var j=Wm(x(r),x(o),f,g,b,E);return E.delete(r),j;case G:if(Ko)return Ko.call(r)==Ko.call(o)}return!1}function QM(r,o,l,f,g,b){var E=l&k,x=uc(r),N=x.length,B=uc(o),j=B.length;if(N!=j&&!E)return!1;for(var z=N;z--;){var ce=x[z];if(!(E?ce in o:it.call(o,ce)))return!1}var ve=b.get(r),Oe=b.get(o);if(ve&&Oe)return ve==o&&Oe==r;var $e=!0;b.set(r,o),b.set(o,r);for(var Te=E;++z<N;){ce=x[z];var ze=r[ce],Qe=o[ce];if(f)var bs=E?f(Qe,ze,ce,o,r,b):f(ze,Qe,ce,r,o,b);if(!(bs===s?ze===Qe||g(ze,Qe,l,f,b):bs)){$e=!1;break}Te||(Te=ce=="constructor")}if($e&&!Te){var ns=r.constructor,ys=o.constructor;ns!=ys&&"constructor"in r&&"constructor"in o&&!(typeof ns=="function"&&ns instanceof ns&&typeof ys=="function"&&ys instanceof ys)&&($e=!1)}return b.delete(r),b.delete(o),$e}function wr(r){return vc(Jm(r,s,ag),r+"")}function uc(r){return fm(r,Vt,hc)}function cc(r){return fm(r,as,Gm)}var dc=Ea?function(r){return Ea.get(r)}:Ic;function Ra(r){for(var o=r.name+"",l=Kn[o],f=it.call(Kn,o)?l.length:0;f--;){var g=l[f],b=g.func;if(b==null||b==r)return g.name}return o}function Jn(r){var o=it.call(_,"placeholder")?_:r;return o.placeholder}function De(){var r=_.iteratee||Tc;return r=r===Tc?mm:r,arguments.length?r(arguments[0],arguments[1]):r}function Ua(r,o){var l=r.__data__;return r2(o)?l[typeof o=="string"?"string":"hash"]:l.map}function fc(r){for(var o=Vt(r),l=o.length;l--;){var f=o[l],g=r[f];o[l]=[f,g,Ym(g)]}return o}function wn(r,o){var l=iA(r,o);return pm(l)?l:s}function YM(r){var o=it.call(r,gn),l=r[gn];try{r[gn]=s;var f=!0}catch{}var g=pa.call(r);return f&&(o?r[gn]=l:delete r[gn]),g}var hc=Bu?function(r){return r==null?[]:(r=lt(r),Lr(Bu(r),function(o){return Xp.call(r,o)}))}:Ac,Gm=Bu?function(r){for(var o=[];r;)Br(o,hc(r)),r=va(r);return o}:Ac,Gt=ss;($u&&Gt(new $u(new ArrayBuffer(1)))!=Le||zo&&Gt(new zo)!=y||ju&&Gt(ju.resolve())!=H||Gn&&Gt(new Gn)!=K||Wo&&Gt(new Wo)!=oe)&&(Gt=function(r){var o=ss(r),l=o==F?r.constructor:s,f=l?En(l):"";if(f)switch(f){case MA:return Le;case PA:return y;case kA:return H;case VA:return K;case RA:return oe}return o});function ZM(r,o,l){for(var f=-1,g=l.length;++f<g;){var b=l[f],E=b.size;switch(b.type){case"drop":r+=E;break;case"dropRight":o-=E;break;case"take":o=Wt(o,r+E);break;case"takeRight":r=Mt(r,o-E);break}}return{start:r,end:o}}function JM(r){var o=r.match(nI);return o?o[1].split(oI):[]}function Km(r,o,l){o=zr(o,r);for(var f=-1,g=o.length,b=!1;++f<g;){var E=Xs(o[f]);if(!(b=r!=null&&l(r,E)))break;r=r[E]}return b||++f!=g?b:(g=r==null?0:r.length,!!g&&qa(g)&&Er(E,g)&&(Re(r)||Cn(r)))}function XM(r){var o=r.length,l=new r.constructor(o);return o&&typeof r[0]=="string"&&it.call(r,"index")&&(l.index=r.index,l.input=r.input),l}function Qm(r){return typeof r.constructor=="function"&&!ti(r)?Qn(va(r)):{}}function e2(r,o,l){var f=r.constructor;switch(o){case Ce:return ic(r);case le:case ie:return new f(+r);case Le:return UM(r,l);case et:case Ze:case Lt:case St:case ts:case Bt:case gr:case Bn:case It:return Am(r,l);case y:return new f;case C:case J:return new f(r);case se:return FM(r);case K:return new f;case G:return LM(r)}}function t2(r,o){var l=o.length;if(!l)return r;var f=l-1;return o[f]=(l>1?"& ":"")+o[f],o=o.join(l>2?", ":" "),r.replace(rI,`{
/* [wrapped with `+o+`] */
`)}function s2(r){return Re(r)||Cn(r)||!!(em&&r&&r[em])}function Er(r,o){var l=typeof r;return o=o??we,!!o&&(l=="number"||l!="symbol"&&pI.test(r))&&r>-1&&r%1==0&&r<o}function rs(r,o,l){if(!_t(l))return!1;var f=typeof o;return(f=="number"?is(l)&&Er(o,l.length):f=="string"&&o in l)?$s(l[o],r):!1}function pc(r,o){if(Re(r))return!1;var l=typeof r;return l=="number"||l=="symbol"||l=="boolean"||r==null||_s(r)?!0:XN.test(r)||!JN.test(r)||o!=null&&r in lt(o)}function r2(r){var o=typeof r;return o=="string"||o=="number"||o=="symbol"||o=="boolean"?r!=="__proto__":r===null}function mc(r){var o=Ra(r),l=_[o];if(typeof l!="function"||!(o in Ge.prototype))return!1;if(r===l)return!0;var f=dc(l);return!!f&&r===f[0]}function n2(r){return!!Yp&&Yp in r}var o2=fa?Cr:Mc;function ti(r){var o=r&&r.constructor,l=typeof o=="function"&&o.prototype||Wn;return r===l}function Ym(r){return r===r&&!_t(r)}function Zm(r,o){return function(l){return l==null?!1:l[r]===o&&(o!==s||r in lt(l))}}function i2(r){var o=ja(r,function(f){return l.size===m&&l.clear(),f}),l=o.cache;return o}function a2(r,o){var l=r[1],f=o[1],g=l|f,b=g<(re|I|be),E=f==be&&l==Q||f==be&&l==Ae&&r[7].length<=o[8]||f==(be|Ae)&&o[7].length<=o[8]&&l==Q;if(!(b||E))return r;f&re&&(r[2]=o[2],g|=l&re?0:ne);var x=o[3];if(x){var N=r[3];r[3]=N?Pm(N,x,o[4]):x,r[4]=N?$r(r[3],p):o[4]}return x=o[5],x&&(N=r[5],r[5]=N?km(N,x,o[6]):x,r[6]=N?$r(r[5],p):o[6]),x=o[7],x&&(r[7]=x),f&be&&(r[8]=r[8]==null?o[8]:Wt(r[8],o[8])),r[9]==null&&(r[9]=o[9]),r[0]=o[0],r[1]=g,r}function l2(r){var o=[];if(r!=null)for(var l in lt(r))o.push(l);return o}function u2(r){return pa.call(r)}function Jm(r,o,l){return o=Mt(o===s?r.length-1:o,0),function(){for(var f=arguments,g=-1,b=Mt(f.length-o,0),E=A(b);++g<b;)E[g]=f[o+g];g=-1;for(var x=A(o+1);++g<o;)x[g]=f[g];return x[o]=l(E),ms(r,this,x)}}function Xm(r,o){return o.length<2?r:yn(r,Ts(o,0,-1))}function c2(r,o){for(var l=r.length,f=Wt(o.length,l),g=os(r);f--;){var b=o[f];r[f]=Er(b,l)?g[b]:s}return r}function gc(r,o){if(!(o==="constructor"&&typeof r[o]=="function")&&o!="__proto__")return r[o]}var eg=sg(Cm),si=xA||function(r,o){return $t.setTimeout(r,o)},vc=sg(PM);function tg(r,o,l){var f=o+"";return vc(r,t2(f,d2(JM(f),l)))}function sg(r){var o=0,l=0;return function(){var f=NA(),g=Ke-(f-l);if(l=f,g>0){if(++o>=te)return arguments[0]}else o=0;return r.apply(s,arguments)}}function Fa(r,o){var l=-1,f=r.length,g=f-1;for(o=o===s?f:o;++l<o;){var b=Xu(l,g),E=r[b];r[b]=r[l],r[l]=E}return r.length=o,r}var rg=i2(function(r){var o=[];return r.charCodeAt(0)===46&&o.push(""),r.replace(eI,function(l,f,g,b){o.push(g?b.replace(lI,"$1"):f||l)}),o});function Xs(r){if(typeof r=="string"||_s(r))return r;var o=r+"";return o=="0"&&1/r==-Se?"-0":o}function En(r){if(r!=null){try{return ha.call(r)}catch{}try{return r+""}catch{}}return""}function d2(r,o){return Ds(hs,function(l){var f="_."+l[0];o&l[1]&&!la(r,f)&&r.push(f)}),r.sort()}function ng(r){if(r instanceof Ge)return r.clone();var o=new Ss(r.__wrapped__,r.__chain__);return o.__actions__=os(r.__actions__),o.__index__=r.__index__,o.__values__=r.__values__,o}function f2(r,o,l){(l?rs(r,o,l):o===s)?o=1:o=Mt(Be(o),0);var f=r==null?0:r.length;if(!f||o<1)return[];for(var g=0,b=0,E=A(ya(f/o));g<f;)E[b++]=Ts(r,g,g+=o);return E}function h2(r){for(var o=-1,l=r==null?0:r.length,f=0,g=[];++o<l;){var b=r[o];b&&(g[f++]=b)}return g}function p2(){var r=arguments.length;if(!r)return[];for(var o=A(r-1),l=arguments[0],f=r;f--;)o[f-1]=arguments[f];return Br(Re(l)?os(l):[l],jt(o,1))}var m2=qe(function(r,o){return Et(r)?Yo(r,jt(o,1,Et,!0)):[]}),g2=qe(function(r,o){var l=Ns(o);return Et(l)&&(l=s),Et(r)?Yo(r,jt(o,1,Et,!0),De(l,2)):[]}),v2=qe(function(r,o){var l=Ns(o);return Et(l)&&(l=s),Et(r)?Yo(r,jt(o,1,Et,!0),s,l):[]});function _2(r,o,l){var f=r==null?0:r.length;return f?(o=l||o===s?1:Be(o),Ts(r,o<0?0:o,f)):[]}function b2(r,o,l){var f=r==null?0:r.length;return f?(o=l||o===s?1:Be(o),o=f-o,Ts(r,0,o<0?0:o)):[]}function y2(r,o){return r&&r.length?Ia(r,De(o,3),!0,!0):[]}function w2(r,o){return r&&r.length?Ia(r,De(o,3),!0):[]}function E2(r,o,l,f){var g=r==null?0:r.length;return g?(l&&typeof l!="number"&&rs(r,o,l)&&(l=0,f=g),pM(r,o,l,f)):[]}function og(r,o,l){var f=r==null?0:r.length;if(!f)return-1;var g=l==null?0:Be(l);return g<0&&(g=Mt(f+g,0)),ua(r,De(o,3),g)}function ig(r,o,l){var f=r==null?0:r.length;if(!f)return-1;var g=f-1;return l!==s&&(g=Be(l),g=l<0?Mt(f+g,0):Wt(g,f-1)),ua(r,De(o,3),g,!0)}function ag(r){var o=r==null?0:r.length;return o?jt(r,1):[]}function C2(r){var o=r==null?0:r.length;return o?jt(r,Se):[]}function D2(r,o){var l=r==null?0:r.length;return l?(o=o===s?1:Be(o),jt(r,o)):[]}function x2(r){for(var o=-1,l=r==null?0:r.length,f={};++o<l;){var g=r[o];f[g[0]]=g[1]}return f}function lg(r){return r&&r.length?r[0]:s}function S2(r,o,l){var f=r==null?0:r.length;if(!f)return-1;var g=l==null?0:Be(l);return g<0&&(g=Mt(f+g,0)),jn(r,o,g)}function O2(r){var o=r==null?0:r.length;return o?Ts(r,0,-1):[]}var T2=qe(function(r){var o=gt(r,nc);return o.length&&o[0]===r[0]?Ku(o):[]}),N2=qe(function(r){var o=Ns(r),l=gt(r,nc);return o===Ns(l)?o=s:l.pop(),l.length&&l[0]===r[0]?Ku(l,De(o,2)):[]}),I2=qe(function(r){var o=Ns(r),l=gt(r,nc);return o=typeof o=="function"?o:s,o&&l.pop(),l.length&&l[0]===r[0]?Ku(l,s,o):[]});function A2(r,o){return r==null?"":OA.call(r,o)}function Ns(r){var o=r==null?0:r.length;return o?r[o-1]:s}function M2(r,o,l){var f=r==null?0:r.length;if(!f)return-1;var g=f;return l!==s&&(g=Be(l),g=g<0?Mt(f+g,0):Wt(g,f-1)),o===o?dA(r,o,g):ua(r,jp,g,!0)}function P2(r,o){return r&&r.length?bm(r,Be(o)):s}var k2=qe(ug);function ug(r,o){return r&&r.length&&o&&o.length?Ju(r,o):r}function V2(r,o,l){return r&&r.length&&o&&o.length?Ju(r,o,De(l,2)):r}function R2(r,o,l){return r&&r.length&&o&&o.length?Ju(r,o,s,l):r}var U2=wr(function(r,o){var l=r==null?0:r.length,f=qu(r,o);return Em(r,gt(o,function(g){return Er(g,l)?+g:g}).sort(Mm)),f});function F2(r,o){var l=[];if(!(r&&r.length))return l;var f=-1,g=[],b=r.length;for(o=De(o,3);++f<b;){var E=r[f];o(E,f,r)&&(l.push(E),g.push(f))}return Em(r,g),l}function _c(r){return r==null?r:AA.call(r)}function L2(r,o,l){var f=r==null?0:r.length;return f?(l&&typeof l!="number"&&rs(r,o,l)?(o=0,l=f):(o=o==null?0:Be(o),l=l===s?f:Be(l)),Ts(r,o,l)):[]}function B2(r,o){return Na(r,o)}function $2(r,o,l){return tc(r,o,De(l,2))}function j2(r,o){var l=r==null?0:r.length;if(l){var f=Na(r,o);if(f<l&&$s(r[f],o))return f}return-1}function H2(r,o){return Na(r,o,!0)}function q2(r,o,l){return tc(r,o,De(l,2),!0)}function z2(r,o){var l=r==null?0:r.length;if(l){var f=Na(r,o,!0)-1;if($s(r[f],o))return f}return-1}function W2(r){return r&&r.length?Dm(r):[]}function G2(r,o){return r&&r.length?Dm(r,De(o,2)):[]}function K2(r){var o=r==null?0:r.length;return o?Ts(r,1,o):[]}function Q2(r,o,l){return r&&r.length?(o=l||o===s?1:Be(o),Ts(r,0,o<0?0:o)):[]}function Y2(r,o,l){var f=r==null?0:r.length;return f?(o=l||o===s?1:Be(o),o=f-o,Ts(r,o<0?0:o,f)):[]}function Z2(r,o){return r&&r.length?Ia(r,De(o,3),!1,!0):[]}function J2(r,o){return r&&r.length?Ia(r,De(o,3)):[]}var X2=qe(function(r){return qr(jt(r,1,Et,!0))}),eP=qe(function(r){var o=Ns(r);return Et(o)&&(o=s),qr(jt(r,1,Et,!0),De(o,2))}),tP=qe(function(r){var o=Ns(r);return o=typeof o=="function"?o:s,qr(jt(r,1,Et,!0),s,o)});function sP(r){return r&&r.length?qr(r):[]}function rP(r,o){return r&&r.length?qr(r,De(o,2)):[]}function nP(r,o){return o=typeof o=="function"?o:s,r&&r.length?qr(r,s,o):[]}function bc(r){if(!(r&&r.length))return[];var o=0;return r=Lr(r,function(l){if(Et(l))return o=Mt(l.length,o),!0}),Ru(o,function(l){return gt(r,Pu(l))})}function cg(r,o){if(!(r&&r.length))return[];var l=bc(r);return o==null?l:gt(l,function(f){return ms(o,s,f)})}var oP=qe(function(r,o){return Et(r)?Yo(r,o):[]}),iP=qe(function(r){return rc(Lr(r,Et))}),aP=qe(function(r){var o=Ns(r);return Et(o)&&(o=s),rc(Lr(r,Et),De(o,2))}),lP=qe(function(r){var o=Ns(r);return o=typeof o=="function"?o:s,rc(Lr(r,Et),s,o)}),uP=qe(bc);function cP(r,o){return Tm(r||[],o||[],Qo)}function dP(r,o){return Tm(r||[],o||[],Xo)}var fP=qe(function(r){var o=r.length,l=o>1?r[o-1]:s;return l=typeof l=="function"?(r.pop(),l):s,cg(r,l)});function dg(r){var o=_(r);return o.__chain__=!0,o}function hP(r,o){return o(r),r}function La(r,o){return o(r)}var pP=wr(function(r){var o=r.length,l=o?r[0]:0,f=this.__wrapped__,g=function(b){return qu(b,r)};return o>1||this.__actions__.length||!(f instanceof Ge)||!Er(l)?this.thru(g):(f=f.slice(l,+l+(o?1:0)),f.__actions__.push({func:La,args:[g],thisArg:s}),new Ss(f,this.__chain__).thru(function(b){return o&&!b.length&&b.push(s),b}))});function mP(){return dg(this)}function gP(){return new Ss(this.value(),this.__chain__)}function vP(){this.__values__===s&&(this.__values__=xg(this.value()));var r=this.__index__>=this.__values__.length,o=r?s:this.__values__[this.__index__++];return{done:r,value:o}}function _P(){return this}function bP(r){for(var o,l=this;l instanceof Da;){var f=ng(l);f.__index__=0,f.__values__=s,o?g.__wrapped__=f:o=f;var g=f;l=l.__wrapped__}return g.__wrapped__=r,o}function yP(){var r=this.__wrapped__;if(r instanceof Ge){var o=r;return this.__actions__.length&&(o=new Ge(this)),o=o.reverse(),o.__actions__.push({func:La,args:[_c],thisArg:s}),new Ss(o,this.__chain__)}return this.thru(_c)}function wP(){return Om(this.__wrapped__,this.__actions__)}var EP=Aa(function(r,o,l){it.call(r,l)?++r[l]:br(r,l,1)});function CP(r,o,l){var f=Re(r)?Bp:hM;return l&&rs(r,o,l)&&(o=s),f(r,De(o,3))}function DP(r,o){var l=Re(r)?Lr:cm;return l(r,De(o,3))}var xP=Fm(og),SP=Fm(ig);function OP(r,o){return jt(Ba(r,o),1)}function TP(r,o){return jt(Ba(r,o),Se)}function NP(r,o,l){return l=l===s?1:Be(l),jt(Ba(r,o),l)}function fg(r,o){var l=Re(r)?Ds:Hr;return l(r,De(o,3))}function hg(r,o){var l=Re(r)?KI:um;return l(r,De(o,3))}var IP=Aa(function(r,o,l){it.call(r,l)?r[l].push(o):br(r,l,[o])});function AP(r,o,l,f){r=is(r)?r:eo(r),l=l&&!f?Be(l):0;var g=r.length;return l<0&&(l=Mt(g+l,0)),za(r)?l<=g&&r.indexOf(o,l)>-1:!!g&&jn(r,o,l)>-1}var MP=qe(function(r,o,l){var f=-1,g=typeof o=="function",b=is(r)?A(r.length):[];return Hr(r,function(E){b[++f]=g?ms(o,E,l):Zo(E,o,l)}),b}),PP=Aa(function(r,o,l){br(r,l,o)});function Ba(r,o){var l=Re(r)?gt:gm;return l(r,De(o,3))}function kP(r,o,l,f){return r==null?[]:(Re(o)||(o=o==null?[]:[o]),l=f?s:l,Re(l)||(l=l==null?[]:[l]),ym(r,o,l))}var VP=Aa(function(r,o,l){r[l?0:1].push(o)},function(){return[[],[]]});function RP(r,o,l){var f=Re(r)?Au:qp,g=arguments.length<3;return f(r,De(o,4),l,g,Hr)}function UP(r,o,l){var f=Re(r)?QI:qp,g=arguments.length<3;return f(r,De(o,4),l,g,um)}function FP(r,o){var l=Re(r)?Lr:cm;return l(r,Ha(De(o,3)))}function LP(r){var o=Re(r)?om:AM;return o(r)}function BP(r,o,l){(l?rs(r,o,l):o===s)?o=1:o=Be(o);var f=Re(r)?lM:MM;return f(r,o)}function $P(r){var o=Re(r)?uM:kM;return o(r)}function jP(r){if(r==null)return 0;if(is(r))return za(r)?qn(r):r.length;var o=Gt(r);return o==y||o==K?r.size:Yu(r).length}function HP(r,o,l){var f=Re(r)?Mu:VM;return l&&rs(r,o,l)&&(o=s),f(r,De(o,3))}var qP=qe(function(r,o){if(r==null)return[];var l=o.length;return l>1&&rs(r,o[0],o[1])?o=[]:l>2&&rs(o[0],o[1],o[2])&&(o=[o[0]]),ym(r,jt(o,1),[])}),$a=DA||function(){return $t.Date.now()};function zP(r,o){if(typeof o!="function")throw new xs(u);return r=Be(r),function(){if(--r<1)return o.apply(this,arguments)}}function pg(r,o,l){return o=l?s:o,o=r&&o==null?r.length:o,yr(r,be,s,s,s,s,o)}function mg(r,o){var l;if(typeof o!="function")throw new xs(u);return r=Be(r),function(){return--r>0&&(l=o.apply(this,arguments)),r<=1&&(o=s),l}}var yc=qe(function(r,o,l){var f=re;if(l.length){var g=$r(l,Jn(yc));f|=Z}return yr(r,f,o,l,g)}),gg=qe(function(r,o,l){var f=re|I;if(l.length){var g=$r(l,Jn(gg));f|=Z}return yr(o,f,r,l,g)});function vg(r,o,l){o=l?s:o;var f=yr(r,Q,s,s,s,s,s,o);return f.placeholder=vg.placeholder,f}function _g(r,o,l){o=l?s:o;var f=yr(r,ye,s,s,s,s,s,o);return f.placeholder=_g.placeholder,f}function bg(r,o,l){var f,g,b,E,x,N,B=0,j=!1,z=!1,ce=!0;if(typeof r!="function")throw new xs(u);o=Is(o)||0,_t(l)&&(j=!!l.leading,z="maxWait"in l,b=z?Mt(Is(l.maxWait)||0,o):b,ce="trailing"in l?!!l.trailing:ce);function ve(Ct){var js=f,xr=g;return f=g=s,B=Ct,E=r.apply(xr,js),E}function Oe(Ct){return B=Ct,x=si(ze,o),j?ve(Ct):E}function $e(Ct){var js=Ct-N,xr=Ct-B,Fg=o-js;return z?Wt(Fg,b-xr):Fg}function Te(Ct){var js=Ct-N,xr=Ct-B;return N===s||js>=o||js<0||z&&xr>=b}function ze(){var Ct=$a();if(Te(Ct))return Qe(Ct);x=si(ze,$e(Ct))}function Qe(Ct){return x=s,ce&&f?ve(Ct):(f=g=s,E)}function bs(){x!==s&&Nm(x),B=0,f=N=g=x=s}function ns(){return x===s?E:Qe($a())}function ys(){var Ct=$a(),js=Te(Ct);if(f=arguments,g=this,N=Ct,js){if(x===s)return Oe(N);if(z)return Nm(x),x=si(ze,o),ve(N)}return x===s&&(x=si(ze,o)),E}return ys.cancel=bs,ys.flush=ns,ys}var WP=qe(function(r,o){return lm(r,1,o)}),GP=qe(function(r,o,l){return lm(r,Is(o)||0,l)});function KP(r){return yr(r,ue)}function ja(r,o){if(typeof r!="function"||o!=null&&typeof o!="function")throw new xs(u);var l=function(){var f=arguments,g=o?o.apply(this,f):f[0],b=l.cache;if(b.has(g))return b.get(g);var E=r.apply(this,f);return l.cache=b.set(g,E)||b,E};return l.cache=new(ja.Cache||_r),l}ja.Cache=_r;function Ha(r){if(typeof r!="function")throw new xs(u);return function(){var o=arguments;switch(o.length){case 0:return!r.call(this);case 1:return!r.call(this,o[0]);case 2:return!r.call(this,o[0],o[1]);case 3:return!r.call(this,o[0],o[1],o[2])}return!r.apply(this,o)}}function QP(r){return mg(2,r)}var YP=RM(function(r,o){o=o.length==1&&Re(o[0])?gt(o[0],gs(De())):gt(jt(o,1),gs(De()));var l=o.length;return qe(function(f){for(var g=-1,b=Wt(f.length,l);++g<b;)f[g]=o[g].call(this,f[g]);return ms(r,this,f)})}),wc=qe(function(r,o){var l=$r(o,Jn(wc));return yr(r,Z,s,o,l)}),yg=qe(function(r,o){var l=$r(o,Jn(yg));return yr(r,he,s,o,l)}),ZP=wr(function(r,o){return yr(r,Ae,s,s,s,o)});function JP(r,o){if(typeof r!="function")throw new xs(u);return o=o===s?o:Be(o),qe(r,o)}function XP(r,o){if(typeof r!="function")throw new xs(u);return o=o==null?0:Mt(Be(o),0),qe(function(l){var f=l[o],g=Wr(l,0,o);return f&&Br(g,f),ms(r,this,g)})}function ek(r,o,l){var f=!0,g=!0;if(typeof r!="function")throw new xs(u);return _t(l)&&(f="leading"in l?!!l.leading:f,g="trailing"in l?!!l.trailing:g),bg(r,o,{leading:f,maxWait:o,trailing:g})}function tk(r){return pg(r,1)}function sk(r,o){return wc(oc(o),r)}function rk(){if(!arguments.length)return[];var r=arguments[0];return Re(r)?r:[r]}function nk(r){return Os(r,D)}function ok(r,o){return o=typeof o=="function"?o:s,Os(r,D,o)}function ik(r){return Os(r,v|D)}function ak(r,o){return o=typeof o=="function"?o:s,Os(r,v|D,o)}function lk(r,o){return o==null||am(r,o,Vt(o))}function $s(r,o){return r===o||r!==r&&o!==o}var uk=Va(Gu),ck=Va(function(r,o){return r>=o}),Cn=hm(function(){return arguments}())?hm:function(r){return yt(r)&&it.call(r,"callee")&&!Xp.call(r,"callee")},Re=A.isArray,dk=kp?gs(kp):bM;function is(r){return r!=null&&qa(r.length)&&!Cr(r)}function Et(r){return yt(r)&&is(r)}function fk(r){return r===!0||r===!1||yt(r)&&ss(r)==le}var Gr=SA||Mc,hk=Vp?gs(Vp):yM;function pk(r){return yt(r)&&r.nodeType===1&&!ri(r)}function mk(r){if(r==null)return!0;if(is(r)&&(Re(r)||typeof r=="string"||typeof r.splice=="function"||Gr(r)||Xn(r)||Cn(r)))return!r.length;var o=Gt(r);if(o==y||o==K)return!r.size;if(ti(r))return!Yu(r).length;for(var l in r)if(it.call(r,l))return!1;return!0}function gk(r,o){return Jo(r,o)}function vk(r,o,l){l=typeof l=="function"?l:s;var f=l?l(r,o):s;return f===s?Jo(r,o,s,l):!!f}function Ec(r){if(!yt(r))return!1;var o=ss(r);return o==ke||o==me||typeof r.message=="string"&&typeof r.name=="string"&&!ri(r)}function _k(r){return typeof r=="number"&&tm(r)}function Cr(r){if(!_t(r))return!1;var o=ss(r);return o==ot||o==Ve||o==R||o==W}function wg(r){return typeof r=="number"&&r==Be(r)}function qa(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=we}function _t(r){var o=typeof r;return r!=null&&(o=="object"||o=="function")}function yt(r){return r!=null&&typeof r=="object"}var Eg=Rp?gs(Rp):EM;function bk(r,o){return r===o||Qu(r,o,fc(o))}function yk(r,o,l){return l=typeof l=="function"?l:s,Qu(r,o,fc(o),l)}function wk(r){return Cg(r)&&r!=+r}function Ek(r){if(o2(r))throw new Pe(a);return pm(r)}function Ck(r){return r===null}function Dk(r){return r==null}function Cg(r){return typeof r=="number"||yt(r)&&ss(r)==C}function ri(r){if(!yt(r)||ss(r)!=F)return!1;var o=va(r);if(o===null)return!0;var l=it.call(o,"constructor")&&o.constructor;return typeof l=="function"&&l instanceof l&&ha.call(l)==yA}var Cc=Up?gs(Up):CM;function xk(r){return wg(r)&&r>=-we&&r<=we}var Dg=Fp?gs(Fp):DM;function za(r){return typeof r=="string"||!Re(r)&&yt(r)&&ss(r)==J}function _s(r){return typeof r=="symbol"||yt(r)&&ss(r)==G}var Xn=Lp?gs(Lp):xM;function Sk(r){return r===s}function Ok(r){return yt(r)&&Gt(r)==oe}function Tk(r){return yt(r)&&ss(r)==_e}var Nk=Va(Zu),Ik=Va(function(r,o){return r<=o});function xg(r){if(!r)return[];if(is(r))return za(r)?Ls(r):os(r);if(qo&&r[qo])return lA(r[qo]());var o=Gt(r),l=o==y?Fu:o==K?ca:eo;return l(r)}function Dr(r){if(!r)return r===0?r:0;if(r=Is(r),r===Se||r===-Se){var o=r<0?-1:1;return o*Ft}return r===r?r:0}function Be(r){var o=Dr(r),l=o%1;return o===o?l?o-l:o:0}function Sg(r){return r?bn(Be(r),0,bt):0}function Is(r){if(typeof r=="number")return r;if(_s(r))return es;if(_t(r)){var o=typeof r.valueOf=="function"?r.valueOf():r;r=_t(o)?o+"":o}if(typeof r!="string")return r===0?r:+r;r=zp(r);var l=dI.test(r);return l||hI.test(r)?zI(r.slice(2),l?2:8):cI.test(r)?es:+r}function Og(r){return Js(r,as(r))}function Ak(r){return r?bn(Be(r),-we,we):r===0?r:0}function nt(r){return r==null?"":vs(r)}var Mk=Yn(function(r,o){if(ti(o)||is(o)){Js(o,Vt(o),r);return}for(var l in o)it.call(o,l)&&Qo(r,l,o[l])}),Tg=Yn(function(r,o){Js(o,as(o),r)}),Wa=Yn(function(r,o,l,f){Js(o,as(o),r,f)}),Pk=Yn(function(r,o,l,f){Js(o,Vt(o),r,f)}),kk=wr(qu);function Vk(r,o){var l=Qn(r);return o==null?l:im(l,o)}var Rk=qe(function(r,o){r=lt(r);var l=-1,f=o.length,g=f>2?o[2]:s;for(g&&rs(o[0],o[1],g)&&(f=1);++l<f;)for(var b=o[l],E=as(b),x=-1,N=E.length;++x<N;){var B=E[x],j=r[B];(j===s||$s(j,Wn[B])&&!it.call(r,B))&&(r[B]=b[B])}return r}),Uk=qe(function(r){return r.push(s,zm),ms(Ng,s,r)});function Fk(r,o){return $p(r,De(o,3),Zs)}function Lk(r,o){return $p(r,De(o,3),Wu)}function Bk(r,o){return r==null?r:zu(r,De(o,3),as)}function $k(r,o){return r==null?r:dm(r,De(o,3),as)}function jk(r,o){return r&&Zs(r,De(o,3))}function Hk(r,o){return r&&Wu(r,De(o,3))}function qk(r){return r==null?[]:Oa(r,Vt(r))}function zk(r){return r==null?[]:Oa(r,as(r))}function Dc(r,o,l){var f=r==null?s:yn(r,o);return f===s?l:f}function Wk(r,o){return r!=null&&Km(r,o,mM)}function xc(r,o){return r!=null&&Km(r,o,gM)}var Gk=Bm(function(r,o,l){o!=null&&typeof o.toString!="function"&&(o=pa.call(o)),r[o]=l},Oc(ls)),Kk=Bm(function(r,o,l){o!=null&&typeof o.toString!="function"&&(o=pa.call(o)),it.call(r,o)?r[o].push(l):r[o]=[l]},De),Qk=qe(Zo);function Vt(r){return is(r)?nm(r):Yu(r)}function as(r){return is(r)?nm(r,!0):SM(r)}function Yk(r,o){var l={};return o=De(o,3),Zs(r,function(f,g,b){br(l,o(f,g,b),f)}),l}function Zk(r,o){var l={};return o=De(o,3),Zs(r,function(f,g,b){br(l,g,o(f,g,b))}),l}var Jk=Yn(function(r,o,l){Ta(r,o,l)}),Ng=Yn(function(r,o,l,f){Ta(r,o,l,f)}),Xk=wr(function(r,o){var l={};if(r==null)return l;var f=!1;o=gt(o,function(b){return b=zr(b,r),f||(f=b.length>1),b}),Js(r,cc(r),l),f&&(l=Os(l,v|w|D,GM));for(var g=o.length;g--;)sc(l,o[g]);return l});function eV(r,o){return Ig(r,Ha(De(o)))}var tV=wr(function(r,o){return r==null?{}:TM(r,o)});function Ig(r,o){if(r==null)return{};var l=gt(cc(r),function(f){return[f]});return o=De(o),wm(r,l,function(f,g){return o(f,g[0])})}function sV(r,o,l){o=zr(o,r);var f=-1,g=o.length;for(g||(g=1,r=s);++f<g;){var b=r==null?s:r[Xs(o[f])];b===s&&(f=g,b=l),r=Cr(b)?b.call(r):b}return r}function rV(r,o,l){return r==null?r:Xo(r,o,l)}function nV(r,o,l,f){return f=typeof f=="function"?f:s,r==null?r:Xo(r,o,l,f)}var Ag=Hm(Vt),Mg=Hm(as);function oV(r,o,l){var f=Re(r),g=f||Gr(r)||Xn(r);if(o=De(o,4),l==null){var b=r&&r.constructor;g?l=f?new b:[]:_t(r)?l=Cr(b)?Qn(va(r)):{}:l={}}return(g?Ds:Zs)(r,function(E,x,N){return o(l,E,x,N)}),l}function iV(r,o){return r==null?!0:sc(r,o)}function aV(r,o,l){return r==null?r:Sm(r,o,oc(l))}function lV(r,o,l,f){return f=typeof f=="function"?f:s,r==null?r:Sm(r,o,oc(l),f)}function eo(r){return r==null?[]:Uu(r,Vt(r))}function uV(r){return r==null?[]:Uu(r,as(r))}function cV(r,o,l){return l===s&&(l=o,o=s),l!==s&&(l=Is(l),l=l===l?l:0),o!==s&&(o=Is(o),o=o===o?o:0),bn(Is(r),o,l)}function dV(r,o,l){return o=Dr(o),l===s?(l=o,o=0):l=Dr(l),r=Is(r),vM(r,o,l)}function fV(r,o,l){if(l&&typeof l!="boolean"&&rs(r,o,l)&&(o=l=s),l===s&&(typeof o=="boolean"?(l=o,o=s):typeof r=="boolean"&&(l=r,r=s)),r===s&&o===s?(r=0,o=1):(r=Dr(r),o===s?(o=r,r=0):o=Dr(o)),r>o){var f=r;r=o,o=f}if(l||r%1||o%1){var g=sm();return Wt(r+g*(o-r+qI("1e-"+((g+"").length-1))),o)}return Xu(r,o)}var hV=Zn(function(r,o,l){return o=o.toLowerCase(),r+(l?Pg(o):o)});function Pg(r){return Sc(nt(r).toLowerCase())}function kg(r){return r=nt(r),r&&r.replace(mI,rA).replace(kI,"")}function pV(r,o,l){r=nt(r),o=vs(o);var f=r.length;l=l===s?f:bn(Be(l),0,f);var g=l;return l-=o.length,l>=0&&r.slice(l,g)==o}function mV(r){return r=nt(r),r&&QN.test(r)?r.replace(dp,nA):r}function gV(r){return r=nt(r),r&&tI.test(r)?r.replace(wu,"\\$&"):r}var vV=Zn(function(r,o,l){return r+(l?"-":"")+o.toLowerCase()}),_V=Zn(function(r,o,l){return r+(l?" ":"")+o.toLowerCase()}),bV=Um("toLowerCase");function yV(r,o,l){r=nt(r),o=Be(o);var f=o?qn(r):0;if(!o||f>=o)return r;var g=(o-f)/2;return ka(wa(g),l)+r+ka(ya(g),l)}function wV(r,o,l){r=nt(r),o=Be(o);var f=o?qn(r):0;return o&&f<o?r+ka(o-f,l):r}function EV(r,o,l){r=nt(r),o=Be(o);var f=o?qn(r):0;return o&&f<o?ka(o-f,l)+r:r}function CV(r,o,l){return l||o==null?o=0:o&&(o=+o),IA(nt(r).replace(Eu,""),o||0)}function DV(r,o,l){return(l?rs(r,o,l):o===s)?o=1:o=Be(o),ec(nt(r),o)}function xV(){var r=arguments,o=nt(r[0]);return r.length<3?o:o.replace(r[1],r[2])}var SV=Zn(function(r,o,l){return r+(l?"_":"")+o.toLowerCase()});function OV(r,o,l){return l&&typeof l!="number"&&rs(r,o,l)&&(o=l=s),l=l===s?bt:l>>>0,l?(r=nt(r),r&&(typeof o=="string"||o!=null&&!Cc(o))&&(o=vs(o),!o&&Hn(r))?Wr(Ls(r),0,l):r.split(o,l)):[]}var TV=Zn(function(r,o,l){return r+(l?" ":"")+Sc(o)});function NV(r,o,l){return r=nt(r),l=l==null?0:bn(Be(l),0,r.length),o=vs(o),r.slice(l,l+o.length)==o}function IV(r,o,l){var f=_.templateSettings;l&&rs(r,o,l)&&(o=s),r=nt(r),o=Wa({},o,f,qm);var g=Wa({},o.imports,f.imports,qm),b=Vt(g),E=Uu(g,b),x,N,B=0,j=o.interpolate||oa,z="__p += '",ce=Lu((o.escape||oa).source+"|"+j.source+"|"+(j===fp?uI:oa).source+"|"+(o.evaluate||oa).source+"|$","g"),ve="//# sourceURL="+(it.call(o,"sourceURL")?(o.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++LI+"]")+`
`;r.replace(ce,function(Te,ze,Qe,bs,ns,ys){return Qe||(Qe=bs),z+=r.slice(B,ys).replace(gI,oA),ze&&(x=!0,z+=`' +
__e(`+ze+`) +
'`),ns&&(N=!0,z+=`';
`+ns+`;
__p += '`),Qe&&(z+=`' +
((__t = (`+Qe+`)) == null ? '' : __t) +
'`),B=ys+Te.length,Te}),z+=`';
`;var Oe=it.call(o,"variable")&&o.variable;if(!Oe)z=`with (obj) {
`+z+`
}
`;else if(aI.test(Oe))throw new Pe(c);z=(N?z.replace(Es,""):z).replace(na,"$1").replace(GN,"$1;"),z="function("+(Oe||"obj")+`) {
`+(Oe?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(x?", __e = _.escape":"")+(N?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+z+`return __p
}`;var $e=Rg(function(){return tt(b,ve+"return "+z).apply(s,E)});if($e.source=z,Ec($e))throw $e;return $e}function AV(r){return nt(r).toLowerCase()}function MV(r){return nt(r).toUpperCase()}function PV(r,o,l){if(r=nt(r),r&&(l||o===s))return zp(r);if(!r||!(o=vs(o)))return r;var f=Ls(r),g=Ls(o),b=Wp(f,g),E=Gp(f,g)+1;return Wr(f,b,E).join("")}function kV(r,o,l){if(r=nt(r),r&&(l||o===s))return r.slice(0,Qp(r)+1);if(!r||!(o=vs(o)))return r;var f=Ls(r),g=Gp(f,Ls(o))+1;return Wr(f,0,g).join("")}function VV(r,o,l){if(r=nt(r),r&&(l||o===s))return r.replace(Eu,"");if(!r||!(o=vs(o)))return r;var f=Ls(r),g=Wp(f,Ls(o));return Wr(f,g).join("")}function RV(r,o){var l=ae,f=V;if(_t(o)){var g="separator"in o?o.separator:g;l="length"in o?Be(o.length):l,f="omission"in o?vs(o.omission):f}r=nt(r);var b=r.length;if(Hn(r)){var E=Ls(r);b=E.length}if(l>=b)return r;var x=l-qn(f);if(x<1)return f;var N=E?Wr(E,0,x).join(""):r.slice(0,x);if(g===s)return N+f;if(E&&(x+=N.length-x),Cc(g)){if(r.slice(x).search(g)){var B,j=N;for(g.global||(g=Lu(g.source,nt(hp.exec(g))+"g")),g.lastIndex=0;B=g.exec(j);)var z=B.index;N=N.slice(0,z===s?x:z)}}else if(r.indexOf(vs(g),x)!=x){var ce=N.lastIndexOf(g);ce>-1&&(N=N.slice(0,ce))}return N+f}function UV(r){return r=nt(r),r&&KN.test(r)?r.replace(cp,fA):r}var FV=Zn(function(r,o,l){return r+(l?" ":"")+o.toUpperCase()}),Sc=Um("toUpperCase");function Vg(r,o,l){return r=nt(r),o=l?s:o,o===s?aA(r)?mA(r):JI(r):r.match(o)||[]}var Rg=qe(function(r,o){try{return ms(r,s,o)}catch(l){return Ec(l)?l:new Pe(l)}}),LV=wr(function(r,o){return Ds(o,function(l){l=Xs(l),br(r,l,yc(r[l],r))}),r});function BV(r){var o=r==null?0:r.length,l=De();return r=o?gt(r,function(f){if(typeof f[1]!="function")throw new xs(u);return[l(f[0]),f[1]]}):[],qe(function(f){for(var g=-1;++g<o;){var b=r[g];if(ms(b[0],this,f))return ms(b[1],this,f)}})}function $V(r){return fM(Os(r,v))}function Oc(r){return function(){return r}}function jV(r,o){return r==null||r!==r?o:r}var HV=Lm(),qV=Lm(!0);function ls(r){return r}function Tc(r){return mm(typeof r=="function"?r:Os(r,v))}function zV(r){return vm(Os(r,v))}function WV(r,o){return _m(r,Os(o,v))}var GV=qe(function(r,o){return function(l){return Zo(l,r,o)}}),KV=qe(function(r,o){return function(l){return Zo(r,l,o)}});function Nc(r,o,l){var f=Vt(o),g=Oa(o,f);l==null&&!(_t(o)&&(g.length||!f.length))&&(l=o,o=r,r=this,g=Oa(o,Vt(o)));var b=!(_t(l)&&"chain"in l)||!!l.chain,E=Cr(r);return Ds(g,function(x){var N=o[x];r[x]=N,E&&(r.prototype[x]=function(){var B=this.__chain__;if(b||B){var j=r(this.__wrapped__),z=j.__actions__=os(this.__actions__);return z.push({func:N,args:arguments,thisArg:r}),j.__chain__=B,j}return N.apply(r,Br([this.value()],arguments))})}),r}function QV(){return $t._===this&&($t._=wA),this}function Ic(){}function YV(r){return r=Be(r),qe(function(o){return bm(o,r)})}var ZV=ac(gt),JV=ac(Bp),XV=ac(Mu);function Ug(r){return pc(r)?Pu(Xs(r)):NM(r)}function e3(r){return function(o){return r==null?s:yn(r,o)}}var t3=$m(),s3=$m(!0);function Ac(){return[]}function Mc(){return!1}function r3(){return{}}function n3(){return""}function o3(){return!0}function i3(r,o){if(r=Be(r),r<1||r>we)return[];var l=bt,f=Wt(r,bt);o=De(o),r-=bt;for(var g=Ru(f,o);++l<r;)o(l);return g}function a3(r){return Re(r)?gt(r,Xs):_s(r)?[r]:os(rg(nt(r)))}function l3(r){var o=++bA;return nt(r)+o}var u3=Pa(function(r,o){return r+o},0),c3=lc("ceil"),d3=Pa(function(r,o){return r/o},1),f3=lc("floor");function h3(r){return r&&r.length?Sa(r,ls,Gu):s}function p3(r,o){return r&&r.length?Sa(r,De(o,2),Gu):s}function m3(r){return Hp(r,ls)}function g3(r,o){return Hp(r,De(o,2))}function v3(r){return r&&r.length?Sa(r,ls,Zu):s}function _3(r,o){return r&&r.length?Sa(r,De(o,2),Zu):s}var b3=Pa(function(r,o){return r*o},1),y3=lc("round"),w3=Pa(function(r,o){return r-o},0);function E3(r){return r&&r.length?Vu(r,ls):0}function C3(r,o){return r&&r.length?Vu(r,De(o,2)):0}return _.after=zP,_.ary=pg,_.assign=Mk,_.assignIn=Tg,_.assignInWith=Wa,_.assignWith=Pk,_.at=kk,_.before=mg,_.bind=yc,_.bindAll=LV,_.bindKey=gg,_.castArray=rk,_.chain=dg,_.chunk=f2,_.compact=h2,_.concat=p2,_.cond=BV,_.conforms=$V,_.constant=Oc,_.countBy=EP,_.create=Vk,_.curry=vg,_.curryRight=_g,_.debounce=bg,_.defaults=Rk,_.defaultsDeep=Uk,_.defer=WP,_.delay=GP,_.difference=m2,_.differenceBy=g2,_.differenceWith=v2,_.drop=_2,_.dropRight=b2,_.dropRightWhile=y2,_.dropWhile=w2,_.fill=E2,_.filter=DP,_.flatMap=OP,_.flatMapDeep=TP,_.flatMapDepth=NP,_.flatten=ag,_.flattenDeep=C2,_.flattenDepth=D2,_.flip=KP,_.flow=HV,_.flowRight=qV,_.fromPairs=x2,_.functions=qk,_.functionsIn=zk,_.groupBy=IP,_.initial=O2,_.intersection=T2,_.intersectionBy=N2,_.intersectionWith=I2,_.invert=Gk,_.invertBy=Kk,_.invokeMap=MP,_.iteratee=Tc,_.keyBy=PP,_.keys=Vt,_.keysIn=as,_.map=Ba,_.mapKeys=Yk,_.mapValues=Zk,_.matches=zV,_.matchesProperty=WV,_.memoize=ja,_.merge=Jk,_.mergeWith=Ng,_.method=GV,_.methodOf=KV,_.mixin=Nc,_.negate=Ha,_.nthArg=YV,_.omit=Xk,_.omitBy=eV,_.once=QP,_.orderBy=kP,_.over=ZV,_.overArgs=YP,_.overEvery=JV,_.overSome=XV,_.partial=wc,_.partialRight=yg,_.partition=VP,_.pick=tV,_.pickBy=Ig,_.property=Ug,_.propertyOf=e3,_.pull=k2,_.pullAll=ug,_.pullAllBy=V2,_.pullAllWith=R2,_.pullAt=U2,_.range=t3,_.rangeRight=s3,_.rearg=ZP,_.reject=FP,_.remove=F2,_.rest=JP,_.reverse=_c,_.sampleSize=BP,_.set=rV,_.setWith=nV,_.shuffle=$P,_.slice=L2,_.sortBy=qP,_.sortedUniq=W2,_.sortedUniqBy=G2,_.split=OV,_.spread=XP,_.tail=K2,_.take=Q2,_.takeRight=Y2,_.takeRightWhile=Z2,_.takeWhile=J2,_.tap=hP,_.throttle=ek,_.thru=La,_.toArray=xg,_.toPairs=Ag,_.toPairsIn=Mg,_.toPath=a3,_.toPlainObject=Og,_.transform=oV,_.unary=tk,_.union=X2,_.unionBy=eP,_.unionWith=tP,_.uniq=sP,_.uniqBy=rP,_.uniqWith=nP,_.unset=iV,_.unzip=bc,_.unzipWith=cg,_.update=aV,_.updateWith=lV,_.values=eo,_.valuesIn=uV,_.without=oP,_.words=Vg,_.wrap=sk,_.xor=iP,_.xorBy=aP,_.xorWith=lP,_.zip=uP,_.zipObject=cP,_.zipObjectDeep=dP,_.zipWith=fP,_.entries=Ag,_.entriesIn=Mg,_.extend=Tg,_.extendWith=Wa,Nc(_,_),_.add=u3,_.attempt=Rg,_.camelCase=hV,_.capitalize=Pg,_.ceil=c3,_.clamp=cV,_.clone=nk,_.cloneDeep=ik,_.cloneDeepWith=ak,_.cloneWith=ok,_.conformsTo=lk,_.deburr=kg,_.defaultTo=jV,_.divide=d3,_.endsWith=pV,_.eq=$s,_.escape=mV,_.escapeRegExp=gV,_.every=CP,_.find=xP,_.findIndex=og,_.findKey=Fk,_.findLast=SP,_.findLastIndex=ig,_.findLastKey=Lk,_.floor=f3,_.forEach=fg,_.forEachRight=hg,_.forIn=Bk,_.forInRight=$k,_.forOwn=jk,_.forOwnRight=Hk,_.get=Dc,_.gt=uk,_.gte=ck,_.has=Wk,_.hasIn=xc,_.head=lg,_.identity=ls,_.includes=AP,_.indexOf=S2,_.inRange=dV,_.invoke=Qk,_.isArguments=Cn,_.isArray=Re,_.isArrayBuffer=dk,_.isArrayLike=is,_.isArrayLikeObject=Et,_.isBoolean=fk,_.isBuffer=Gr,_.isDate=hk,_.isElement=pk,_.isEmpty=mk,_.isEqual=gk,_.isEqualWith=vk,_.isError=Ec,_.isFinite=_k,_.isFunction=Cr,_.isInteger=wg,_.isLength=qa,_.isMap=Eg,_.isMatch=bk,_.isMatchWith=yk,_.isNaN=wk,_.isNative=Ek,_.isNil=Dk,_.isNull=Ck,_.isNumber=Cg,_.isObject=_t,_.isObjectLike=yt,_.isPlainObject=ri,_.isRegExp=Cc,_.isSafeInteger=xk,_.isSet=Dg,_.isString=za,_.isSymbol=_s,_.isTypedArray=Xn,_.isUndefined=Sk,_.isWeakMap=Ok,_.isWeakSet=Tk,_.join=A2,_.kebabCase=vV,_.last=Ns,_.lastIndexOf=M2,_.lowerCase=_V,_.lowerFirst=bV,_.lt=Nk,_.lte=Ik,_.max=h3,_.maxBy=p3,_.mean=m3,_.meanBy=g3,_.min=v3,_.minBy=_3,_.stubArray=Ac,_.stubFalse=Mc,_.stubObject=r3,_.stubString=n3,_.stubTrue=o3,_.multiply=b3,_.nth=P2,_.noConflict=QV,_.noop=Ic,_.now=$a,_.pad=yV,_.padEnd=wV,_.padStart=EV,_.parseInt=CV,_.random=fV,_.reduce=RP,_.reduceRight=UP,_.repeat=DV,_.replace=xV,_.result=sV,_.round=y3,_.runInContext=T,_.sample=LP,_.size=jP,_.snakeCase=SV,_.some=HP,_.sortedIndex=B2,_.sortedIndexBy=$2,_.sortedIndexOf=j2,_.sortedLastIndex=H2,_.sortedLastIndexBy=q2,_.sortedLastIndexOf=z2,_.startCase=TV,_.startsWith=NV,_.subtract=w3,_.sum=E3,_.sumBy=C3,_.template=IV,_.times=i3,_.toFinite=Dr,_.toInteger=Be,_.toLength=Sg,_.toLower=AV,_.toNumber=Is,_.toSafeInteger=Ak,_.toString=nt,_.toUpper=MV,_.trim=PV,_.trimEnd=kV,_.trimStart=VV,_.truncate=RV,_.unescape=UV,_.uniqueId=l3,_.upperCase=FV,_.upperFirst=Sc,_.each=fg,_.eachRight=hg,_.first=lg,Nc(_,function(){var r={};return Zs(_,function(o,l){it.call(_.prototype,l)||(r[l]=o)}),r}(),{chain:!1}),_.VERSION=i,Ds(["bind","bindKey","curry","curryRight","partial","partialRight"],function(r){_[r].placeholder=_}),Ds(["drop","take"],function(r,o){Ge.prototype[r]=function(l){l=l===s?1:Mt(Be(l),0);var f=this.__filtered__&&!o?new Ge(this):this.clone();return f.__filtered__?f.__takeCount__=Wt(l,f.__takeCount__):f.__views__.push({size:Wt(l,bt),type:r+(f.__dir__<0?"Right":"")}),f},Ge.prototype[r+"Right"]=function(l){return this.reverse()[r](l).reverse()}}),Ds(["filter","map","takeWhile"],function(r,o){var l=o+1,f=l==vt||l==ft;Ge.prototype[r]=function(g){var b=this.clone();return b.__iteratees__.push({iteratee:De(g,3),type:l}),b.__filtered__=b.__filtered__||f,b}}),Ds(["head","last"],function(r,o){var l="take"+(o?"Right":"");Ge.prototype[r]=function(){return this[l](1).value()[0]}}),Ds(["initial","tail"],function(r,o){var l="drop"+(o?"":"Right");Ge.prototype[r]=function(){return this.__filtered__?new Ge(this):this[l](1)}}),Ge.prototype.compact=function(){return this.filter(ls)},Ge.prototype.find=function(r){return this.filter(r).head()},Ge.prototype.findLast=function(r){return this.reverse().find(r)},Ge.prototype.invokeMap=qe(function(r,o){return typeof r=="function"?new Ge(this):this.map(function(l){return Zo(l,r,o)})}),Ge.prototype.reject=function(r){return this.filter(Ha(De(r)))},Ge.prototype.slice=function(r,o){r=Be(r);var l=this;return l.__filtered__&&(r>0||o<0)?new Ge(l):(r<0?l=l.takeRight(-r):r&&(l=l.drop(r)),o!==s&&(o=Be(o),l=o<0?l.dropRight(-o):l.take(o-r)),l)},Ge.prototype.takeRightWhile=function(r){return this.reverse().takeWhile(r).reverse()},Ge.prototype.toArray=function(){return this.take(bt)},Zs(Ge.prototype,function(r,o){var l=/^(?:filter|find|map|reject)|While$/.test(o),f=/^(?:head|last)$/.test(o),g=_[f?"take"+(o=="last"?"Right":""):o],b=f||/^find/.test(o);g&&(_.prototype[o]=function(){var E=this.__wrapped__,x=f?[1]:arguments,N=E instanceof Ge,B=x[0],j=N||Re(E),z=function(ze){var Qe=g.apply(_,Br([ze],x));return f&&ce?Qe[0]:Qe};j&&l&&typeof B=="function"&&B.length!=1&&(N=j=!1);var ce=this.__chain__,ve=!!this.__actions__.length,Oe=b&&!ce,$e=N&&!ve;if(!b&&j){E=$e?E:new Ge(this);var Te=r.apply(E,x);return Te.__actions__.push({func:La,args:[z],thisArg:s}),new Ss(Te,ce)}return Oe&&$e?r.apply(this,x):(Te=this.thru(z),Oe?f?Te.value()[0]:Te.value():Te)})}),Ds(["pop","push","shift","sort","splice","unshift"],function(r){var o=da[r],l=/^(?:push|sort|unshift)$/.test(r)?"tap":"thru",f=/^(?:pop|shift)$/.test(r);_.prototype[r]=function(){var g=arguments;if(f&&!this.__chain__){var b=this.value();return o.apply(Re(b)?b:[],g)}return this[l](function(E){return o.apply(Re(E)?E:[],g)})}}),Zs(Ge.prototype,function(r,o){var l=_[o];if(l){var f=l.name+"";it.call(Kn,f)||(Kn[f]=[]),Kn[f].push({name:o,func:l})}}),Kn[Ma(s,I).name]=[{name:"wrapper",func:s}],Ge.prototype.clone=UA,Ge.prototype.reverse=FA,Ge.prototype.value=LA,_.prototype.at=pP,_.prototype.chain=mP,_.prototype.commit=gP,_.prototype.next=vP,_.prototype.plant=bP,_.prototype.reverse=yP,_.prototype.toJSON=_.prototype.valueOf=_.prototype.value=wP,_.prototype.first=_.prototype.head,qo&&(_.prototype[qo]=_P),_},zn=gA();mn?((mn.exports=zn)._=zn,Tu._=zn):$t._=zn}).call(Ro)}(Ji,Ji.exports);var Xh=Ji.exports;const Fe=async(e,t)=>{try{const s=window.M.cfg.wwwroot+"/lib/ajax/service.php?sesskey="+window.M.cfg.sesskey+"&info="+e,n=await fetch(s,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify([{index:0,methodname:e,args:t}])}),a=n.clone();try{return(await n.json())[0]}catch{return{error:await a.text()}}}catch(s){throw console.error("Erro na chamada AJAX:",s),s}};async function Y0(e={}){try{return await Fe("local_offermanager_fetch",{search_string:e.search||"",type:e.type||null,only_active:e.onlyActive===!0,page:e.page||1,per_page:e.perPage||25,sort_by:e.sortBy||"name",sort_direction:e.sortDesc?"DESC":"ASC"})}catch(t){throw new Error(t.message||"Erro ao buscar ofertas")}}async function ep(e){try{return await Fe("local_offermanager_get",{id:e})}catch(t){throw new Error(t.message||"Erro ao buscar oferta")}}async function tp(e){try{return await Fe("local_offermanager_save",{id:e.id||0,name:e.name,description:e.description||"",type:e.type||"",audienceids:e.audiences||[]})}catch(t){throw new Error(t.message||"Erro ao salvar oferta")}}async function Z0(e){try{return await Fe("local_offermanager_delete",{id:e})}catch(t){throw new Error(t.message||"Erro ao excluir oferta")}}async function du(){const e=await Fe("local_offermanager_get_type_options",{});if(e.error)throw new Error(error.message||"Erro ao buscar opções de tipos");return e}async function J0(e,t){try{return await Fe("local_offermanager_add_course_to_offer",{offer_id:e,course:t})}catch(s){throw new Error(s.message||"Erro ao adicionar curso à oferta")}}async function X0(e,t){try{return await Fe("local_offermanager_delete_course",{offercourseid:t})}catch(s){throw new Error(s.message||"Erro ao remover curso da oferta")}}async function ew(e,t,s){try{return await Fe("local_offermanager_set_course_status",{id:t,status:s?1:0})}catch(i){throw new Error(i.message||"Erro ao alterar status do curso")}}async function sp(e){var t;try{const s=await Fe("local_offermanager_get_audiences",{offerid:0});return(t=s==null?void 0:s.data)!=null&&t.all_audiences?{items:s.data.all_audiences.filter(n=>n.name.toLowerCase().includes(e.toLowerCase())).map(n=>({id:n.id,name:n.name}))}:{items:[]}}catch(s){throw new Error(s)}}async function tw(e,t){try{return await Fe("local_offermanager_update_audiences",{offerid:e,audienceids:t})}catch(s){throw new Error(s)}}async function sw(e,t){try{return await Fe("local_offermanager_set_status",{id:e,status:!t})}catch(s){throw new Error(s.message||"Erro ao alterar status da oferta")}}async function Uo(e="",t=0){try{return await Fe("local_offermanager_get_categories",{search_string:e,offerid:t})}catch(s){throw new Error(s.message||"Erro ao buscar categorias")}}async function rp(e,t,s="",i=1,n=20){try{console.log(`getCoursesByCategory - Parâmetros: offerId=${e}, categoryId=${t}, search=${s}, page=${i}, perPage=${n}`);const a=parseInt(e,10),u=parseInt(t,10),c=parseInt(i,10),h=parseInt(n,10);(isNaN(a)||isNaN(u)||isNaN(c)||isNaN(h))&&console.error("Parâmetros inválidos para getCoursesByCategory");const m={offerid:a,categoryid:u,search_string:s||"",page:c,per_page:h,exclude_courseids:[]};console.log("Chamando endpoint com parâmetros:",m);const p=await Fe("local_offermanager_fetch_potential_courses",m);return console.log("Resposta bruta do endpoint:",p),p}catch(a){throw console.error("Erro em getCoursesByCategory:",a),new Error(a.message||"Erro ao buscar cursos")}}async function rw(e,t=""){try{return await Fe("local_offermanager_fetch_current_courses",{offerid:e,categoryid:0,search_string:t,exclude_courseids:[]})}catch(s){throw new Error(s.message||"Erro ao buscar cursos por nome")}}async function np(e,t){try{return await Fe("local_offermanager_fetch_current_courses",{offerid:e,categoryid:t,search_string:"",exclude_courseids:[]})}catch(s){throw new Error(s.message||"Erro ao buscar cursos por categoria")}}async function nw(e,t){try{return await Fe("local_offermanager_add_courses",{offerid:e,courseids:t})}catch(s){throw new Error(s.message||"Erro ao adicionar cursos à oferta")}}async function fu(e,t={}){try{return t.sortBy==="name"&&(t.sortBy="fullname"),t.sortBy==="turmasCount"&&(t.sortBy="class_counter"),await Fe("local_offermanager_get_current_courses",{offerid:e,only_active:t.onlyActive||!1,courseids:t.courseIds||[],page:t.page||1,per_page:t.perPage||100,sort_by:t.sortBy||"id",sort_direction:t.sortDesc?"DESC":"ASC",course_search:t.courseSearch||"",category_search:t.categorySearch||""})}catch(s){throw new Error(s.message||"Erro ao buscar cursos da oferta")}}async function ow(e){try{const t=["enableenddate","enddate","enablepreenrolment","preenrolmentstartdate","preenrolmentenddate","description","enableenrolperiod","enrolperiod","minusers","maxusers","roleid","enablereenrol","reenrolmentsituations","enableextension","extensionperiod","extensiondaysavailable","extensionmaxrequests","extensionallowedsituations"],s={optional_fields:{}};e.offercourseid?s.offercourseid=parseInt(e.offercourseid):console.error("offercourseid não está definido nos parâmetros"),e.classname?s.classname=e.classname:console.error("classname não está definido nos parâmetros"),e.startdate?s.startdate=e.startdate:console.error("startdate não está definido nos parâmetros"),e.teachers&&Array.isArray(e.teachers)?s.teachers=[...e.teachers]:(console.warn("teachers não está definido nos parâmetros ou não é um array"),s.teachers=[]),e.enrol?s.enrol=e.enrol:console.error("enrol não está definido nos parâmetros"),e.optional_fields&&t.forEach(u=>{if(u in e.optional_fields){const c=e.optional_fields[u];["enrolperiod","extensionperiod","extensiondaysavailable","extensionmaxrequests","minusers","maxusers"].includes(u)?c!==0&&c!==null&&c!==void 0&&c!==""&&(s.optional_fields[u]=c):typeof c=="boolean"?s.optional_fields[u]=c:Array.isArray(c)?c.length>0&&(s.optional_fields[u]=c):c!=null&&c!==""&&(s.optional_fields[u]=c)}});const n=["offercourseid","classname","startdate","enrol"].filter(u=>!s[u]);if(n.length>0)throw console.error("Campos obrigatórios ausentes no serviço:",n),new Error(`Campos obrigatórios ausentes: ${n.join(", ")}`);return await Fe("local_offermanager_add_class",s)}catch(t){throw console.error("Erro ao criar turma:",t),new Error(t.message||"Erro ao criar turma")}}async function hu(e){try{return await Fe("local_offermanager_get_class",{offerclassid:e})}catch(t){throw new Error(t.message||"Erro ao buscar turma")}}async function iw(e){try{const t=await Fe("local_offermanager_get_course",{offercourseid:e});return t.error?[]:t}catch(t){throw new Error(t.message||"Erro ao buscar curso da oferta")}}async function aw(e){try{const t=await Fe("local_offermanager_get_classes",{offercourseid:e});return Array.isArray(t)&&t.length===0?(console.log(`Curso ${e} não tem turmas (array vazio)`),[]):Array.isArray(t)&&t.length>0&&t[0].error===!1&&Array.isArray(t[0].data)&&t[0].data.length===0?(console.log(`Curso ${e} não tem turmas (data vazio)`),[]):t}catch(t){throw console.error(`Erro ao buscar turmas do curso ${e}:`,t),new Error(t.message||"Erro ao buscar curso da oferta")}}async function lw(e){try{const t=["enableenddate","enddate","enablepreenrolment","preenrolmentstartdate","preenrolmentenddate","description","enableenrolperiod","enrolperiod","minusers","maxusers","roleid","enablereenrol","reenrolmentsituations","enableextension","extensionperiod","extensiondaysavailable","extensionmaxrequests","extensionallowedsituations"],s={offerclassid:e.offerclassid,classname:e.classname,startdate:e.startdate,teachers:e.teachers,optional_fields:{}};return e.optional_fields&&t.forEach(n=>{if(n in e.optional_fields){const a=e.optional_fields[n];["enrolperiod","extensionperiod","extensiondaysavailable","extensionmaxrequests","minusers","maxusers"].includes(n)?a!==0&&a!==null&&a!==void 0&&a!==""&&(s.optional_fields[n]=a):typeof a=="boolean"?s.optional_fields[n]=a:Array.isArray(a)?a.length>0&&(s.optional_fields[n]=a):a!=null&&a!==""&&(s.optional_fields[n]=a)}}),console.log("Campos enviados para a API de atualização:",Object.keys(s.optional_fields)),console.log("Objeto completo enviado para a API de atualização:",s),"enrol"in s&&delete s.enrol,await Fe("local_offermanager_update_class",s)}catch(t){throw new Error(t.message||"Erro ao atualizar turma")}}async function uw(e){try{return await Fe("local_offermanager_delete_class",{offerclassid:e})}catch(t){throw new Error(t.message||"Erro ao excluir turma")}}async function cw(e,t=0,s="",i=[]){try{return await Fe("local_offermanager_get_potential_teachers",{offercourseid:e,search_string:s,offerclassid:t,excluded_userids:i})}catch(n){throw new Error(n.message||"Erro ao buscar professores")}}async function dw(){try{const e=await Fe("local_offermanager_get_situation_list",{});if(e.error)throw new Error(e.exception.message||"Erro ao buscar situações de matrícula");return e}catch(e){throw new Error(e.message||"Erro ao buscar situações de matrícula")}}async function fw(e,t){try{if(!t)throw new Error("É necessário especificar um curso de destino para duplicar a turma");const s=parseInt(e,10),i=parseInt(t,10);if(isNaN(s)||isNaN(i))throw new Error("IDs inválidos para duplicação de turma");return await Fe("local_offermanager_duplicate_class",{offerclassid:s,targetoffercourseid:i})}catch(s){throw new Error(s.message||"Erro ao duplicar turma")}}async function hw(e){try{const t=parseInt(e,10);if(isNaN(t))throw new Error("ID da turma inválido");const s=await Fe("local_offermanager_get_duplication_courses",{offerclassid:t});let i;return s&&s.data&&Array.isArray(s.data)?i=s.data:i=s,Array.isArray(i)?i.map(a=>({id:a.id,name:a.name||a.fullname,courseid:a.courseid||null,offercourseid:a.offercourseid||a.id,categoryid:a.categoryid||null,category_name:a.category_name||""})):[]}catch(t){try{const s=await Fe("local_offermanager_get_class",{id:parseInt(e,10)});let i,n;if(s&&s.data)i=s.data.offerid,n=s.data.offercourseid;else if(s)i=s.offerid,n=s.offercourseid;else throw new Error("Não foi possível determinar a oferta da turma");const a=await Fe("local_offermanager_get_offer_courses",{offerid:parseInt(i,10)});let u=[];return a&&Array.isArray(a.data)?u=a.data:a&&a.data&&Array.isArray(a.data.courses)?u=a.data.courses:Array.isArray(a)&&(u=a),u.filter(m=>(m.id||m.offercourseid)!=n).map(m=>({id:m.id,name:m.fullname||m.name,courseid:m.courseid||null,offercourseid:m.id,categoryid:m.categoryid||null,category_name:m.category_name||""}))}catch{throw new Error(t.message||"Erro ao buscar cursos para duplicação")}}}async function pu(e){try{const t=await Fe("local_offermanager_get_course_roles",{offercourseid:e});if(t.error)throw new Error(error.message||"Erro ao buscar papéis do curso");return t}catch(t){throw new Error(t.message||"Erro ao buscar papéis do curso")}}async function op(e=!0){try{return await Fe("local_offermanager_get_class_methods",{enabled:e})}catch(t){throw new Error(t.message||"Erro ao buscar métodos de inscrição")}}async function pw(e,t){try{return await Fe("local_offermanager_set_class_status",{id:e,status:t?1:0})}catch(s){throw new Error(s.message||"Erro ao alterar status da turma")}}const T3="",mw={name:"CustomTable",props:{headers:{type:Array,required:!0},items:{type:Array,required:!0},sortBy:{type:String,default:""},sortDesc:{type:Boolean,default:!1}},methods:{handleSort(e){this.$emit("sort",{sortBy:e,sortDesc:this.sortBy===e?!this.sortDesc:!1})}}},gw={class:"table-responsive"},vw={class:"table"},_w=["data-value"],bw=["onClick"],yw=["data-column"];function ww(e,t,s,i,n,a){return S(),O("div",gw,[d("table",vw,[d("thead",null,[d("tr",null,[(S(!0),O(Ie,null,at(s.headers,u=>(S(),O("th",{key:u.value,class:pe({"text-right":u.align==="right"}),style:us(u.width?{width:u.width}:{}),"data-value":u.value},[u.value==="select"?Rt(e.$slots,"header-select",{key:0},()=>[Ue(q(u.text),1)],!0):(S(),O(Ie,{key:1},[Ue(q(u.text)+" ",1),u.sortable?(S(),O("span",{key:0,onClick:c=>u.sortable?a.handleSort(u.value):null,class:"sort-icon"},[d("i",{class:pe(["fas",{"fa-sort":s.sortBy!==u.value,"fa-sort-up":s.sortBy===u.value&&!s.sortDesc,"fa-sort-down":s.sortBy===u.value&&s.sortDesc}])},null,2)],8,bw)):ee("",!0)],64))],14,_w))),128))])]),d("tbody",null,[(S(!0),O(Ie,null,at(s.items,u=>(S(),O("tr",{key:u.id},[(S(!0),O(Ie,null,at(s.headers,c=>(S(),O("td",{key:c.value,class:pe({"text-right":c.align==="right"}),"data-column":c.value},[Rt(e.$slots,"item-"+c.value,{item:u},()=>[Ue(q(u[c.value]),1)],!0)],10,yw))),128))]))),128))])])])}const hn=je(mw,[["render",ww],["__scopeId","data-v-c46cd2d8"]]),N3="",Ew={name:"CustomSelect",props:{modelValue:{type:[Number,String],default:""},options:{type:Array,required:!0},label:{type:String,default:""},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}}},methods:{handleChange(e){this.$emit("update:modelValue",e.target.value),this.hasError&&e.target.value&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate"]},Cw={class:"select-wrapper"},Dw=["value","disabled"],xw=["value"],Sw={key:1,class:"error-message"};function Ow(e,t,s,i,n,a){return S(),O("div",{ref:"selectContainer",class:"custom-select-container",style:us(a.customWidth)},[s.label?(S(),O("div",{key:0,class:pe(["select-label",{disabled:s.disabled}])},q(s.label),3)):ee("",!0),d("div",Cw,[d("select",{value:s.modelValue,onChange:t[0]||(t[0]=(...u)=>a.handleChange&&a.handleChange(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),class:pe(["custom-select",{error:s.hasError}]),disabled:s.disabled},[(S(!0),O(Ie,null,at(s.options,u=>(S(),O("option",{key:u.value,value:u.value},q(u.label),9,xw))),128))],42,Dw),d("div",{class:pe(["select-arrow",{disabled:s.disabled}])},null,2)]),s.hasError&&s.errorMessage?(S(),O("div",Sw,q(s.errorMessage),1)):ee("",!0)],4)}const mr=je(Ew,[["render",Ow],["__scopeId","data-v-bbc06e80"]]),I3="",Tw={name:"CustomInput",props:{modelValue:{type:[String,Number],default:""},label:{type:String,default:""},placeholder:{type:String,default:"Digite aqui..."},type:{type:String,default:"text"},hasSearchIcon:{type:Boolean,default:!1},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1},max:{type:[String,Number],default:null}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}},isDateType(){return this.type==="date"},isNumberType(){return this.type==="number"}},methods:{handleInput(e){let t=e.target.value;if(this.isNumberType&&(t.includes("-")&&(t=t.replace(/-/g,""),e.target.value=t),t!=="")){const s=parseFloat(t);s<0||isNaN(s)?(t="",e.target.value=t):this.max!==null&&s>parseFloat(this.max)&&(t=this.max.toString(),e.target.value=t,this.$emit("validate"))}this.$emit("update:modelValue",t),this.hasError&&t&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate"]},Nw={key:0,class:"input-label"},Iw=["type","placeholder","value","disabled","min","max"],Aw={key:0,class:"search-icon"},Mw={key:2,class:"error-message"};function Pw(e,t,s,i,n,a){return S(),O("div",{class:"custom-input-container",style:us(a.customWidth)},[s.label?(S(),O("div",Nw,q(s.label),1)):ee("",!0),d("div",{class:pe(["input-wrapper",{"with-icon":s.hasSearchIcon||a.isDateType}])},[d("input",{type:s.type,placeholder:s.placeholder,value:s.modelValue,onInput:t[0]||(t[0]=(...u)=>a.handleInput&&a.handleInput(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),disabled:s.disabled,class:pe(["form-control custom-input",{error:s.hasError}]),min:a.isNumberType?0:null,max:s.max},null,42,Iw),s.hasSearchIcon?(S(),O("div",Aw,t[2]||(t[2]=[d("i",{class:"fas fa-search"},null,-1)]))):ee("",!0),a.isDateType?(S(),O("div",{key:1,class:pe(["calendar-icon",{disabled:s.disabled}])},t[3]||(t[3]=[d("i",{class:"fas fa-calendar-alt"},null,-1)]),2)):ee("",!0),s.hasError&&s.errorMessage?(S(),O("div",Mw,q(s.errorMessage),1)):ee("",!0)],2)],4)}const Fo=je(Tw,[["render",Pw],["__scopeId","data-v-2acf0e85"]]),A3="",kw={name:"CustomCheckbox",props:{modelValue:{type:Boolean,default:!1},label:{type:String,default:""},id:{type:String,required:!0},disabled:{type:Boolean,default:!1}},emits:["update:modelValue"]},Vw=["id","checked","disabled"],Rw=["for"];function Uw(e,t,s,i,n,a){return S(),O("div",{class:pe(["checkbox-container",{disabled:s.disabled}])},[d("input",{type:"checkbox",id:s.id,checked:s.modelValue,onChange:t[0]||(t[0]=u=>e.$emit("update:modelValue",u.target.checked)),class:"custom-checkbox",disabled:s.disabled},null,40,Vw),d("label",{for:s.id,class:pe(["checkbox-label",{disabled:s.disabled}])},[Rt(e.$slots,"default",{},()=>[Ue(q(s.label),1)],!0)],10,Rw)],2)}const Xi=je(kw,[["render",Uw],["__scopeId","data-v-727d967e"]]),M3="",Fw={name:"CustomButton",props:{variant:{type:String,default:"primary",validator:e=>["primary","secondary","success","danger","warning","info"].includes(e)},label:{type:String,default:""},icon:{type:String,default:""},disabled:{type:Boolean,default:!1}},emits:["click"]},Lw=["disabled"],Bw={key:1};function $w(e,t,s,i,n,a){return S(),O("button",{class:pe(["custom-button",[`btn-${s.variant}`]]),disabled:s.disabled,onClick:t[0]||(t[0]=u=>e.$emit("click",u))},[s.icon?(S(),O("i",{key:0,class:pe(s.icon)},null,2)):ee("",!0),s.label?(S(),O("span",Bw,q(s.label),1)):ee("",!0),Rt(e.$slots,"default",{},void 0,!0)],10,Lw)}const Ln=je(Fw,[["render",$w],["__scopeId","data-v-36572ff9"]]),P3="",jw={name:"FilterSection",props:{title:{type:String,default:"FILTRO"},hasActiveTags:{type:Boolean,default:!1}}},Hw={class:"filter-section"},qw={key:0},zw={class:"filter-content"},Ww={key:1,class:"filter-tags"};function Gw(e,t,s,i,n,a){return S(),O("div",Hw,[s.title?(S(),O("h2",qw,q(s.title),1)):ee("",!0),d("div",zw,[Rt(e.$slots,"default",{},void 0,!0)]),s.hasActiveTags?(S(),O("div",Ww,[Rt(e.$slots,"tags",{},void 0,!0)])):ee("",!0)])}const ip=je(jw,[["render",Gw],["__scopeId","data-v-ef6fc6cc"]]),k3="",Kw={name:"FilterRow",props:{inline:{type:Boolean,default:!1}}};function Qw(e,t,s,i,n,a){return S(),O("div",{class:pe(["filter-row",{"filter-row-inline":s.inline}])},[Rt(e.$slots,"default",{},void 0,!0)],2)}const ea=je(Kw,[["render",Qw],["__scopeId","data-v-bd21f1e1"]]),V3="",Yw={name:"FilterGroup",props:{label:{type:String,default:""},isCheckbox:{type:Boolean,default:!1}}},Zw={key:0,class:"filter-label"},Jw={class:"filter-input"};function Xw(e,t,s,i,n,a){return S(),O("div",{class:pe(["filter-group",{"checkbox-group":s.isCheckbox}])},[s.label?(S(),O("div",Zw,q(s.label),1)):ee("",!0),d("div",Jw,[Rt(e.$slots,"default",{},void 0,!0)])],2)}const ta=je(Yw,[["render",Xw],["__scopeId","data-v-7df5c6a5"]]),R3="",e1={name:"FilterActions"},t1={class:"filter-actions"};function s1(e,t,s,i,n,a){return S(),O("div",t1,[Rt(e.$slots,"default",{},void 0,!0)])}const ap=je(e1,[["render",s1],["__scopeId","data-v-68346c90"]]),U3="",r1={name:"LFLoading",props:{isLoading:{type:Boolean,default:!1}}},n1={key:0};function o1(e,t,s,i,n,a){return S(),Ut(Uf,null,{default:Ne(()=>[s.isLoading?(S(),O("div",n1,t[0]||(t[0]=[d("div",{class:"modal-overlay"},null,-1),d("div",{class:"loader-wrapper"},[d("span",{class:"loader",role:"status"},[d("span",{class:"sr-only"},"Carregando...")])],-1)]))):ee("",!0)]),_:1})}const mu=je(r1,[["render",o1],["__scopeId","data-v-b3cb5b4c"]]),F3="",i1={name:"Toast",props:{show:{type:Boolean,required:!0},message:{type:String,required:!0},type:{type:String,default:"success",validator:function(e){return["success","error","warning","info"].includes(e)}},duration:{type:Number,default:3e3}},computed:{icon(){return{success:"fas fa-check-circle",error:"fas fa-exclamation-circle",warning:"fas fa-exclamation-triangle",info:"fas fa-info-circle"}[this.type]},progressStyle(){return{animation:`progress ${this.duration}ms linear`}}}},a1={class:"toast-content"};function l1(e,t,s,i,n,a){return S(),Ut(Zv,{to:"body"},[M(Uf,{name:"toast"},{default:Ne(()=>[s.show?(S(),O("div",{key:0,class:pe(["toast",s.type])},[d("div",a1,[d("i",{class:pe(a.icon)},null,2),d("span",null,q(s.message),1)]),d("div",{class:"toast-progress",style:us(a.progressStyle)},null,4)],2)):ee("",!0)]),_:1})])}const Lo=je(i1,[["render",l1],["__scopeId","data-v-4440998c"]]),L3="",u1={name:"Pagination",props:{currentPage:{type:Number,required:!0},perPage:{type:Number,required:!0},total:{type:Number,required:!0},perPageOptions:{type:Array,default:()=>[5,10,20,50]}},emits:["update:currentPage","update:perPage"],computed:{totalPages(){return Math.ceil(this.total/this.perPage)},visiblePages(){const t=Math.floor(2.5);let s=Math.max(1,this.currentPage-t),i=Math.min(this.totalPages,s+5-1);i-s+1<5&&(s=Math.max(1,i-5+1));const n=[];for(let a=s;a<=i;a++)n.push(a);return n},from(){return this.total===0?0:(this.currentPage-1)*this.perPage+1},to(){return Math.min(this.from+this.perPage-1,this.total)},perPageModel:{get(){return this.perPage},set(e){this.$emit("update:perPage",e)}}},methods:{handlePageChange(e){e>=1&&e<=this.totalPages&&this.$emit("update:currentPage",e)},handlePerPageChange(){this.$emit("update:currentPage",1)}}},c1={class:"pagination-container mt-3"},d1={class:"pagination-info"},f1=["value"],h1={class:"pagination-text"},p1={class:"pagination-controls"},m1=["disabled"],g1=["onClick"],v1=["disabled"];function _1(e,t,s,i,n,a){return S(),O("div",c1,[d("div",d1,[ut(d("select",{"onUpdate:modelValue":t[0]||(t[0]=u=>a.perPageModel=u),class:"per-page-select",onChange:t[1]||(t[1]=(...u)=>a.handlePerPageChange&&a.handlePerPageChange(...u))},[(S(!0),O(Ie,null,at(s.perPageOptions,u=>(S(),O("option",{key:u,value:u},q(u),9,f1))),128))],544),[[Kl,a.perPageModel]]),d("span",h1," Mostrando de "+q(a.from)+" até "+q(a.to)+" de "+q(s.total)+" resultados ",1)]),d("div",p1,[d("button",{class:"page-item",disabled:s.currentPage<=1,onClick:t[2]||(t[2]=u=>a.handlePageChange(s.currentPage-1))},t[4]||(t[4]=[d("i",{class:"fas fa-chevron-left"},null,-1)]),8,m1),(S(!0),O(Ie,null,at(a.visiblePages,u=>(S(),O("button",{key:u,class:pe(["page-item",{active:u===s.currentPage}]),onClick:c=>a.handlePageChange(u)},q(u),11,g1))),128)),d("button",{class:"page-item",disabled:s.currentPage>=a.totalPages,onClick:t[3]||(t[3]=u=>a.handlePageChange(s.currentPage+1))},t[5]||(t[5]=[d("i",{class:"fas fa-chevron-right"},null,-1)]),8,v1)])])}const pn=je(u1,[["render",_1],["__scopeId","data-v-5bcca60d"]]),B3="",b1={name:"PageHeader",props:{title:{type:String,required:!0}}},y1={class:"page-header"},w1={class:"header-actions"};function E1(e,t,s,i,n,a){return S(),O("div",y1,[d("h2",null,q(s.title),1),d("div",w1,[Rt(e.$slots,"actions",{},void 0,!0)])])}const sa=je(b1,[["render",E1],["__scopeId","data-v-7563ce12"]]),$3="",C1={name:"Modal",components:{CustomButton:Ln},props:{show:{type:Boolean,default:!1},size:{type:String,default:"md",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},showDefaultFooter:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Confirmar"},cancelButtonText:{type:String,default:"Cancelar"},confirmDisabled:{type:Boolean,default:!1}},emits:["close","confirm"],mounted(){document.addEventListener("keydown",this.handleKeyDown),this.show&&(document.body.style.overflow="hidden")},unmounted(){document.removeEventListener("keydown",this.handleKeyDown),document.body.style.overflow=""},watch:{show(e){document.body.style.overflow=e?"hidden":""}},methods:{handleKeyDown(e){this.show&&e.key==="Escape"&&this.$emit("close")}}},D1={class:"modal-body"},x1={key:0,class:"modal-footer"},S1={key:1,class:"modal-footer"};function O1(e,t,s,i,n,a){const u=X("custom-button");return s.show?(S(),O("div",{key:0,class:"modal-backdrop",onClick:t[3]||(t[3]=c=>s.closeOnBackdrop?e.$emit("close"):null)},[d("div",{class:pe(["modal-container",[`modal-${s.size}`]]),onClick:t[2]||(t[2]=Pt(()=>{},["stop"]))},[d("div",D1,[Rt(e.$slots,"default",{},void 0,!0)]),e.$slots.footer?(S(),O("div",x1,[Rt(e.$slots,"footer",{},void 0,!0)])):s.showDefaultFooter?(S(),O("div",S1,[M(u,{variant:"secondary",label:s.cancelButtonText,onClick:t[0]||(t[0]=c=>e.$emit("close"))},null,8,["label"]),M(u,{variant:"primary",label:s.confirmButtonText,onClick:t[1]||(t[1]=c=>e.$emit("confirm")),disabled:s.confirmDisabled},null,8,["label","disabled"])])):ee("",!0)],2)])):ee("",!0)}const T1=je(C1,[["render",O1],["__scopeId","data-v-784205f2"]]),j3="",N1={name:"ConfirmationModal",components:{Modal:T1},props:{show:{type:Boolean,default:!1},title:{type:String,default:"Confirmação"},message:{type:String,default:""},listTitle:{type:String,default:""},listItems:{type:Array,default:()=>[]},confirmButtonText:{type:String,default:"Confirmar"},cancelButtonText:{type:String,default:"Cancelar"},confirmDisabled:{type:Boolean,default:!1},icon:{type:String,default:"warning",validator:e=>["warning","info","error","success","question",""].includes(e)}},emits:["close","confirm"],computed:{iconClass(){return{warning:"fas fa-exclamation-triangle text-warning",info:"fas fa-info-circle text-info",error:"fas fa-times-circle text-danger",success:"fas fa-check-circle text-success",question:"fas fa-question-circle text-primary"}[this.icon]||""},hasListContent(){return this.listItems&&this.listItems.length>0}}},I1={key:0,class:"icon-container"},A1={class:"modal-custom-title"},M1={key:1,class:"message-list"},P1={key:0,class:"list-title"},k1={key:2,class:"message"},V1={class:"modal-custom-footer"},R1=["disabled"];function U1(e,t,s,i,n,a){const u=X("modal");return S(),Ut(u,{show:s.show,"confirm-button-text":s.confirmButtonText,"cancel-button-text":s.cancelButtonText,"confirm-disabled":s.confirmDisabled,size:"sm","show-default-footer":!1,onClose:t[2]||(t[2]=c=>e.$emit("close")),onConfirm:t[3]||(t[3]=c=>e.$emit("confirm"))},{default:Ne(()=>[d("div",{class:pe(["confirmation-content",{"has-list":a.hasListContent}])},[s.icon?(S(),O("div",I1,[d("i",{class:pe(a.iconClass)},null,2)])):ee("",!0),d("h3",A1,q(s.title),1),a.hasListContent?(S(),O("div",M1,[s.listTitle?(S(),O("p",P1,q(s.listTitle),1)):ee("",!0),d("ul",null,[(S(!0),O(Ie,null,at(s.listItems,(c,h)=>(S(),O("li",{key:h},q(c),1))),128))])])):(S(),O("div",k1,q(s.message),1)),d("div",V1,[d("button",{class:"btn-cancel",onClick:t[0]||(t[0]=c=>e.$emit("close"))},q(s.cancelButtonText),1),d("button",{class:"btn-danger",disabled:s.confirmDisabled,onClick:t[1]||(t[1]=c=>e.$emit("confirm"))},q(s.confirmButtonText),9,R1)])],2)]),_:1},8,["show","confirm-button-text","cancel-button-text","confirm-disabled"])}const gu=je(N1,[["render",U1],["__scopeId","data-v-ef02bc58"]]),H3="",q3="",F1={name:"OfferManagerView",components:{CustomTable:hn,CustomSelect:mr,CustomInput:Fo,CustomCheckbox:Xi,CustomButton:Ln,FilterSection:ip,FilterRow:ea,FilterGroup:ta,FilterActions:ap,Pagination:pn,PageHeader:sa,ConfirmationModal:gu,LFLoading:mu,Toast:Lo},setup(){return{router:Zi()}},mounted(){if(!document.querySelector('link[href*="font-awesome"]')){const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)}},data(){return{icons:{edit:Q0},inputFilters:{search:"",type:"",hideInactive:!1},appliedFilters:{search:"",type:"",hideInactive:!1},typeOptions:[],typeOptionsEnabled:!1,tableHeaders:[{text:"NOME DA OFERTA",value:"name",sortable:!0},{text:"DESCRIÇÃO",value:"description",sortable:!0},{text:"STATUS DA OFERTA",value:"status",sortable:!0},{text:"TIPO DA OFERTA",value:"type",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1}],offers:[],totalOffers:0,loading:!1,error:null,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,currentPage:1,perPage:10,sortBy:"name",sortDesc:!1,showDeleteModal:!1,offerToDelete:null,showStatusModal:!1,selectedOffer:null}},computed:{typeSelectOptions(){return[{value:"",label:"Todos"},...this.typeOptions]},hasActiveFilters(){return this.appliedFilters.search||this.appliedFilters.hideInactive}},watch:{perPage(e,t){e!==t&&(this.currentPage=1,this.loadOffers())},currentPage(e,t){e!==t&&this.loadOffers()}},async created(){this.debouncedSearch=Xh.debounce(this.handleSearchInput,300),this.loadTypeOptions(),this.loadOffers()},methods:{getTypeLabel(e){if(!e)return"";const t=this.typeOptions.find(s=>s.value===e||s.label===e);return t?t.label:e},async loadTypeOptions(){const e=await du();e.data.types&&(this.typeOptionsEnabled=e.data.enabled,e.data.default&&(this.inputFilters.type=e.data.default),this.typeOptions=e.data.types.map(t=>({value:t,label:t})))},async loadOffers(){this.loading=!0,this.error=null;const e={search:this.appliedFilters.search||"",type:this.appliedFilters.type||null,onlyActive:this.appliedFilters.hideInactive===!0,page:this.currentPage,perPage:this.perPage,sortBy:this.sortBy,sortDesc:this.sortDesc},t=await Y0(e);if(t.error)throw new Error(t.message||"Erro ao carregar ofertas");this.offers=t.data.offers||[],this.totalOffers=t.data.total_items||0,this.loading=!1},async handlePageChange(e){e!==this.currentPage&&(this.currentPage=e,await this.loadOffers())},async handlePerPageChange(e){e!==this.perPage&&(this.perPage=e,this.currentPage=1,await this.loadOffers())},async clearFilters(){this.inputFilters.type,this.inputFilters={search:"",type:"",hideInactive:!1},this.appliedFilters={search:"",type:"",hideInactive:!1},this.currentPage=1,await this.loadOffers()},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,this.loadOffers()},createNewOffer(){this.router.push({name:"nova-oferta"})},editOffer(e){this.router.push({name:"editar-oferta",params:{id:e.id.toString()}})},deleteOffer(e){e.can_delete&&(this.offerToDelete=e,this.showDeleteModal=!0)},async confirmDeleteOffer(){if(this.offerToDelete)try{this.loading=!0,await Z0(this.offerToDelete.id),await this.loadOffers(),this.showSuccessMessage(`Oferta "${this.offerToDelete.name}" excluída com sucesso`),this.offerToDelete=null,this.showDeleteModal=!1}catch(e){this.error=e.message,this.showErrorMessage(`Erro ao excluir oferta "${this.offerToDelete.name}"`)}finally{this.loading=!1}},toggleOfferStatus(e){e.status===0&&!e.can_activate||(this.selectedOffer=e,this.showStatusModal=!0)},async confirmToggleStatus(){if(this.selectedOffer)try{this.loading=!0,await sw(this.selectedOffer.id,this.selectedOffer.status===1),await this.loadOffers(),this.showSuccessMessage(this.selectedOffer.status===1?`Oferta "${this.selectedOffer.name}" inativada com sucesso`:`Oferta "${this.selectedOffer.name}" inativada com sucesso`),this.selectedOffer=null,this.showStatusModal=!1}catch(e){this.error=e.message,this.showErrorMessage(this.selectedOffer.status===1?`Erro ao inativar oferta "${this.selectedOffer.name}"`:`Erro ao ativar oferta "${this.selectedOffer.name}"`)}finally{this.loading=!1}},getStatusButtonTitle(e){return e.status===1?"Desativar":e.can_activate?"Ativar":"Não é possível ativar esta oferta"},async handleTypeChange(e){this.appliedFilters.type=e,this.currentPage=1,await this.loadOffers()},async handleHideInactiveChange(e){const t=e===!0;this.inputFilters.hideInactive=t,this.appliedFilters.hideInactive=t,this.currentPage=1,await this.loadOffers()},async handleSearchInput(){(this.inputFilters.search.length>=3||this.inputFilters.search==="")&&(this.appliedFilters.search=this.inputFilters.search,this.currentPage=1,await this.loadOffers())},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}}},L1={id:"offer-manager-component",class:"offer-manager"},B1={class:"new-offer-container"},$1={key:0,class:"alert alert-danger"},j1={class:"table-container"},H1=["title"],q1={class:"action-buttons"},z1=["onClick"],W1=["onClick","disabled","title"],G1={key:0,class:"fas fa-eye"},K1={key:1,class:"fas fa-eye-slash"},Q1=["onClick","disabled","title"];function Y1(e,t,s,i,n,a){var ye,Z,he,be,Ae,ue;const u=X("CustomButton"),c=X("PageHeader"),h=X("CustomCheckbox"),m=X("FilterGroup"),p=X("CustomInput"),v=X("CustomSelect"),w=X("FilterActions"),D=X("FilterRow"),k=X("FilterSection"),L=X("CustomTable"),re=X("Pagination"),I=X("ConfirmationModal"),ne=X("LFLoading"),Q=X("Toast");return S(),O("div",L1,[M(c,{title:"Gerenciar Ofertas"},{actions:Ne(()=>[d("div",B1,[M(u,{variant:"primary",label:"Nova Oferta",onClick:a.createNewOffer},null,8,["onClick"])])]),_:1}),M(k,{title:"FILTRO"},{default:Ne(()=>[M(D,{inline:!0,class:"offer-manager-filters"},{default:Ne(()=>[M(m,{"is-checkbox":!0,class:"checkbox-filter-group"},{default:Ne(()=>[M(h,{modelValue:n.inputFilters.hideInactive,"onUpdate:modelValue":[t[0]||(t[0]=ae=>n.inputFilters.hideInactive=ae),a.handleHideInactiveChange],id:"hideInactive",label:"Não exibir inativas"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),M(m,{label:"Oferta",class:"search-filter-group"},{default:Ne(()=>[M(p,{modelValue:n.inputFilters.search,"onUpdate:modelValue":t[1]||(t[1]=ae=>n.inputFilters.search=ae),placeholder:"Buscar...",width:339,"has-search-icon":!0,onInput:e.debouncedSearch},null,8,["modelValue","onInput"])]),_:1}),n.typeOptionsEnabled?(S(),Ut(m,{key:0,label:"Tipo",class:"type-filter-group"},{default:Ne(()=>[M(v,{modelValue:n.inputFilters.type,"onUpdate:modelValue":[t[2]||(t[2]=ae=>n.inputFilters.type=ae),a.handleTypeChange],options:a.typeSelectOptions,width:144},null,8,["modelValue","options","onUpdate:modelValue"])]),_:1})):ee("",!0),M(w,{class:"actions-filter-group"},{default:Ne(()=>[M(u,{variant:"secondary",label:"Limpar",onClick:a.clearFilters},null,8,["onClick"])]),_:1})]),_:1})]),_:1}),n.error?(S(),O("div",$1,[t[7]||(t[7]=d("i",{class:"fas fa-exclamation-circle"},null,-1)),Ue(" "+q(n.error),1)])):ee("",!0),d("div",j1,[M(L,{headers:n.tableHeaders,items:n.offers,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"item-description":Ne(({item:ae})=>[d("span",{title:ae.description},q(ae.description.length>50?ae.description.slice(0,50)+"...":ae.description),9,H1)]),"item-type":Ne(({item:ae})=>[Ue(q(ae.type.charAt(0).toUpperCase()+ae.type.slice(1)),1)]),"item-status":Ne(({item:ae})=>[Ue(q(ae.status===1?"Ativa":"Inativa"),1)]),"item-actions":Ne(({item:ae})=>[d("div",q1,[d("button",{class:"btn-action btn-edit",onClick:V=>a.editOffer(ae),title:"Editar"},t[8]||(t[8]=[d("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[d("g",{"clip-path":"url(#clip0_9_197955)"},[d("path",{d:"M12.854 0.145905C12.7602 0.0521694 12.6331 -0.000488281 12.5005 -0.000488281C12.3679 -0.000488281 12.2408 0.0521694 12.147 0.145905L10.5 1.7929L14.207 5.49991L15.854 3.8539C15.9006 3.80746 15.9375 3.75228 15.9627 3.69154C15.9879 3.63079 16.0009 3.56567 16.0009 3.4999C16.0009 3.43414 15.9879 3.36902 15.9627 3.30827C15.9375 3.24753 15.9006 3.19235 15.854 3.1459L12.854 0.145905ZM13.5 6.2069L9.793 2.4999L3.293 8.9999H3.5C3.63261 8.9999 3.75978 9.05258 3.85355 9.14635C3.94732 9.24012 4 9.3673 4 9.4999V9.9999H4.5C4.63261 9.9999 4.75978 10.0526 4.85355 10.1464C4.94732 10.2401 5 10.3673 5 10.4999V10.9999H5.5C5.63261 10.9999 5.75978 11.0526 5.85355 11.1464C5.94732 11.2401 6 11.3673 6 11.4999V11.9999H6.5C6.63261 11.9999 6.75978 12.0526 6.85355 12.1464C6.94732 12.2401 7 12.3673 7 12.4999V12.7069L13.5 6.2069ZM6.032 13.6749C6.01095 13.619 6.00012 13.5597 6 13.4999V12.9999H5.5C5.36739 12.9999 5.24021 12.9472 5.14644 12.8535C5.05268 12.7597 5 12.6325 5 12.4999V11.9999H4.5C4.36739 11.9999 4.24021 11.9472 4.14644 11.8535C4.05268 11.7597 4 11.6325 4 11.4999V10.9999H3.5C3.36739 10.9999 3.24021 10.9472 3.14644 10.8535C3.05268 10.7597 3 10.6325 3 10.4999V9.9999H2.5C2.44022 9.99981 2.38094 9.98897 2.325 9.96791L2.146 10.1459C2.09835 10.1939 2.06093 10.251 2.036 10.3139L0.0359968 15.3139C-0.000373859 15.4048 -0.00927736 15.5043 0.0103901 15.6002C0.0300575 15.6961 0.077431 15.7841 0.146638 15.8533C0.215844 15.9225 0.30384 15.9698 0.399716 15.9895C0.495593 16.0092 0.595133 16.0003 0.685997 15.9639L5.686 13.9639C5.74886 13.939 5.80601 13.9016 5.854 13.8539L6.032 13.6759V13.6749Z",fill:"var(--primary)"})]),d("defs",null,[d("clipPath",{id:"clip0_9_197955"},[d("rect",{width:"16",height:"16",fill:"white"})])])],-1)]),8,z1),d("button",{class:pe(["btn-action",ae.status===1?"btn-deactivate":"btn-activate"]),onClick:V=>a.toggleOfferStatus(ae),disabled:ae.status===0&&!ae.can_activate,title:a.getStatusButtonTitle(ae)},[ae.status===1?(S(),O("i",G1)):(S(),O("i",K1))],10,W1),d("button",{class:"btn-action btn-delete",onClick:V=>a.deleteOffer(ae),disabled:!ae.can_delete,title:ae.can_delete?"Excluir":"Não é possível excluir esta oferta"},t[9]||(t[9]=[d("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,Q1)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"])]),M(re,{"current-page":n.currentPage,"onUpdate:currentPage":t[3]||(t[3]=ae=>n.currentPage=ae),"per-page":n.perPage,"onUpdate:perPage":t[4]||(t[4]=ae=>n.perPage=ae),total:n.totalOffers,loading:n.loading},null,8,["current-page","per-page","total","loading"]),M(I,{show:n.showDeleteModal,title:"A exclusão desta instância de oferta é uma ação irreversível.",message:"Todos os cursos vinculados serão desassociados e as turmas relacionadas serão removidas. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir Oferta","cancel-button-text":"Cancelar",icon:"warning",onClose:t[5]||(t[5]=ae=>n.showDeleteModal=!1),onConfirm:a.confirmDeleteOffer},null,8,["show","onConfirm"]),M(I,{show:n.showStatusModal,title:((ye=n.selectedOffer)==null?void 0:ye.status)===1?"Ao inativar esta oferta, os cursos e as turmas associadas serão tratados da seguinte forma:":"Confirmar Ativação",message:((Z=n.selectedOffer)==null?void 0:Z.status)===1?"":"Tem certeza que deseja ativar esta oferta?","list-title":((he=n.selectedOffer)==null?void 0:he.status)===1?"Comportamento para os cursos, turmas e matrículas:":"","list-items":((be=n.selectedOffer)==null?void 0:be.status)===1?["Todos os cursos contidos na oferta não serão mais disponibilizados para os usuários da oferta, mas as turmas e matrículas permanecerão ativas.","Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.","Novos alunos não poderão ser inscritos através da oferta."]:[],"confirm-button-text":((Ae=n.selectedOffer)==null?void 0:Ae.status)===1?"Inativar oferta":"Ativar","cancel-button-text":"Cancelar",icon:((ue=n.selectedOffer)==null?void 0:ue.status)===1?"warning":"question",onClose:t[6]||(t[6]=ae=>n.showStatusModal=!1),onConfirm:a.confirmToggleStatus},null,8,["show","title","message","list-title","list-items","confirm-button-text","icon","onConfirm"]),M(ne,{"is-loading":n.loading},null,8,["is-loading"]),M(Q,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])])}const Z1=je(F1,[["render",Y1],["__scopeId","data-v-3bd3228c"]]);async function J1(e={}){try{const t=await Fe("local_offermanager_fetch_enrolments",{offerclassid:e.offerclassid,userids:e.userids||[],page:e.page||1,perpage:e.perpage||20,orderby:e.orderby||"fullname",direction:e.direction||"ASC"});if(t.error)throw new Error(error.message||"Erro ao buscar matrículas");return t}catch(t){throw new Error(t.message||"Erro ao buscar matrículas")}}async function vu(e={}){try{const t=await Fe("local_offermanager_get_enroled_users",{offerclassid:e.offerclassid,searchstring:e.searchstring||"",fieldstring:e.fieldstring||"name",excludeduserids:e.excludeduserids||[]});if(t.error)throw new Error(t.message||"Erro ao buscar opções de filtro");return t}catch{throw new Error(response.message||"Erro ao buscar opções de filtro")}}async function X1(e={}){try{const t=await Fe("local_offermanager_enrol_users",{offerclassid:e.offerclassid,userids:e.userids||[],roleid:e.roleid||5});if(t.error)throw new Error(t.message||"Erro ao matricular usuários");return t}catch{throw new Error(response.message||"Erro ao matricular usuários")}}async function eE(e,t="",s){const i=await Fe("local_offermanager_get_potential_users_to_enrol",{offerclassid:e,search_string:t,excluded_userids:s});return i.error?(console.error("Erro na resposta de getPotentialUsersToEnrol:",i.error),[]):i}async function tE(e={}){try{const t=await Fe("local_offermanager_edit_offer_user_enrol",{offeruserenrolid:e.offeruserenrolid,status:e.status,timestart:e.timestart,timeend:e.timeend,roleid:e.roleid});return t==null?(console.error("Resposta vazia de editEnrolment"),!1):typeof t=="boolean"?t:t&&typeof t.success=="boolean"?t.success:t&&t.error===!1?t.data===!0:(console.warn("Formato de resposta não reconhecido:",t),!1)}catch(t){return console.error("Erro ao editar matrícula:",t),!1}}async function sE(e={}){try{const t=await Fe("local_offermanager_edit_offer_user_enrol_bulk",{offeruserenrolids:e.offeruserenrolids||[],status:e.status,timestart:e.timestart,timeend:e.timeend});return t&&t.error===!1&&t.data===!0?e.offeruserenrolids.map(i=>({id:i,operation_status:!0})):Array.isArray(t)?t:t&&Array.isArray(t.data)?t.data:(console.error("Formato de resposta não reconhecido:",t),[])}catch(t){throw console.error("Erro ao editar matrículas em lote:",t),t}}async function rE(e,t){try{const s=await Fe("local_offermanager_cancel_enrolment",{offeruserenrolid:e,reason:t});if(s.error)throw new Error(s.message||"Erro ao cancelar matrícula");return s.data}catch(s){throw new Error(s.message||"Erro ao cancelar matrícula")}}async function nE(e){try{const t=await Fe("local_offermanager_delete_offer_user_enrol_bulk",{offeruserenrolids:e});return t&&t.error===!1&&t.data===!0?e.map(i=>({id:i,operation_status:!0})):Array.isArray(t)?t:t&&Array.isArray(t.data)?t.data:(console.warn("Formato de resposta não reconhecido:",t),[])}catch(t){return console.error("Erro ao excluir matrículas em lote:",t),[]}}async function oE(e){try{const t=await Fe("local_offermanager_get_roles",{offeruserenrolid:e});return t?t.error?(console.error("Erro na resposta de getUserRoles:",t.error),[]):Array.isArray(t)?t:t&&Array.isArray(t.data)?t.data:[]:(console.error("Resposta vazia de getUserRoles"),[])}catch(t){return console.error("Erro ao buscar papéis do usuário:",t),[]}}async function iE(e,t){try{const s=await Fe("local_offermanager_update_roles",{offeruserenrolid:e,roleids:Array.isArray(t)?t:[t]});return s==null?(console.error("Resposta vazia de updateUserRoles"),!1):typeof s=="boolean"?s:s&&typeof s.success=="boolean"?s.success:s&&s.error===!1?s.data===!0:(console.warn("Formato de resposta não reconhecido:",s),!1)}catch(s){return console.error("Erro ao atualizar papéis do usuário:",s),!1}}const z3="",aE={name:"HierarchicalSelect",props:{modelValue:{type:String,default:""},options:{type:Array,required:!0},label:{type:String,default:""},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}}},methods:{handleChange(e){this.$emit("update:modelValue",e.target.value),this.$emit("navigate",e.target.value),this.hasError&&e.target.value&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate","navigate"]},lE={class:"select-wrapper"},uE=["value","disabled"],cE=["label"],dE=["value"],fE={key:1,class:"error-message"};function hE(e,t,s,i,n,a){return S(),O("div",{ref:"selectContainer",class:"hierarchical-select-container",style:us(a.customWidth)},[s.label?(S(),O("div",{key:0,class:pe(["select-label",{disabled:s.disabled}])},q(s.label),3)):ee("",!0),d("div",lE,[d("select",{value:s.modelValue,onChange:t[0]||(t[0]=(...u)=>a.handleChange&&a.handleChange(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),class:pe(["hierarchical-select",{error:s.hasError}]),disabled:s.disabled},[(S(!0),O(Ie,null,at(s.options,u=>(S(),O("optgroup",{key:u.value,label:u.label},[(S(!0),O(Ie,null,at(u.children,c=>(S(),O("option",{key:c.value,value:c.value,class:"child-option"},q(c.label),9,dE))),128))],8,cE))),128))],42,uE),d("div",{class:pe(["select-arrow",{disabled:s.disabled}])},null,2)]),s.hasError&&s.errorMessage?(S(),O("div",fE,q(s.errorMessage),1)):ee("",!0)],4)}const pE=je(aE,[["render",hE],["__scopeId","data-v-b5d38077"]]),W3="",mE={name:"FilterTag",emits:["remove"]};function gE(e,t,s,i,n,a){return S(),O("div",{class:"tag badge badge-primary",onClick:t[0]||(t[0]=u=>e.$emit("remove"))},[t[1]||(t[1]=d("i",{class:"fas fa-times"},null,-1)),Rt(e.$slots,"default",{},void 0,!0)])}const Bo=je(mE,[["render",gE],["__scopeId","data-v-fe063554"]]),G3="",vE={name:"FilterTags"},_E={class:"filter-tags"};function bE(e,t,s,i,n,a){return S(),O("div",_E,[Rt(e.$slots,"default",{},void 0,!0)])}const ra=je(vE,[["render",bE],["__scopeId","data-v-d8e54e5f"]]),K3="",yE={name:"Autocomplete",components:{FilterTag:Bo,FilterTags:ra},props:{modelValue:{type:[Array,String,Number],default:()=>[]},items:{type:Array,default:()=>[]},placeholder:{type:String,default:""},label:{type:String,default:""},width:{type:[Number,String],default:"auto"},required:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},minChars:{type:Number,default:3},showAllOption:{type:Boolean,default:!1},inputMaxWidth:{type:[String,Number],default:null},autoOpen:{type:Boolean,default:!0},noResultsText:{type:String,default:"Nenhum item disponível"},hasSearchIcon:{type:Boolean,default:!1},showFilterTags:{type:Boolean,default:!0},showSelectedInInput:{type:Boolean,default:!1},maxLabelLength:{type:Number,default:30},loading:{type:Boolean,default:!1},keepOpenOnSelect:{type:Boolean,default:!1}},emits:["update:modelValue","select","select-all","load-more","search"],data(){return{searchQuery:"",isOpen:!1,selectedIndex:-1,internalItems:[],uniqueId:`autocomplete-${Math.random().toString(36).substring(2,9)}`,focusedOptionIndex:-1,blurTimeout:null,debouncedSearch:null}},computed:{displayItems(){let e=this.internalItems;if(this.searchQuery){const t=this.searchQuery.toLowerCase();e=this.internalItems.filter(s=>s.label.toLowerCase().includes(t))}return this.showAllOption&&Array.isArray(this.modelValue)?[{label:"Todos",value:"__ALL__"},...e]:e},inputMaxWidthStyle(){return this.inputMaxWidth?typeof this.inputMaxWidth=="number"?`${this.inputMaxWidth}px`:this.inputMaxWidth:null},getSelectedItemLabel(){if(!this.modelValue)return"";const e=this.internalItems.find(t=>t.value===this.modelValue);return e?e.label:""}},created(){this.debouncedSearch=Xh.debounce(e=>{this.$emit("search",e)},300)},watch:{items:{handler(e){this.internalItems=Array.isArray(e)?[...e]:[],this.autoOpen&&this.keepOpenOnSelect&&!this.disabled&&this.internalItems.length>0&&this.$refs.inputElement===document.activeElement&&(this.isOpen=!0)},immediate:!0,deep:!0},searchQuery(e){this.isOpen=!0,this.selectedIndex=-1,(e.length===0||e.length>=this.minChars)&&this.debouncedSearch(e)}},methods:{handleFocus(){this.autoOpen&&!this.disabled&&(this.isOpen=!0,this.selectedIndex=-1,this.searchQuery&&(this.searchQuery="",this.$emit("search",""))),this.blurTimeout&&(clearTimeout(this.blurTimeout),this.blurTimeout=null)},openDropdown(){this.disabled||(this.isOpen=!0)},handleBlur(){this.blurTimeout=setTimeout(()=>{this.$el.contains(document.activeElement)||(this.isOpen=!1,this.selectedIndex=-1)},150)},handleInput(){this.disabled||(this.isOpen=!0)},selectItem(e){if(e.value==="__ALL__"){Array.isArray(this.modelValue)&&this.$emit("select-all"),this.searchQuery="",this.isOpen=!1,this.selectedIndex=-1,this.$nextTick(()=>{this.focusInput()});return}if(Array.isArray(this.modelValue)){const t=[...this.modelValue],s=t.findIndex(i=>i.value===e.value);s===-1?t.push(e):t.splice(s,1),this.$emit("update:modelValue",t)}else this.$emit("update:modelValue",e.value),this.$emit("select",e);this.searchQuery="",this.isOpen=!!this.keepOpenOnSelect,this.selectedIndex=-1,this.$nextTick(()=>{this.autoOpen&&this.focusInput()})},removeItem(e){if(Array.isArray(this.modelValue)){const t=this.modelValue.filter(s=>s.value!==e.value);this.$emit("update:modelValue",t)}else this.$emit("update:modelValue","");Array.isArray(this.modelValue)?(this.searchQuery="",this.isOpen=!1,this.selectedIndex=-1):(this.selectedIndex=-1,this.$nextTick(()=>{this.isOpen=!1})),this.$nextTick(()=>{this.focusInput()})},removeSelectedItem(){this.$emit("update:modelValue",""),this.searchQuery="",this.selectedIndex=-1,this.$nextTick(()=>{this.focusInput()})},handleKeydown(e){if(!this.isOpen&&e.key!=="Tab"){this.isOpen=!0;return}switch(e.key){case"ArrowDown":e.preventDefault(),e.stopPropagation(),this.selectedIndex=Math.min(this.selectedIndex+1,this.displayItems.length-1),this.focusOption(this.selectedIndex);break;case"ArrowUp":e.preventDefault(),e.stopPropagation(),this.selectedIndex=Math.max(this.selectedIndex-1,-1),this.selectedIndex===-1?this.focusInput():this.focusOption(this.selectedIndex);break;case"Enter":e.preventDefault(),this.selectedIndex>=0?this.selectItem(this.displayItems[this.selectedIndex]):this.searchQuery&&this.searchQuery.length>=this.minChars&&this.$emit("search",this.searchQuery);break;case"Escape":e.preventDefault(),this.isOpen=!1,this.selectedIndex=-1;break;case"Tab":this.isOpen&&!e.shiftKey&&this.displayItems.length>0&&(e.preventDefault(),e.stopPropagation(),this.selectedIndex=0,this.focusOption(0));break}},handleOptionKeydown(e,t,s){switch(e.key){case"ArrowDown":e.preventDefault(),e.stopPropagation(),s<this.displayItems.length-1&&(this.selectedIndex=s+1,this.focusOption(this.selectedIndex));break;case"ArrowUp":e.preventDefault(),e.stopPropagation(),s>0?(this.selectedIndex=s-1,this.focusOption(this.selectedIndex)):(this.selectedIndex=-1,this.focusInput());break;case"Enter":case" ":e.preventDefault(),this.selectItem(t);break;case"Escape":e.preventDefault(),this.isOpen=!1,this.selectedIndex=-1,this.focusInput();break;case"Tab":e.shiftKey?(e.preventDefault(),e.stopPropagation(),s>0?(this.selectedIndex=s-1,this.focusOption(this.selectedIndex)):(this.selectedIndex=-1,this.focusInput())):(e.preventDefault(),e.stopPropagation(),s<this.displayItems.length-1?(this.selectedIndex=s+1,this.focusOption(this.selectedIndex)):(this.selectedIndex=0,this.focusOption(0)));break}},focusInput(){this.$refs.inputElement&&this.$refs.inputElement.focus()},focusOption(e){requestAnimationFrame(()=>{var s;const t=(s=this.$refs.optionElements)==null?void 0:s[e];t&&t.focus()})},handleClickOutside(e){this.$el.contains(e.target)||(this.isOpen=!1,this.selectedIndex=-1)},truncateLabel(e){return e?e.length<=this.maxLabelLength?e:e.substring(0,this.maxLabelLength)+"...":""},handleScroll(e){if(!e||!e.target)return;const t=e.target;t.scrollHeight&&t.scrollTop!==void 0&&t.clientHeight&&t.scrollHeight-t.scrollTop-t.clientHeight<50&&this.$emit("load-more")}},mounted(){document.addEventListener("click",this.handleClickOutside),this.autoOpen&&!this.disabled&&this.internalItems.length>0&&this.$nextTick(()=>{this.isOpen=!0})},beforeUnmount(){document.removeEventListener("click",this.handleClickOutside)}},wE={class:"autocomplete-container"},EE=["id"],CE={class:"autocomplete-wrapper"},DE=["placeholder","disabled","aria-expanded","aria-owns","aria-labelledby","aria-controls"],xE={key:0,class:"selected-item"},SE=["title"],OE=["id"],TE=["id","data-index","aria-selected","tabindex","onClick","onKeydown","title"],NE={class:"item-label"},IE={key:0,class:"fas fa-check"},AE={key:0,class:"dropdown-item loading-item"},ME={key:1,class:"dropdown-item no-results"},PE={key:0,class:"tags-container"};function kE(e,t,s,i,n,a){const u=X("FilterTag"),c=X("FilterTags");return S(),O("div",wE,[s.label?(S(),O("label",{key:0,class:pe(["filter-label",{required:s.required}]),id:`${n.uniqueId}-label`},q(s.label),11,EE)):ee("",!0),d("div",CE,[d("div",{class:"input-container",style:us({maxWidth:a.inputMaxWidthStyle})},[d("div",{class:pe(["input-wrapper",{"has-search-icon":s.hasSearchIcon,"has-selected-item":s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!n.isOpen&&!n.searchQuery}])},[ut(d("input",{type:"text",class:"form-control",placeholder:s.placeholder,"onUpdate:modelValue":t[0]||(t[0]=h=>n.searchQuery=h),disabled:s.disabled,"aria-expanded":n.isOpen,"aria-owns":`${n.uniqueId}-listbox`,"aria-labelledby":s.label?`${n.uniqueId}-label`:void 0,"aria-autocomplete":"list","aria-controls":`${n.uniqueId}-listbox`,role:"combobox",tabindex:"0",onKeydown:t[1]||(t[1]=(...h)=>a.handleKeydown&&a.handleKeydown(...h)),onFocus:t[2]||(t[2]=h=>!s.disabled&&a.handleFocus),onInput:t[3]||(t[3]=(...h)=>a.handleInput&&a.handleInput(...h)),onClick:t[4]||(t[4]=h=>!s.disabled&&a.openDropdown()),onBlur:t[5]||(t[5]=(...h)=>a.handleBlur&&a.handleBlur(...h)),ref:"inputElement"},null,40,DE),[[Xt,n.searchQuery]]),s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!n.isOpen&&!n.searchQuery?(S(),O("div",xE,[d("span",{class:"selected-text",title:a.getSelectedItemLabel},q(a.truncateLabel(a.getSelectedItemLabel)),9,SE),d("i",{class:"fas fa-times remove-selected",onClick:t[6]||(t[6]=Pt((...h)=>a.removeSelectedItem&&a.removeSelectedItem(...h),["stop"]))})])):ee("",!0),s.hasSearchIcon&&!(s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!n.isOpen&&!n.searchQuery)?(S(),O("i",{key:1,class:pe(["search-icon",{"fas fa-search":!s.loading,"spinner-border spinner-border-sm":s.loading}])},null,2)):ee("",!0)],2),n.isOpen?(S(),O("div",{key:0,class:"dropdown-menu show",id:`${n.uniqueId}-listbox`,role:"listbox",tabindex:"-1",ref:"dropdownMenu",onScroll:t[7]||(t[7]=(...h)=>a.handleScroll&&a.handleScroll(...h))},[a.displayItems.length>0?(S(),O(Ie,{key:0},[(S(!0),O(Ie,null,at(a.displayItems,(h,m)=>(S(),O("div",{key:h.value==="__ALL__"?"__ALL__":h.value,class:pe(["dropdown-item",{active:n.selectedIndex===m,selected:h.value!=="__ALL__"&&(Array.isArray(s.modelValue)?s.modelValue.some(p=>p.value===h.value):s.modelValue===h.value)}]),id:`${n.uniqueId}-option-${m}`,role:"option","data-index":m,"aria-selected":n.selectedIndex===m,tabindex:n.selectedIndex===m?0:-1,onClick:p=>a.selectItem(h),onKeydown:p=>a.handleOptionKeydown(p,h,m),ref_for:!0,ref:"optionElements",title:h.label},[d("span",NE,q(a.truncateLabel(h.label)),1),h.value!=="__ALL__"&&Array.isArray(s.modelValue)&&s.modelValue.some(p=>p.value===h.value)?(S(),O("i",IE)):ee("",!0)],42,TE))),128)),s.loading?(S(),O("div",AE,t[8]||(t[8]=[d("span",null,"Carregando mais itens...",-1)]))):ee("",!0)],64)):(S(),O("div",ME,q(s.noResultsText||"Nenhum item disponível"),1))],40,OE)):ee("",!0)],4),s.showFilterTags&&Array.isArray(s.modelValue)&&s.modelValue.length>0?(S(),O("div",PE,[M(c,null,{default:Ne(()=>[(S(!0),O(Ie,null,at(s.modelValue,h=>(S(),Ut(u,{key:h.value,onRemove:m=>a.removeItem(h)},{default:Ne(()=>[Ue(q(h.label),1)]),_:2},1032,["onRemove"]))),128))]),_:1})])):ee("",!0)])])}const $o=je(yE,[["render",kE],["__scopeId","data-v-86d58871"]]),Q3="",VE={name:"EnrolmentModalNew",components:{Toast:Lo,CustomSelect:mr},props:{show:{type:Boolean,default:!1},title:{type:String,default:"Matricular usuários na turma"},size:{type:String,default:"lg",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Salvar"},cancelButtonText:{type:String,default:"Cancelar"},offerclassid:{type:Number,required:!0},roles:{type:Array,required:!0}},emits:["close","success"],data(){return{enrolmentMethod:"manual",enrolmentMethodOptions:[{value:"manual",label:"Manual"},{value:"batch",label:"Em lote"}],selectedRoleId:"",searchQuery:"",isOpen:!1,userOptions:[],selectedUsers:[],debounceTimer:null,selectedFile:null,csvUsers:[],isDragging:!1,csvDelimiter:",",csvEncoding:"UTF-8",delimiterOptions:[{value:",",label:","},{value:";",label:";"},{value:":",label:":"},{value:"	",label:"\\t"},{value:" ",label:"Espaço"}],encodingOptions:[{value:"UTF-8",label:"UTF-8"},{value:"WINDOWS-1252",label:"WINDOWS-1252"},{value:"ISO-8859-1",label:"ISO-8859-1"},{value:"ASCII",label:"ASCII"},{value:"ISO-8859-2",label:"ISO-8859-2"},{value:"ISO-8859-3",label:"ISO-8859-3"},{value:"ISO-8859-4",label:"ISO-8859-4"},{value:"ISO-8859-5",label:"ISO-8859-5"},{value:"ISO-8859-6",label:"ISO-8859-6"},{value:"ISO-8859-7",label:"ISO-8859-7"},{value:"ISO-8859-8",label:"ISO-8859-8"},{value:"ISO-8859-9",label:"ISO-8859-9"},{value:"ISO-8859-10",label:"ISO-8859-10"},{value:"ISO-8859-13",label:"ISO-8859-13"},{value:"ISO-8859-14",label:"ISO-8859-14"},{value:"ISO-8859-15",label:"ISO-8859-15"},{value:"ISO-8859-16",label:"ISO-8859-16"},{value:"WINDOWS-874",label:"WINDOWS-874"},{value:"WINDOWS-1250",label:"WINDOWS-1250"},{value:"WINDOWS-1251",label:"WINDOWS-1251"},{value:"WINDOWS-1253",label:"WINDOWS-1253"},{value:"WINDOWS-1254",label:"WINDOWS-1254"},{value:"WINDOWS-1255",label:"WINDOWS-1255"},{value:"WINDOWS-1256",label:"WINDOWS-1256"},{value:"WINDOWS-1257",label:"WINDOWS-1257"},{value:"WINDOWS-1258",label:"WINDOWS-1258"},{value:"KOI8-R",label:"KOI8-R"},{value:"MACINTOSH",label:"MACINTOSH"},{value:"IBM866",label:"IBM866"},{value:"BIG5",label:"BIG5"},{value:"EUC-JP",label:"EUC-JP"},{value:"SHIFT_JIS",label:"SHIFT_JIS"},{value:"EUC-KR",label:"EUC-KR"},{value:"UTF-7",label:"UTF-7"},{value:"UTF-16",label:"UTF-16"},{value:"UTF-32",label:"UTF-32"},{value:"UCS-2",label:"UCS-2"},{value:"UCS-4",label:"UCS-4"}],loadingUsers:!1,isSubmitting:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,showResultAlerts:!1,batchMessage:"",batchMessageType:"success",failedMessages:[],reenrolMessages:[]}},computed:{isFormValid(){return this.roles.length===0?!1:this.enrolmentMethod==="manual"?this.selectedUsers.length>0&&this.selectedRoleId:this.enrolmentMethod==="batch"?this.csvUsers.length>0&&this.selectedRoleId:!1}},watch:{show(e){document.body.style.overflow=e?"hidden":"",e&&this.initializeForm()}},mounted(){document.addEventListener("keydown",this.handleKeyDown),document.addEventListener("click",this.handleClickOutside),this.show&&(document.body.style.overflow="hidden",this.initializeForm())},unmounted(){document.removeEventListener("keydown",this.handleKeyDown),document.removeEventListener("click",this.handleClickOutside),document.body.style.overflow=""},methods:{handleKeyDown(e){this.show&&e.key==="Escape"&&this.$emit("close")},handleClickOutside(e){if(this.show===!1)return;const t=document.querySelector(".custom-autocomplete-wrapper");t&&!t.contains(e.target)&&(this.isOpen=!1)},async initializeForm(){this.resetForm()},resetForm(){let e=this.roles.find(t=>t.value==5);this.enrolmentMethod="manual",this.selectedRoleId=e.value,this.searchQuery="",this.selectedUsers=[],this.selectedFile=null,this.csvUsers=[],this.csvDelimiter=",",this.csvEncoding="UTF-8",this.showResultAlerts=!1,this.batchMessage="",this.batchMessageType="success",this.failedMessages=[],this.reenrolMessages=[]},async fetchPotentialUsersToEnrol(e){this.loadingUsers=!0;let t=this.selectedUsers.map(i=>i.value);const s=await eE(this.offerclassid,e,t);this.userOptions=s.data.map(i=>({value:i.id,label:i.fullname})),this.loadingUsers=!1},handleInput(){const e=this.searchQuery.trim();this.debounceTimer&&clearTimeout(this.debounceTimer),e.length>=3?this.debounceTimer=setTimeout(async()=>{await this.fetchPotentialUsersToEnrol(e),this.userOptions&&(this.isOpen=!0)},500):(this.isOpen=!1,this.userOptions=[])},selectUser(e){const t=this.selectedUsers.findIndex(s=>s.value===e.value);t===-1?this.selectedUsers.push(e):this.selectedUsers.splice(t,1),this.searchQuery="",this.isOpen=!1},removeUser(e){this.selectedUsers=this.selectedUsers.filter(t=>t.value!==e.value)},onDragOver(){this.isDragging=!0},onDragLeave(){this.isDragging=!1},onDrop(e){this.isDragging=!1;const t=e.dataTransfer.files;t.length>0&&this.processFile(t[0])},handleFileSelect(e){const t=e.target.files;t.length>0&&this.processFile(t[0])},removeFile(){this.selectedFile=null,this.csvUsers=[],this.$refs.fileInput&&(this.$refs.fileInput.value="")},processFile(e){if(e.type!=="text/csv"&&!e.name.endsWith(".csv")){this.showErrorMessage("Por favor, selecione um arquivo CSV válido.");return}this.selectedFile=e,this.readCSVFile(e)},readCSVFile(e){const t=new FileReader;t.onload=s=>{const i=s.target.result;this.parseCSV(i)},t.onerror=s=>{if(console.error("Erro ao ler o arquivo:",s),this.csvEncoding!=="UTF-8"){console.log("Tentando ler com UTF-8 como fallback...");const i=new FileReader;i.onload=n=>{const a=n.target.result;this.parseCSV(a)},i.onerror=()=>{this.showErrorMessage("Não foi possível ler o arquivo. Verifique se o formato e a codificação estão corretos.")},i.readAsText(e,"UTF-8")}else this.showErrorMessage("Não foi possível ler o arquivo. Verifique se o formato está correto.")};try{t.readAsText(e,this.csvEncoding)}catch(s){console.error("Erro ao tentar ler o arquivo com a codificação selecionada:",s),this.showErrorMessage(`Erro ao ler o arquivo com a codificação ${this.csvEncoding}. Tente selecionar outra codificação.`)}},parseCSV(e){try{const t=this.csvDelimiter,s=/�/.test(e);s&&console.warn("O arquivo contém caracteres inválidos. Pode haver um problema com a codificação selecionada.");const i=e.split(/\r?\n/),n=[];if(i.length<2){console.log("EnrolmentModalNew - Linhas do CSV:",i),this.showErrorMessage("O arquivo CSV deve conter pelo menos uma linha de cabeçalho e uma linha de dados.");return}const a=(m,p)=>{if(p==="\\t")return m.split("	");if(p===" ")return m.split(/\s+/);{const v=p.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");return m.split(new RegExp(v))}},u=a(i[0].toLowerCase(),t);if(u.length<2||!u.some(m=>m.includes("userid"))||!u.some(m=>m.includes("firstname"))){this.showErrorMessage("O arquivo CSV deve conter colunas para UserID e firstname do usuário.");return}const c=u.findIndex(m=>m.includes("userid")),h=u.findIndex(m=>m.includes("firstname"));for(let m=1;m<i.length;m++){const p=i[m].trim();if(!p)continue;const v=a(p,t);if(v.length>Math.max(c,h)){const w=v[c].trim(),D=v[h].trim();if(w&&D){if(!/^\d+$/.test(w)){console.warn(`Linha ${m+1}: ID inválido '${w}'. Deve ser um número.`);continue}n.push({id:w,name:D})}}}if(n.length===0){s?this.showErrorMessage("Nenhum usuário válido encontrado no arquivo CSV. Pode haver um problema com a codificação selecionada. Tente selecionar outra codificação."):this.showErrorMessage("Nenhum usuário válido encontrado no arquivo CSV. Verifique o formato do arquivo.");return}this.csvUsers=n}catch(t){console.error("Erro ao processar arquivo CSV:",t),this.showErrorMessage("Erro ao processar o arquivo CSV. Verifique o formato e a codificação e tente novamente.")}},formatFileSize(e){if(e===0)return"0 Bytes";const t=1024,s=["Bytes","KB","MB","GB","TB"],i=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,i)).toFixed(2))+" "+s[i]},async handleSubmit(){if(this.isFormValid)try{this.isSubmitting=!0;let e=[];this.enrolmentMethod==="manual"?e=this.selectedUsers.map(s=>s.value):this.enrolmentMethod==="batch"&&(e=this.csvUsers.map(s=>parseInt(s.id))),e||this.showErrorMessage("Nenhum usuário selecionado para efetuar a matrícula");const t=await X1({offerclassid:this.offerclassid,userids:e,roleid:parseInt(this.selectedRoleId)});if(t.data){this.showResultAlerts=!0;const s=t.data.filter(u=>u.success),i=s.length,n=i>0?s.filter(u=>u.reenrol):[],a=t.data.filter(u=>u.success==!1);this.batchMessage=i>0?`${i} de ${e.length} usuário(s) matriculado(s) com sucesso.`:"Nenhuma inscrição foi realizada",this.batchMessageType=i>0?"success":"danger",this.reenrolMessages=n.length>0?n.map(u=>u.message):[],this.failedMessages=a.length>0?a.map(u=>u.message):[],i>0&&this.$emit("success",{count:i,total:e.length})}}catch(e){this.showErrorMessage(e.message||"Erro ao matricular usuários. Tente novamente.")}finally{this.isSubmitting=!1}},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}}},RE={class:"modal-header"},UE={class:"modal-title"},FE={class:"modal-body"},LE={key:0,class:"loading-overlay"},BE={key:1,class:"result-alerts"},$E={key:1,class:"failed-messages"},jE={key:2,class:"reenrol-messages"},HE={key:2,class:"enrolment-modal"},qE={class:"form-row"},zE={class:"form-group"},WE={class:"limited-width-input"},GE={class:"form-group"},KE={class:"limited-width-input"},QE={key:0,class:"error-message"},YE={key:0,class:"form-group"},ZE={class:"user-select-container"},JE={class:"custom-autocomplete-wrapper"},XE={key:0,class:"dropdown-menu show"},eC=["onClick"],tC={key:0,class:"fas fa-check"},sC={key:0,class:"selected-users-container"},rC={class:"filter-tags"},nC=["onClick"],oC={key:1,class:"form-group"},iC={class:"file-name"},aC={class:"file-size"},lC={key:0,class:"csv-users-preview"},uC={class:"preview-header"},cC={class:"selected-users-container"},dC={class:"filter-tags"},fC={key:0,class:"more-users"},hC={class:"csv-info"},pC={class:"csv-example"},mC=["href"],gC={class:"csv-options-row"},vC={class:"csv-option"},_C={class:"csv-option"},bC={key:0,class:"modal-footer"},yC=["disabled"];function wC(e,t,s,i,n,a){const u=X("CustomSelect"),c=X("Toast");return S(),O(Ie,null,[s.show?(S(),O("div",{key:0,class:"modal-backdrop",onClick:t[15]||(t[15]=h=>s.closeOnBackdrop?e.$emit("close"):null)},[d("div",{class:pe(["modal-container",[`modal-${s.size}`]]),onClick:t[14]||(t[14]=Pt(()=>{},["stop"]))},[d("div",RE,[d("h3",UE,q(s.title),1),d("button",{class:"modal-close",onClick:t[0]||(t[0]=h=>e.$emit("close"))},t[16]||(t[16]=[d("i",{class:"fas fa-times"},null,-1)]))]),d("div",FE,[n.isSubmitting?(S(),O("div",LE,t[17]||(t[17]=[d("div",{class:"loading-content"},[d("div",{class:"spinner-border text-primary",role:"status"},[d("span",{class:"sr-only"},"Carregando...")]),d("p",{class:"loading-text mt-3"},"Processando matrículas...")],-1)]))):ee("",!0),n.showResultAlerts?(S(),O("div",BE,[n.batchMessage?(S(),O("div",{key:0,class:pe(["alert",n.batchMessageType==="success"?"alert-success":"alert-danger"])},[d("i",{class:pe(n.batchMessageType==="success"?"fas fa-check-circle":"fas fa-exclamation-triangle")},null,2),Ue(" "+q(n.batchMessage),1)],2)):ee("",!0),n.failedMessages.length>0?(S(),O("div",$E,[(S(!0),O(Ie,null,at(n.failedMessages,(h,m)=>(S(),O("div",{key:m,class:"alert alert-warning"},[t[18]||(t[18]=d("i",{class:"fas fa-exclamation-triangle"},null,-1)),Ue(" "+q(h),1)]))),128))])):ee("",!0),n.reenrolMessages.length>0?(S(),O("div",jE,[(S(!0),O(Ie,null,at(n.reenrolMessages,(h,m)=>(S(),O("div",{key:m,class:"alert alert-info"},[t[19]||(t[19]=d("i",{class:"fas fa-exclamation-triangle"},null,-1)),Ue(" "+q(h),1)]))),128))])):ee("",!0)])):(S(),O("div",HE,[t[34]||(t[34]=d("h3",{class:"section-title"},"OPÇÕES DE MATRÍCULA",-1)),d("div",qE,[d("div",zE,[t[20]||(t[20]=d("div",{class:"label-with-help"},[d("label",{class:"form-label"},"Forma de matrícula"),d("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),d("div",WE,[M(u,{modelValue:n.enrolmentMethod,"onUpdate:modelValue":t[1]||(t[1]=h=>n.enrolmentMethod=h),options:n.enrolmentMethodOptions,style:{width:"100%"},required:""},null,8,["modelValue","options"])])]),d("div",GE,[t[21]||(t[21]=d("div",{class:"label-with-help"},[d("label",{class:"form-label"},"Papel para atribuir"),d("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),d("div",KE,[M(u,{modelValue:n.selectedRoleId,"onUpdate:modelValue":t[2]||(t[2]=h=>n.selectedRoleId=h),options:s.roles,class:"w-100",required:""},null,8,["modelValue","options"]),s.roles.length===0?(S(),O("div",QE," Não foi possível carregar os papéis disponíveis para esta turma. ")):ee("",!0)])])]),n.enrolmentMethod==="manual"?(S(),O("div",YE,[t[24]||(t[24]=d("div",{class:"label-with-help"},[d("label",{class:"form-label"},"Selecionar usuários"),d("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),d("div",ZE,[d("div",JE,[ut(d("input",{type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[3]||(t[3]=h=>n.searchQuery=h),onInput:t[4]||(t[4]=(...h)=>a.handleInput&&a.handleInput(...h))},null,544),[[Xt,n.searchQuery]]),t[22]||(t[22]=d("div",{class:"select-arrow"},null,-1)),n.isOpen?(S(),O("div",XE,[(S(!0),O(Ie,null,at(n.userOptions,(h,m)=>(S(),O("div",{key:h.value,class:"dropdown-item",onClick:p=>a.selectUser(h)},[Ue(q(h.label)+" ",1),n.selectedUsers.some(p=>p.value===h.value)?(S(),O("i",tC)):ee("",!0)],8,eC))),128))])):ee("",!0)])]),n.selectedUsers.length>0?(S(),O("div",sC,[d("div",rC,[(S(!0),O(Ie,null,at(n.selectedUsers,h=>(S(),O("div",{key:h.value,class:"tag badge badge-primary",onClick:m=>a.removeUser(h)},[t[23]||(t[23]=d("i",{class:"fas fa-times"},null,-1)),Ue(" "+q(h.label),1)],8,nC))),128))])])):ee("",!0)])):ee("",!0),n.enrolmentMethod==="batch"?(S(),O("div",oC,[t[33]||(t[33]=d("div",{class:"label-with-help"},[d("label",{class:"form-label"},"Matricular usuários a partir de um arquivo CSV"),d("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),d("div",{class:pe(["csv-upload-area",{"drag-over":n.isDragging}]),onDragover:t[6]||(t[6]=Pt((...h)=>a.onDragOver&&a.onDragOver(...h),["prevent"])),onDragleave:t[7]||(t[7]=Pt((...h)=>a.onDragLeave&&a.onDragLeave(...h),["prevent"])),onDrop:t[8]||(t[8]=Pt((...h)=>a.onDrop&&a.onDrop(...h),["prevent"])),onClick:t[9]||(t[9]=h=>e.$refs.fileInput.click())},[d("input",{type:"file",ref:"fileInput",accept:".csv",style:{display:"none"},onChange:t[5]||(t[5]=(...h)=>a.handleFileSelect&&a.handleFileSelect(...h))},null,544),n.selectedFile?(S(),O(Ie,{key:1},[t[27]||(t[27]=d("div",{class:"file-icon"},[d("i",{class:"fas fa-file-alt"})],-1)),d("p",iC,q(n.selectedFile.name),1),d("p",aC," ("+q(a.formatFileSize(n.selectedFile.size))+") ",1),t[28]||(t[28]=d("p",{class:"file-replace-text"}," Clique ou arraste outro arquivo para substituir ",-1))],64)):(S(),O(Ie,{key:0},[t[25]||(t[25]=d("div",{class:"upload-icon"},[d("i",{class:"fas fa-arrow-down"})],-1)),t[26]||(t[26]=d("p",{class:"upload-text"}," Você pode arrastar e soltar arquivos aqui para adicioná-los. ",-1))],64))],34),n.csvUsers.length>0?(S(),O("div",lC,[d("div",uC,[d("span",null,"Usuários encontrados no arquivo ("+q(n.csvUsers.length)+"):",1)]),d("div",cC,[d("div",dC,[(S(!0),O(Ie,null,at(n.csvUsers.slice(0,5),h=>(S(),O("div",{key:h.id,class:"tag badge badge-primary"},q(h.name),1))),128)),n.csvUsers.length>5?(S(),O("span",fC,"+"+q(n.csvUsers.length-5)+" mais",1)):ee("",!0)])])])):ee("",!0),d("div",hC,[t[32]||(t[32]=d("p",{class:"csv-format-text"},"Formatos aceitos: CSV",-1)),d("div",pC,[t[29]||(t[29]=d("span",{class:"example-label"},"Exemplo CSV",-1)),d("a",{href:`/local/offermanager/export_potential_users.php?offerclassid=${s.offerclassid}`,class:"example-csv"},"example.csv",8,mC)]),d("div",gC,[d("div",vC,[t[30]||(t[30]=d("label",null,"Delimitador do CSV",-1)),M(u,{modelValue:n.csvDelimiter,"onUpdate:modelValue":t[10]||(t[10]=h=>n.csvDelimiter=h),options:n.delimiterOptions,width:160},null,8,["modelValue","options"])]),d("div",_C,[t[31]||(t[31]=d("label",null,"Codificação",-1)),M(u,{modelValue:n.csvEncoding,"onUpdate:modelValue":t[11]||(t[11]=h=>n.csvEncoding=h),options:n.encodingOptions,width:160},null,8,["modelValue","options"])])])])])):ee("",!0),t[35]||(t[35]=d("div",{class:"form-info"},[d("span",{style:{color:"#f8f9fa","font-size":"15px"}},"Este formulário contém campos obrigatórios marcados com"),d("i",{class:"fas fa-exclamation-circle",style:{color:"#dc3545","font-size":"0.85rem","vertical-align":"middle"}})],-1))]))]),n.showResultAlerts?ee("",!0):(S(),O("div",bC,[d("button",{class:"btn btn-primary",onClick:t[12]||(t[12]=(...h)=>a.handleSubmit&&a.handleSubmit(...h)),disabled:n.isSubmitting||!a.isFormValid},q(s.confirmButtonText),9,yC),d("button",{class:"btn btn-secondary",onClick:t[13]||(t[13]=h=>e.$emit("close"))},q(s.cancelButtonText),1)]))],2)])):ee("",!0),M(c,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])],64)}const EC=je(VE,[["render",wC],["__scopeId","data-v-6eeb7b8d"]]),Y3="",CC={name:"EnrollmentDetailsModal",props:{show:{type:Boolean,default:!1},user:{type:Object,default:null},courseName:{type:String,default:""}},emits:["close"],methods:{getEnrolmentMethod(e){if(console.log("EnrollmentDetailsModal - Método de inscrição recebido:",e),!e)return"Não disponível";switch(e){case"offer_manual":return"Inscrição manual";case"offer_self":return"Autoinscrição";default:return e}}}},DC={class:"modal-header"},xC={key:0,class:"modal-body"},SC={class:"details-container"},OC={class:"detail-row"},TC={class:"detail-value"},NC={class:"detail-row"},IC={class:"detail-value"},AC={class:"detail-row"},MC={class:"detail-value"},PC={class:"detail-row"},kC={class:"detail-value"},VC={class:"detail-row"},RC={class:"detail-value"},UC={key:1,class:"modal-body no-data"},FC={class:"modal-footer"};function LC(e,t,s,i,n,a){return s.show?(S(),O("div",{key:0,class:"modal-backdrop",onClick:t[3]||(t[3]=u=>e.$emit("close"))},[d("div",{class:"modal-container",onClick:t[2]||(t[2]=Pt(()=>{},["stop"]))},[d("div",DC,[t[5]||(t[5]=d("h3",{class:"modal-title"},"Informações da matrícula",-1)),d("button",{class:"modal-close",onClick:t[0]||(t[0]=u=>e.$emit("close"))},t[4]||(t[4]=[d("i",{class:"fas fa-times"},null,-1)]))]),s.user?(S(),O("div",xC,[d("div",SC,[d("div",OC,[t[6]||(t[6]=d("div",{class:"detail-label"},"Nome completo",-1)),d("div",TC,q(s.user.fullName),1)]),d("div",NC,[t[7]||(t[7]=d("div",{class:"detail-label"},"Curso",-1)),d("div",IC,q(s.courseName),1)]),d("div",AC,[t[8]||(t[8]=d("div",{class:"detail-label"},"Método de inscrição",-1)),d("div",MC,q(a.getEnrolmentMethod(s.user.enrol)),1)]),d("div",PC,[t[9]||(t[9]=d("div",{class:"detail-label"},"Estado",-1)),d("div",kC,[d("span",{class:pe(["status-tag",s.user.status===0?"status-ativo":"status-inativo"])},q(s.user.statusName),3)])]),d("div",VC,[t[10]||(t[10]=d("div",{class:"detail-label"},"Matrícula criada",-1)),d("div",RC,q(s.user.createdDate),1)])])])):(S(),O("div",UC,"Nenhum dado disponível")),d("div",FC,[d("button",{class:"btn btn-secondary",onClick:t[1]||(t[1]=u=>e.$emit("close"))}," Cancelar ")])])])):ee("",!0)}const BC=je(CC,[["render",LC],["__scopeId","data-v-030365c3"]]),Z3="",$C={name:"EditEnrollmentModal",components:{CustomSelect:mr},props:{show:{type:Boolean,default:!1},user:{type:Object,default:null},offerclassid:{type:[Number,String],required:!0}},emits:["close","success","error"],data(){return{isSubmitting:!1,formData:{status:"1",enableStartDate:!0,startDateStr:"",startTimeStr:"00:00",enableEndDate:!1,endDateStr:"",endTimeStr:"00:00",validityPeriod:"unlimited"},statusOptions:[{value:0,label:"Ativo"},{value:1,label:"Suspenso"}],validityPeriodOptions:[{value:"unlimited",label:"Ilimitado"},...Array.from({length:365},(e,t)=>{const s=t+1;return{value:s.toString(),label:s===1?"1 dia":`${s} dias`}})]}},watch:{show(e){e&&this.user&&this.initializeForm()},user(e){e&&this.show&&this.initializeForm()}},methods:{getEnrolmentMethod(e){if(!e)return"Não disponível";switch(e){case"offer_manual":return"Inscrição manual";case"offer_self":return"Autoinscrição";default:return e}},initializeForm(){if(!this.user)return;this.formData.status=this.user.status;const e=this.user.timestart,t=e?new Date(e*1e3):new Date;this.formData.startDateStr=this.formatDateForInput(t),this.formData.startTimeStr=this.formatTimeForInput(t),this.formData.enableStartDate=!0;const s=this.validityPeriodOptions.filter(i=>i.value!=="unlimited");if(this.user.timeend){const i=new Date(this.user.timeend*1e3);this.formData.endDateStr=this.formatDateForInput(i),this.formData.endTimeStr=this.formatTimeForInput(i),this.formData.enableEndDate=this.user.timeend>0;const a=i-t,u=Math.ceil(a/(1e3*60*60*24)),c=s.find(h=>parseInt(h.value)===u);this.formData.validityPeriod=c?c.value:"unlimited"}else{const i=new Date;i.setMonth(i.getMonth()+3),this.formData.endDateStr=this.formatDateForInput(i),this.formData.endTimeStr=this.formatTimeForInput(i),this.formData.validityPeriod="unlimited",this.formData.enableEndDate=!1}},handleValidityPeriodChange(){if(this.formData.validityPeriod!=="unlimited"){this.formData.enableEndDate=!1;const e=this.formData.enableStartDate&&this.formData.startDateStr?new Date(this.formData.startDateStr):new Date,t=parseInt(this.formData.validityPeriod),s=new Date(e);s.setDate(s.getDate()+t),this.formData.endDateStr=this.formatDateForInput(s),this.formData.endTimeStr=this.formData.startTimeStr}},handleEnableEndDateChange(){this.formData.enableEndDate&&(this.formData.validityPeriod="unlimited")},formatDateForInput(e){const t=e.getFullYear(),s=String(e.getMonth()+1).padStart(2,"0"),i=String(e.getDate()).padStart(2,"0");return`${t}-${s}-${i}`},formatTimeForInput(e){return`${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}`},async saveChanges(){var e;if((e=this.user)!=null&&e.offeruserenrolid)try{this.isSubmitting=!0;const t=Number(this.formData.status)||0,s=this.getStartTimestamp(),i=this.getEndTimestamp(s);if(s>i&&i!==0){this.$emit("error","A data de início da matrícula deve ser menor que a data de fim da matrícula.");return}await tE({offeruserenrolid:this.user.offeruserenrolid,status:t,timestart:s,timeend:i})?(this.$emit("success",{userId:this.user.id,offeruserenrolid:this.user.offeruserenrolid,status:t,timestart:s,timeend:i}),this.$emit("close")):this.$emit("error","Não foi possível editar a matrícula. Por favor, tente novamente.")}catch{this.$emit("error","Ocorreu um erro ao editar a matrícula. Por favor, tente novamente.")}finally{this.isSubmitting=!1}},getStartTimestamp(){if(this.formData.enableStartDate&&this.formData.startDateStr){const e=this.parseDateTime(this.formData.startDateStr,this.formData.startTimeStr);return Math.floor(e.getTime()/1e3)}return 0},getEndTimestamp(e){if(this.formData.enableEndDate&&this.formData.endDateStr){const t=this.parseDateTime(this.formData.endDateStr,this.formData.endTimeStr);return Math.floor(t.getTime()/1e3)}if(this.formData.validityPeriod!=="unlimited"){const t=parseInt(this.formData.validityPeriod);if(this.formData.enableStartDate&&this.formData.startDateStr){const s=this.parseDateTime(this.formData.startDateStr,this.formData.startTimeStr),i=new Date(s);return i.setDate(i.getDate()+t),Math.floor(i.getTime()/1e3)}}return 0},parseDateTime(e,t){const[s,i,n]=e.split("-").map(Number),[a,u]=t.split(":").map(Number);return new Date(s,i-1,n,a,u,0,0)}}},jC={class:"modal-header"},HC={class:"modal-title"},qC={class:"modal-body"},zC={class:"enrollment-form"},WC={class:"form-row"},GC={class:"form-value"},KC={class:"form-row"},QC={class:"form-field"},YC={class:"select-wrapper"},ZC={class:"form-row"},JC={class:"form-field date-time-field"},XC={class:"date-field"},eD={class:"time-field"},tD={class:"enable-checkbox"},sD={class:"form-row"},rD={class:"form-field"},nD={class:"select-wrapper"},oD={class:"form-row"},iD={class:"date-field"},aD=["disabled"],lD={class:"time-field"},uD=["disabled"],cD={class:"enable-checkbox"},dD={class:"form-row"},fD={class:"form-value"},hD={class:"modal-footer"},pD={class:"footer-buttons"},mD=["disabled"];function gD(e,t,s,i,n,a){const u=X("CustomSelect");return s.show?(S(),O("div",{key:0,class:"modal-backdrop",onClick:t[15]||(t[15]=c=>e.$emit("close"))},[d("div",{class:"modal-container",onClick:t[14]||(t[14]=Pt(()=>{},["stop"]))},[d("div",jC,[d("h3",HC," Editar matrícula de "+q(s.user?s.user.fullName:""),1),d("button",{class:"modal-close",onClick:t[0]||(t[0]=c=>e.$emit("close"))},t[16]||(t[16]=[d("i",{class:"fas fa-times"},null,-1)]))]),d("div",qC,[d("div",zC,[d("div",WC,[t[17]||(t[17]=d("div",{class:"form-label"},"Método de inscrição",-1)),d("div",GC,q(a.getEnrolmentMethod(s.user&&s.user.enrol?s.user.enrol:"")),1)]),d("div",KC,[t[18]||(t[18]=d("div",{class:"form-label"},"Estado",-1)),d("div",QC,[d("div",YC,[M(u,{modelValue:n.formData.status,"onUpdate:modelValue":t[1]||(t[1]=c=>n.formData.status=c),options:n.statusOptions,width:120,class:"smaller-select"},null,8,["modelValue","options"])])])]),d("div",ZC,[t[20]||(t[20]=d("div",{class:"form-label"},"Matrícula começa",-1)),d("div",JC,[d("div",XC,[ut(d("input",{type:"date","onUpdate:modelValue":t[2]||(t[2]=c=>n.formData.startDateStr=c),class:"form-control",onChange:t[3]||(t[3]=(...c)=>e.handleStartDateChange&&e.handleStartDateChange(...c))},null,544),[[Xt,n.formData.startDateStr]])]),d("div",eD,[ut(d("input",{type:"time","onUpdate:modelValue":t[4]||(t[4]=c=>n.formData.startTimeStr=c),class:"form-control",onChange:t[5]||(t[5]=(...c)=>e.handleStartTimeChange&&e.handleStartTimeChange(...c))},null,544),[[Xt,n.formData.startTimeStr]])]),d("div",tD,[ut(d("input",{type:"checkbox",id:"enable-start-date","onUpdate:modelValue":t[6]||(t[6]=c=>n.formData.enableStartDate=c),class:"custom-checkbox"},null,512),[[$i,n.formData.enableStartDate]]),t[19]||(t[19]=d("label",{for:"enable-start-date"},"Habilitar",-1))])])]),d("div",sD,[t[21]||(t[21]=d("div",{class:"form-label"},"Período de validade da matrícula",-1)),d("div",rD,[d("div",nD,[M(u,{modelValue:n.formData.validityPeriod,"onUpdate:modelValue":t[7]||(t[7]=c=>n.formData.validityPeriod=c),options:n.validityPeriodOptions,width:120,class:"smaller-select",onChange:a.handleValidityPeriodChange,disabled:n.formData.enableEndDate},null,8,["modelValue","options","onChange","disabled"])])])]),d("div",oD,[t[23]||(t[23]=d("div",{class:"form-label"},"Matrícula termina",-1)),d("div",{class:pe(["form-field date-time-field",{"disabled-inputs-only":!n.formData.enableEndDate}])},[d("div",iD,[ut(d("input",{type:"date","onUpdate:modelValue":t[8]||(t[8]=c=>n.formData.endDateStr=c),class:"form-control",disabled:!n.formData.enableEndDate},null,8,aD),[[Xt,n.formData.endDateStr]])]),d("div",lD,[ut(d("input",{type:"time","onUpdate:modelValue":t[9]||(t[9]=c=>n.formData.endTimeStr=c),class:"form-control",disabled:!n.formData.enableEndDate},null,8,uD),[[Xt,n.formData.endTimeStr]])]),d("div",cD,[ut(d("input",{type:"checkbox",id:"enable-enddate","onUpdate:modelValue":t[10]||(t[10]=c=>n.formData.enableEndDate=c),class:"custom-checkbox",onChange:t[11]||(t[11]=(...c)=>a.handleEnableEndDateChange&&a.handleEnableEndDateChange(...c))},null,544),[[$i,n.formData.enableEndDate]]),t[22]||(t[22]=d("label",{for:"enable-enddate"},"Habilitar",-1))])],2)]),d("div",dD,[t[24]||(t[24]=d("div",{class:"form-label"},"Matrícula criada",-1)),d("div",fD,q(s.user&&s.user.createdDate?s.user.createdDate:"Não disponível"),1)])])]),d("div",hD,[t[25]||(t[25]=d("div",{class:"footer-spacer"},null,-1)),d("div",pD,[d("button",{class:"btn btn-primary",onClick:t[12]||(t[12]=(...c)=>a.saveChanges&&a.saveChanges(...c)),disabled:n.isSubmitting},q(n.isSubmitting?"Salvando...":"Salvar mudanças"),9,mD),d("button",{class:"btn btn-secondary",onClick:t[13]||(t[13]=c=>e.$emit("close"))}," Cancelar ")])])])])):ee("",!0)}const vD=je($C,[["render",gD],["__scopeId","data-v-24ba0708"]]),J3="",X3="",_D={name:"BulkEditEnrollmentModal",components:{Pagination:pn,CustomTable:hn,CustomSelect:mr},props:{show:{type:Boolean,default:!1},users:{type:Array,default:()=>[]},offerclassid:{type:[Number,String],required:!0}},emits:["close","success","error"],data(){return{isSubmitting:!1,formData:{status:"1",startDateStr:"",startTimeStr:"00:00",enableStartDate:!1,endDateStr:"",endTimeStr:"23:59",enableEndDate:!1},statusOptions:[{value:1,label:"Ativo"},{value:0,label:"Suspenso"}],currentPage:1,perPage:5,sortBy:"fullName",sortDesc:!1,tableHeaders:[{text:"NOME/SOBRENOME",value:"fullName",sortable:!1},{text:"ESTADO ",value:"statusName",sortable:!1},{text:"INÍCIO DA MATRÍCULA",value:"startDate",sortable:!1},{text:"FIM DA MATRÍCULA",value:"endDate",sortable:!1}]}},computed:{filteredUsers(){const e=[...this.users].sort((i,n)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<n[this.sortBy]?-1*a:i[this.sortBy]>n[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)}},watch:{show(e){e&&this.initializeForm()}},methods:{initializeForm(){const e=new Date;this.formData={status:"1",startDateStr:this.formatDateForInput(e),startTimeStr:"00:00",enableStartDate:!1,endDateStr:this.formatDateForInput(e),endTimeStr:"23:59",enableEndDate:!1}},formatDateForInput(e){return e.toISOString().split("T")[0]},formatTimeForInput(e){return`${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}`},handleStartDateChange(){},handleStartTimeChange(){},handleEndDateChange(){},handleEndTimeChange(){},async saveChanges(){if(!this.users||this.users.length===0){console.error("Nenhum usuário selecionado"),this.$emit("error","Nenhum usuário selecionado para edição em lote.");return}try{this.isSubmitting=!0;const e=parseInt(this.formData.status);let t=0;if(this.formData.enableStartDate&&this.formData.startDateStr){const[a,u,c]=this.formData.startDateStr.split("-").map(Number),[h,m]=this.formData.startTimeStr.split(":").map(Number),p=new Date(a,u-1,c,h,m,0,0);t=Math.floor(p.getTime()/1e3);const v=p.getTimezoneOffset()*60;t+=v}let s=0;if(this.formData.enableEndDate&&this.formData.endDateStr){const[a,u,c]=this.formData.endDateStr.split("-").map(Number),[h,m]=this.formData.endTimeStr.split(":").map(Number),p=new Date(a,u-1,c,h,m,0,0);s=Math.floor(p.getTime()/1e3);const v=p.getTimezoneOffset()*60;s+=v}const i=this.users.filter(a=>a.offeruserenrolid).map(a=>a.offeruserenrolid);if(i.length===0){console.error("Nenhum ID de matrícula encontrado"),this.$emit("error","Não foi possível encontrar os IDs das matrículas dos usuários selecionados.");return}const n=await sE({offeruserenrolids:i,status:e,timestart:t,timeend:s});if(Array.isArray(n)&&n.length>0){const a=n.filter(h=>h.operation_status).length,u=n.length-a;let c="";if(a===n.length)c=`${a} matrícula(s) editada(s) com sucesso.`;else if(a>0)c=`${a} de ${n.length} matrícula(s) editada(s) com sucesso. ${u} matrícula(s) não puderam ser editadas.`;else{c="Nenhuma matrícula pôde ser editada.",this.$emit("error",c);return}this.$emit("success",{message:c,count:a,total:n.length}),this.$emit("close")}else console.error("Resposta inválida da API:",n),this.$emit("error","Não foi possível editar as matrículas. Por favor, tente novamente.")}catch(e){console.error("Erro ao salvar alterações:",e),this.$emit("error","Ocorreu um erro ao editar as matrículas. Por favor, tente novamente.")}finally{this.isSubmitting=!1}}}},bD={class:"modal-header"},yD={class:"modal-body"},wD={class:"enrollment-form"},ED={class:"table-container"},CD={class:"form-row"},DD={class:"form-field"},xD={class:"select-wrapper"},SD={class:"form-row"},OD={class:"form-field date-time-field"},TD={class:"date-field"},ND=["disabled"],ID={class:"time-field"},AD=["disabled"],MD={class:"enable-checkbox"},PD={class:"form-row"},kD={class:"form-field date-time-field"},VD={class:"date-field"},RD=["disabled"],UD={class:"time-field"},FD=["disabled"],LD={class:"enable-checkbox"},BD={class:"modal-footer"},$D={class:"footer-buttons"},jD=["disabled"];function HD(e,t,s,i,n,a){const u=X("CustomTable"),c=X("Pagination"),h=X("CustomSelect");return s.show?(S(),O("div",{key:0,class:"modal-backdrop",onClick:t[17]||(t[17]=m=>e.$emit("close"))},[d("div",{class:"modal-container",onClick:t[16]||(t[16]=Pt(()=>{},["stop"]))},[d("div",bD,[t[19]||(t[19]=d("h3",{class:"modal-title"},"Edição de Matrículas em Lote",-1)),d("button",{class:"modal-close",onClick:t[0]||(t[0]=m=>e.$emit("close"))},t[18]||(t[18]=[d("i",{class:"fas fa-times"},null,-1)]))]),d("div",yD,[d("div",wD,[d("div",null,[d("div",ED,[M(u,{headers:n.tableHeaders,items:a.filteredUsers},null,8,["headers","items"])]),s.users.length>0?ut((S(),Ut(c,{key:0,"current-page":n.currentPage,"onUpdate:currentPage":t[1]||(t[1]=m=>n.currentPage=m),"per-page":n.perPage,"onUpdate:perPage":t[2]||(t[2]=m=>n.perPage=m),total:s.users.length},null,8,["current-page","per-page","total"])),[[zl,s.users.length>n.perPage]]):ee("",!0),t[20]||(t[20]=d("span",{class:"d-block w-100 border-bottom mt-4"},null,-1))]),d("div",CD,[t[21]||(t[21]=d("div",{class:"form-label"},"Alterar o status",-1)),d("div",DD,[d("div",xD,[M(h,{modelValue:n.formData.status,"onUpdate:modelValue":t[3]||(t[3]=m=>n.formData.status=m),options:n.statusOptions,width:235,class:"smaller-select"},null,8,["modelValue","options"])])])]),d("div",SD,[t[23]||(t[23]=d("div",{class:"form-label"},"Alterar data de início",-1)),d("div",OD,[d("div",TD,[ut(d("input",{type:"date","onUpdate:modelValue":t[4]||(t[4]=m=>n.formData.startDateStr=m),class:"form-control",onChange:t[5]||(t[5]=(...m)=>a.handleStartDateChange&&a.handleStartDateChange(...m)),disabled:!n.formData.enableStartDate},null,40,ND),[[Xt,n.formData.startDateStr]])]),d("div",ID,[ut(d("input",{type:"time","onUpdate:modelValue":t[6]||(t[6]=m=>n.formData.startTimeStr=m),class:"form-control",onChange:t[7]||(t[7]=(...m)=>a.handleStartTimeChange&&a.handleStartTimeChange(...m)),disabled:!n.formData.enableStartDate},null,40,AD),[[Xt,n.formData.startTimeStr]])]),d("div",MD,[ut(d("input",{type:"checkbox",id:"enable-start-date","onUpdate:modelValue":t[8]||(t[8]=m=>n.formData.enableStartDate=m),class:"custom-checkbox"},null,512),[[$i,n.formData.enableStartDate]]),t[22]||(t[22]=d("label",{for:"enable-start-date"},"Habilitar",-1))])])]),d("div",PD,[t[25]||(t[25]=d("div",{class:"form-label"},"Alterar data de fim",-1)),d("div",kD,[d("div",VD,[ut(d("input",{type:"date","onUpdate:modelValue":t[9]||(t[9]=m=>n.formData.endDateStr=m),class:"form-control",onChange:t[10]||(t[10]=(...m)=>a.handleEndDateChange&&a.handleEndDateChange(...m)),disabled:!n.formData.enableEndDate},null,40,RD),[[Xt,n.formData.endDateStr]])]),d("div",UD,[ut(d("input",{type:"time","onUpdate:modelValue":t[11]||(t[11]=m=>n.formData.endTimeStr=m),class:"form-control",onChange:t[12]||(t[12]=(...m)=>a.handleEndTimeChange&&a.handleEndTimeChange(...m)),disabled:!n.formData.enableEndDate},null,40,FD),[[Xt,n.formData.endTimeStr]])]),d("div",LD,[ut(d("input",{type:"checkbox",id:"enable-end-date","onUpdate:modelValue":t[13]||(t[13]=m=>n.formData.enableEndDate=m),class:"custom-checkbox"},null,512),[[$i,n.formData.enableEndDate]]),t[24]||(t[24]=d("label",{for:"enable-end-date"},"Habilitar",-1))])])])])]),d("div",BD,[t[26]||(t[26]=d("div",{class:"footer-spacer"},null,-1)),d("div",$D,[d("button",{class:"btn btn-primary",onClick:t[14]||(t[14]=(...m)=>a.saveChanges&&a.saveChanges(...m)),disabled:n.isSubmitting},q(n.isSubmitting?"Salvando...":"Salvar mudanças"),9,jD),d("button",{class:"btn btn-secondary",onClick:t[15]||(t[15]=m=>e.$emit("close"))}," Cancelar ")])])])])):ee("",!0)}const qD=je(_D,[["render",HD],["__scopeId","data-v-92e8899f"]]),eR="",zD={name:"BulkDeleteEnrollmentModal",components:{Pagination:pn,CustomSelect:mr,CustomTable:hn},props:{show:{type:Boolean,default:!1},users:{type:Array,default:()=>[]},offerclassid:{type:[Number,String],required:!0}},emits:["close","confirm","error"],data(){return{isSubmitting:!1,currentPage:1,perPage:5,sortBy:"fullName",sortDesc:!1,tableHeaders:[{text:"NOME/SOBRENOME",value:"fullName",sortable:!1},{text:"ESTADO ",value:"statusName",sortable:!1},{text:"INÍCIO DA MATRÍCULA",value:"startDate",sortable:!1},{text:"FIM DA MATRÍCULA",value:"endDate",sortable:!1}]}},computed:{filteredUsers(){const e=[...this.users].sort((i,n)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<n[this.sortBy]?-1*a:i[this.sortBy]>n[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)}}},WD={class:"modal-header"},GD={class:"modal-body"},KD={class:"enrollment-form"},QD={class:"table-container"},YD={class:"modal-footer"},ZD={class:"footer-buttons"},JD=["disabled"];function XD(e,t,s,i,n,a){const u=X("CustomTable"),c=X("Pagination");return s.show?(S(),O("div",{key:0,class:"modal-backdrop",onClick:t[6]||(t[6]=h=>e.$emit("close"))},[d("div",{class:"modal-container",onClick:t[5]||(t[5]=Pt(()=>{},["stop"]))},[d("div",WD,[t[8]||(t[8]=d("h3",{class:"modal-title"},"Remoção de Matrículas",-1)),d("button",{class:"modal-close",onClick:t[0]||(t[0]=h=>e.$emit("close"))},t[7]||(t[7]=[d("i",{class:"fas fa-times"},null,-1)]))]),d("div",GD,[d("div",KD,[d("div",QD,[M(u,{headers:n.tableHeaders,items:a.filteredUsers},null,8,["headers","items"])]),s.users.length>0?ut((S(),Ut(c,{key:0,"current-page":n.currentPage,"onUpdate:currentPage":t[1]||(t[1]=h=>n.currentPage=h),"per-page":n.perPage,"onUpdate:perPage":t[2]||(t[2]=h=>n.perPage=h),total:s.users.length},null,8,["current-page","per-page","total"])),[[zl,s.users.length>n.perPage]]):ee("",!0)]),t[9]||(t[9]=d("div",{class:"text-center mt-5"},[d("h5",{class:"mt-1"}," Tem certeza de que deseja excluir essas inscrições de usuário? ")],-1))]),d("div",YD,[d("div",ZD,[d("button",{class:"btn btn-primary",onClick:t[3]||(t[3]=h=>e.$emit("confirm")),disabled:n.isSubmitting},q(n.isSubmitting?"Removendo...":"Remover matrículas"),9,JD),d("button",{class:"btn btn-secondary",onClick:t[4]||(t[4]=h=>e.$emit("close"))}," Cancelar ")])])])])):ee("",!0)}const ex=je(zD,[["render",XD],["__scopeId","data-v-37ea04c6"]]),tR="",tx={name:"TextEditor",props:{modelValue:{type:String,default:""},label:{type:String,default:""},placeholder:{type:String,default:"Digite o conteúdo aqui..."},rows:{type:Number,default:5},disabled:{type:Boolean,default:!1}},emits:["update:modelValue"],data(){return{showHtmlSource:!1,htmlContent:this.modelValue}},mounted(){this.$refs.editableContent&&(this.$refs.editableContent.textContent=this.modelValue)},watch:{modelValue:{handler(e){this.showHtmlSource?this.htmlContent=e:this.$refs.editableContent&&this.$refs.editableContent.textContent!==e&&(this.$refs.editableContent.textContent=e)},immediate:!0}},methods:{applyFormat(e,t=null){this.showHtmlSource||(document.execCommand(e,!1,t),this.updateContent())},insertLink(){if(this.showHtmlSource)return;const e=prompt("Digite a URL do link:","http://");e&&this.applyFormat("createLink",e)},insertImage(){if(this.showHtmlSource)return;const e=prompt("Digite a URL da imagem:","http://");e&&this.applyFormat("insertImage",e)},toggleHtmlView(){this.showHtmlSource?this.$nextTick(()=>{this.$refs.editableContent&&(this.$refs.editableContent.textContent=this.htmlContent)}):this.htmlContent=this.$refs.editableContent.textContent,this.showHtmlSource=!this.showHtmlSource},updateContent(){if(!this.showHtmlSource&&this.$refs.editableContent){const e=this.$refs.editableContent.textContent;this.$emit("update:modelValue",e)}},updateHtmlContent(){this.showHtmlSource&&this.$emit("update:modelValue",this.htmlContent)}}},sx={class:"text-editor-container"},rx={class:"editor-toolbar"},nx={class:"toolbar-group"},ox=["disabled"],ix=["disabled"],ax=["disabled"],lx=["disabled"],ux={class:"toolbar-group"},cx=["disabled"],dx=["disabled"],fx=["contenteditable","data-text"],hx=["rows","placeholder","disabled"];function px(e,t,s,i,n,a){return S(),O("div",sx,[s.label?(S(),O("label",{key:0,class:pe(["filter-label",{disabled:s.disabled}])},q(s.label),3)):ee("",!0),d("div",{class:pe(["editor-container",{disabled:s.disabled}])},[d("div",rx,[d("div",nx,[d("button",{class:"btn-editor",onClick:t[0]||(t[0]=u=>!s.disabled&&a.applyFormat("bold")),title:"Negrito",disabled:s.disabled},t[10]||(t[10]=[d("i",{class:"fas fa-bold"},null,-1)]),8,ox),d("button",{class:"btn-editor",onClick:t[1]||(t[1]=u=>!s.disabled&&a.applyFormat("italic")),title:"Itálico",disabled:s.disabled},t[11]||(t[11]=[d("i",{class:"fas fa-italic"},null,-1)]),8,ix),d("button",{class:"btn-editor",onClick:t[2]||(t[2]=u=>!s.disabled&&a.applyFormat("underline")),title:"Sublinhado",disabled:s.disabled},t[12]||(t[12]=[d("i",{class:"fas fa-underline"},null,-1)]),8,ax),d("button",{class:"btn-editor",onClick:t[3]||(t[3]=u=>!s.disabled&&a.applyFormat("strikethrough")),title:"Tachado",disabled:s.disabled},t[13]||(t[13]=[d("i",{class:"fas fa-strikethrough"},null,-1)]),8,lx)]),t[16]||(t[16]=d("div",{class:"toolbar-divider"},null,-1)),d("div",ux,[d("button",{class:"btn-editor",onClick:t[4]||(t[4]=u=>!s.disabled&&a.applyFormat("insertUnorderedList")),title:"Lista não ordenada",disabled:s.disabled},t[14]||(t[14]=[d("i",{class:"fas fa-list-ul"},null,-1)]),8,cx),d("button",{class:"btn-editor",onClick:t[5]||(t[5]=u=>!s.disabled&&a.applyFormat("insertOrderedList")),title:"Lista ordenada",disabled:s.disabled},t[15]||(t[15]=[d("i",{class:"fas fa-list-ol"},null,-1)]),8,dx)])]),n.showHtmlSource?ut((S(),O("textarea",{key:1,"onUpdate:modelValue":t[8]||(t[8]=u=>n.htmlContent=u),class:"editor-textarea",rows:s.rows,placeholder:s.placeholder,onInput:t[9]||(t[9]=(...u)=>a.updateHtmlContent&&a.updateHtmlContent(...u)),disabled:s.disabled},null,40,hx)),[[Xt,n.htmlContent]]):(S(),O("div",{key:0,class:"editor-content",contenteditable:!s.disabled,onInput:t[6]||(t[6]=(...u)=>a.updateContent&&a.updateContent(...u)),onKeyup:t[7]||(t[7]=(...u)=>a.updateContent&&a.updateContent(...u)),ref:"editableContent","data-text":s.placeholder},null,40,fx))],2)])}const _u=je(tx,[["render",px],["__scopeId","data-v-041cbaad"]]),sR="",mx={name:"CancelEnrollmentModal",components:{TextEditor:_u},props:{show:{type:Boolean,default:!1},user:{type:Object,default:null},offerclassid:{type:[Number,String],required:!0}},emits:["close","confirm","error"],data(){return{isSubmitting:!1,cancellationReason:""}},watch:{show(e){e?(this.cancellationReason="",this.isSubmitting=!1):this.resetForm()}},methods:{handleConfirm(){this.isSubmitting||(this.isSubmitting=!0,this.$emit("confirm",{user:this.user,reason:this.cancellationReason.trim(),offerclassid:this.offerclassid}))},resetForm(){this.cancellationReason="",this.isSubmitting=!1}}},gx={class:"modal-header"},vx={class:"modal-body"},_x={class:"user-info"},bx={class:"reason-section"},yx={class:"text-editor-wrapper"},wx={class:"modal-footer"},Ex={class:"footer-buttons"},Cx=["disabled"];function Dx(e,t,s,i,n,a){var c;const u=X("TextEditor");return s.show?(S(),O("div",{key:0,class:"modal-backdrop",onClick:t[5]||(t[5]=h=>e.$emit("close"))},[d("div",{class:"modal-container",onClick:t[4]||(t[4]=Pt(()=>{},["stop"]))},[d("div",gx,[t[7]||(t[7]=d("h3",{class:"modal-title"},"Cancelar matrícula",-1)),d("button",{class:"modal-close",onClick:t[0]||(t[0]=h=>e.$emit("close"))},t[6]||(t[6]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[d("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),d("line",{x1:"6",y1:"6",x2:"18",y2:"18"})],-1)]))]),d("div",vx,[t[11]||(t[11]=d("div",{class:"confirmation-text"},[d("p",null,"Você tem certeza de que deseja cancelar esta matrícula?")],-1)),d("div",_x,[d("p",null,[t[8]||(t[8]=Ue('Ao cancelar a matrícula do usuário "')),d("strong",null,q(((c=s.user)==null?void 0:c.fullName)||"<nome_do_usuário>"),1),t[9]||(t[9]=Ue('", o progresso dele até aqui será salvo e ele não terá mais acesso ao conteúdo do curso.'))])]),d("div",bx,[t[10]||(t[10]=d("label",{class:"reason-label"},"Justificativa do cancelamento",-1)),d("div",yx,[M(u,{modelValue:n.cancellationReason,"onUpdate:modelValue":t[1]||(t[1]=h=>n.cancellationReason=h),placeholder:"Digite aqui a justificativa motivo do cancelamento da matrícula",rows:6},null,8,["modelValue"])])])]),d("div",wx,[d("div",Ex,[d("button",{class:"btn btn-danger",onClick:t[2]||(t[2]=(...h)=>a.handleConfirm&&a.handleConfirm(...h)),disabled:n.isSubmitting},q(n.isSubmitting?"Processando...":"Confirmar"),9,Cx),d("button",{class:"btn btn-secondary",onClick:t[3]||(t[3]=h=>e.$emit("close"))}," Cancelar ")])])])])):ee("",!0)}const xx=je(mx,[["render",Dx],["__scopeId","data-v-5118f6c7"]]),rR="",Sx={name:"BackButton",props:{label:{type:String,default:"Voltar"},route:{type:String,default:"/local/offermanager/"}},methods:{goBack(){this.$emit("click")}}};function Ox(e,t,s,i,n,a){return S(),O("button",{class:"btn-back",onClick:t[0]||(t[0]=(...u)=>a.goBack&&a.goBack(...u))},[t[1]||(t[1]=d("i",{class:"fas fa-angle-left"},null,-1)),Ue(" "+q(s.label),1)])}const bu=je(Sx,[["render",Ox],["__scopeId","data-v-c577f103"]]),nR="",Tx={name:"UserAvatar",props:{imageUrl:{type:String,default:""},fullName:{type:String,required:!0},size:{type:Number,default:32}},computed:{hasImage(){return!!this.imageUrl},initials(){if(!this.fullName)return"";const e=this.fullName.split(" ").filter(i=>i.length>0);if(e.length===0)return"";if(e.length===1)return e[0].substring(0,2).toUpperCase();const t=e[0].charAt(0),s=e[e.length-1].charAt(0);return(t+s).toUpperCase()},backgroundColor(){const e=["#1976D2","#388E3C","#D32F2F","#7B1FA2","#FFA000","#0097A7","#E64A19","#5D4037","#455A64","#616161"];let t=0;for(let i=0;i<this.fullName.length;i++)t=this.fullName.charCodeAt(i)+((t<<5)-t);const s=Math.abs(t)%e.length;return e[s]},avatarStyle(){return{width:`${this.size}px`,height:`${this.size}px`,minWidth:`${this.size}px`,minHeight:`${this.size}px`}}}},Nx=["src"];function Ix(e,t,s,i,n,a){return S(),O("div",{class:"user-avatar",style:us(a.avatarStyle)},[a.hasImage?(S(),O("img",{key:0,src:s.imageUrl,alt:"Foto de perfil",class:"avatar-image"},null,8,Nx)):(S(),O("div",{key:1,class:"avatar-initials",style:us({backgroundColor:a.backgroundColor})},q(a.initials),5))],4)}const Ax=je(Tx,[["render",Ix],["__scopeId","data-v-eed19d8a"]]),oR="",Mx={name:"RoleSelector",props:{userId:{type:[Number,String],required:!0},offeruserenrolid:{type:[Number,String],required:!0},currentRole:{type:[String,Array],required:!0},offerclassid:{type:[Number,String],required:!0}},data(){return{isEditing:!1,selectedRoles:[],roles:[],loading:!1,initialLoading:!0}},computed:{displayRoleNames(){return Array.isArray(this.currentRole)?this.currentRole.join(", "):String(this.currentRole||"")}},mounted(){this.loadRoles()},methods:{async loadRoles(){var e;this.initialLoading||(this.loading=!0);try{const t=await hu(parseInt(this.offerclassid)),s=((e=t==null?void 0:t.data)==null?void 0:e.offercourseid)||t.offercourseid;if(!s)throw new Error("offercourseid não encontrado");const i=await pu(s);this.roles=Array.isArray(i==null?void 0:i.data)?i.data:Array.isArray(i)?i:[];const n=await oE(this.offeruserenrolid);if(Array.isArray(n)&&n.length)this.selectedRoles=n.map(a=>a.id);else if(Array.isArray(this.currentRole))this.selectedRoles=this.roles.filter(a=>this.currentRole.includes(a.name)).map(a=>a.id);else if(this.currentRole){const a=this.roles.find(u=>u.name.toLowerCase()===String(this.currentRole).toLowerCase());a&&(this.selectedRoles=[a.id])}}catch{this.$emit("error","Não foi possível carregar papéis.")}finally{this.loading=!1,this.initialLoading=!1}},startEditing(){this.isEditing=!0,this.$nextTick(()=>{var e;return(e=this.$refs.roleSelect)==null?void 0:e.focus()})},cancelEdit(){this.isEditing=!1},close(){this.isEditing&&(this.isEditing=!1)},async saveRoles(){if(!this.selectedRoles.length){this.$emit("error","Selecione ao menos um papel.");return}this.loading=!0;try{const e=await iE(this.offeruserenrolid,this.selectedRoles.map(t=>parseInt(t)));if(e===!0||e&&e.error===!1||e&&e.success===!0){const t=this.roles.filter(s=>this.selectedRoles.includes(s.id)).map(s=>s.name);this.$emit("success",{userId:this.userId,offeruserenrolid:this.offeruserenrolid,roleids:this.selectedRoles,roleNames:t}),this.isEditing=!1,this.$emit("reload-table")}else throw new Error("Resposta inesperada do servidor: "+JSON.stringify(e))}catch(e){console.error("Erro ao salvar papéis:",e),this.$emit("error","Não foi possível salvar papéis.")}finally{this.loading=!1}}}},Px={class:"role-selector"},kx={key:1,class:"role-edit-wrapper"},Vx={class:"role-edit-container"},Rx={class:"select-wrapper"},Ux=["value"],Fx={class:"role-actions"},Lx={key:2,class:"loading-overlay"};function Bx(e,t,s,i,n,a){return S(),O("div",Px,[n.isEditing?(S(),O("div",kx,[d("div",Vx,[d("div",Rx,[ut(d("select",{"onUpdate:modelValue":t[1]||(t[1]=u=>n.selectedRoles=u),class:"role-select",ref:"roleSelect",multiple:"",onClick:t[2]||(t[2]=Pt(()=>{},["stop"])),style:us({height:Math.max(4,n.roles.length)*25+"px"})},[(S(!0),O(Ie,null,at(n.roles,u=>(S(),O("option",{key:u.id,value:u.id},q(u.name),9,Ux))),128))],4),[[Kl,n.selectedRoles]])]),d("div",Fx,[d("button",{class:"btn-save",onClick:t[3]||(t[3]=Pt((...u)=>a.saveRoles&&a.saveRoles(...u),["stop"])),title:"Salvar"},t[6]||(t[6]=[d("i",{class:"fas fa-check"},null,-1)])),d("button",{class:"btn-cancel",onClick:t[4]||(t[4]=Pt((...u)=>a.cancelEdit&&a.cancelEdit(...u),["stop"])),title:"Cancelar"},t[7]||(t[7]=[d("i",{class:"fas fa-times"},null,-1)]))])])])):(S(),O("div",{key:0,class:"role-display",onClick:t[0]||(t[0]=Pt((...u)=>a.startEditing&&a.startEditing(...u),["stop"]))},[d("span",null,q(a.displayRoleNames),1),t[5]||(t[5]=d("i",{class:"fas fa-pencil-alt edit-icon","aria-hidden":"true"},null,-1))])),n.loading&&n.isEditing?(S(),O("div",Lx,t[8]||(t[8]=[d("div",{class:"spinner"},null,-1)]))):ee("",!0)])}const $x=je(Mx,[["render",Bx],["__scopeId","data-v-217c6284"]]),iR="",jx={name:"RegisteredUsers",components:{CustomTable:hn,CustomSelect:mr,HierarchicalSelect:pE,CustomInput:Fo,CustomCheckbox:Xi,CustomButton:Ln,FilterSection:ip,FilterRow:ea,FilterGroup:ta,FilterActions:ap,FilterTag:Bo,FilterTags:ra,Pagination:pn,PageHeader:sa,ConfirmationModal:gu,Autocomplete:$o,EnrolmentModalNew:EC,EnrollmentDetailsModal:BC,Toast:Lo,EditEnrollmentModal:vD,BulkEditEnrollmentModal:qD,BulkDeleteEnrollmentModal:ex,CancelEnrollmentModal:xx,BackButton:bu,UserAvatar:Ax,RoleSelector:$x,LFLoading:mu},data(){return{offerid:null,offerclassid:null,offercourseid:null,courseid:null,courseContextId:null,filteredUsers:[],nameOptions:[],cpfOptions:[],emailOptions:[],nameSearchInput:"",cpfSearchInput:"",emailSearchInput:"",showNameDropdown:!1,showCpfDropdown:!1,showEmailDropdown:!1,nameDebounceTimer:null,cpfDebounceTimer:null,emailDebounceTimer:null,tableHeaders:[{text:"",value:"select",sortable:!1,width:"50px"},{text:"NOME/SOBRENOME",value:"fullName",sortable:!0,width:"220px"},{text:"E-MAIL",value:"email",sortable:!0},{text:"CPF",value:"cpf",sortable:!0},{text:"PAPÉIS",value:"roles",sortable:!1},{text:"GRUPOS",value:"groups",sortable:!1},{text:"DATA INÍCIO DA MATRÍCULA",value:"startDate",sortable:!0},{text:"DATA FIM DA MATRÍCULA",value:"endDate",sortable:!0},{text:"PRAZO DE CONCLUSÃO",value:"deadline",sortable:!0},{text:"PROGRESSO",value:"progress",sortable:!1},{text:"SITUAÇÃO DE MATRÍCULA",value:"situation",sortable:!0},{text:"NOTA",value:"grade",sortable:!1},{text:"ESTADO",value:"status",sortable:!0}],enrolments:[],totalEnrolments:0,loading:!1,error:null,currentPage:1,perPage:10,sortBy:"fullName",sortDesc:!1,showBulkDeleteEnrollmentModal:!1,showEnrollmentModal:!1,selectedUser:null,showEnrolmentModal:!1,roleOptions:[],showEditEnrollmentModal:!1,showBulkEditEnrollmentModal:!1,showCancelEnrollmentModal:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,classDetails:{},selectedUsers:[],selectedBulkAction:"",selectedPageView:"usuarios_matriculados",pageViewOptions:[{value:"matriculas",label:"Matrículas",children:[{value:"usuarios_matriculados",label:"Usuários matriculados"}]},{value:"grupos",label:"Grupos",children:[{value:"grupos",label:"Grupos"},{value:"agrupamentos",label:"Agrupamentos"},{value:"visao_geral",label:"Visão geral"}]},{value:"permissoes",label:"Permissões",children:[{value:"permissoes",label:"Permissões"},{value:"outros_usuarios",label:"Outros usuários"},{value:"verificar_permissoes",label:"Verificar permissões"}]}]}},setup(){return{router:Zi()}},async created(){var t,s,i,n;if(this.offerclassid=this.offerclassid??this.$route.params.offerclassid,!this.offerclassid)throw new Error("ID da turma não foi definido.");this.offerclassid=parseInt(this.offerclassid);const e=await hu(this.offerclassid);if(e.error)throw new Error("Erro ao requisitar informações da turma");this.classDetails=e.data,this.offerid=parseInt((t=this.classDetails)==null?void 0:t.offerid),this.offercourseid=parseInt((s=this.classDetails)==null?void 0:s.offercourseid),this.corseid=(i=this.classDetails)==null?void 0:i.courseid,this.courseContextId=(n=this.classDetails)==null?void 0:n.course_context_id,await this.loadRoles(),await this.loadRegisteredUsers()},beforeUnmount(){this.nameDebounceTimer&&clearTimeout(this.nameDebounceTimer),this.cpfDebounceTimer&&clearTimeout(this.cpfDebounceTimer),this.emailDebounceTimer&&clearTimeout(this.emailDebounceTimer)},computed:{allSelected(){return this.enrolments.length>0&&this.selectedUsers.length===this.enrolments.length},someSelected(){return this.selectedUsers.length>0&&!this.allSelected},excludedUserIds(){return this.filteredUsers.map(e=>e.id||e.value)}},watch:{perPage(e,t){e!==t&&(this.currentPage=1,this.selectedUsers=[],this.loadRegisteredUsers())},currentPage(e,t){e!==t&&this.loadRegisteredUsers()}},methods:{async loadRegisteredUsers(){this.loading=!0,this.error=null;let e=[];this.filteredUsers.length>0&&(e=this.excludedUserIds);const t={offerclassid:this.offerclassid,userids:e,page:this.currentPage,perpage:this.perPage,orderby:this.mapSortFieldToBackend(this.sortBy||"fullName"),direction:this.sortDesc?"DESC":"ASC"},s=await J1(t);if(s.data){const i=s.data;Array.isArray(i.enrolments)&&(this.enrolments=i.enrolments.map(n=>({id:n.userid,offeruserenrolid:n.offeruserenrolid,fullName:n.fullname,email:n.email,cpf:n.cpf,enrol:n.enrol,roles:this.formatRoles(n.roles),groups:n.groups,timecreated:n.timecreated,createdDate:this.formatDateTime(n.timecreated),timestart:n.timestart,timeend:n.timeend,startDate:this.formatDate(n.timestart),endDate:this.formatDate(n.timeend),deadline:n.enrolperiod,progress:this.formatProgress(n.progress),situation:n.situation,situationName:n.situation_name,grade:n.grade||"-",status:n.status,statusName:n.status!==void 0?n.status===0?"Ativo":"Suspenso":"-",canCancel:n.can_cancel})),this.totalEnrolments=i.total||this.enrolments.length)}else this.enrolments=[],this.totalEnrolments=0;this.loading=!1},formatDate(e){return!e||e===0?"-":new Date(e*1e3).toLocaleDateString("pt-BR")},formatDateTime(e,t={}){return!e||e===0?"-":(Object.keys(t).length===0&&(t={day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}),new Date(e*1e3).toLocaleString("pt-BR",t))},formatProgress(e){return e==null?"-":Math.round(e)+"%"},formatRoles(e){return!e||e==="-"?"-":typeof e=="string"?e.split(",").join(", "):Array.isArray(e)&&e.length>0&&typeof e[0]=="object"&&e[0].name?e.map(t=>t.name).join(", "):Array.isArray(e)?e.join(", "):"-"},async loadNameOptions(e){if(!e||e.length<3){this.nameOptions=[],this.showNameDropdown=!1;return}try{const t=await vu({offerclassid:this.offerclassid,fieldstring:"name",searchstring:e,excludeduserids:this.excludedUserIds});t.data?(this.nameOptions=t.data.map(s=>({id:s.id,value:s.id,label:s.fullname})),this.showNameDropdown=this.nameOptions.length>0):(this.nameOptions=[],this.showNameDropdown=!1)}catch{this.nameOptions=[],this.showNameDropdown=!1}},async loadCpfOptions(e){if(!e||e.length<3){this.cpfOptions=[],this.showCpfDropdown=!1;return}try{const t=await vu({offerclassid:this.offerclassid,fieldstring:"username",searchstring:e,excludeduserids:this.excludedUserIds});t.data?(this.cpfOptions=t.data.map(s=>({id:s.id,value:s.id,label:s.fullname})),this.showCpfDropdown=this.cpfOptions.length>0):(this.cpfOptions=[],this.showCpfDropdown=!1)}catch{this.cpfOptions=[],this.showCpfDropdown=!1}},async loadEmailOptions(e){if(!e||e.length<3){this.emailOptions=[],this.showEmailDropdown=!1;return}try{const t=await vu({offerclassid:this.offerclassid,fieldstring:"email",searchstring:e,excludeduserids:this.excludedUserIds});!t.error&&t.data?(this.emailOptions=t.data.map(s=>({id:s.id,value:s.id,label:s.fullname})),this.showEmailDropdown=this.emailOptions.length>0):(this.emailOptions=[],this.showEmailDropdown=!1)}catch{this.emailOptions=[],this.showEmailDropdown=!1}},handleNameInput(){this.nameDebounceTimer&&clearTimeout(this.nameDebounceTimer),this.nameSearchInput.length>=3?this.nameDebounceTimer=setTimeout(()=>{this.loadNameOptions(this.nameSearchInput)},500):this.showNameDropdown=!1},handleCpfInput(){this.cpfDebounceTimer&&clearTimeout(this.cpfDebounceTimer),this.cpfSearchInput.length>=3?this.cpfDebounceTimer=setTimeout(()=>{this.loadCpfOptions(this.cpfSearchInput)},500):this.showCpfDropdown=!1},handleEmailInput(){this.emailDebounceTimer&&clearTimeout(this.emailDebounceTimer),this.emailSearchInput.length>=3?this.emailDebounceTimer=setTimeout(()=>{this.loadEmailOptions(this.emailSearchInput)},500):this.showEmailDropdown=!1},selectNameOption(e){this.filteredUsers.push({id:e.id,value:e.value,label:e.label,type:"name"}),this.nameSearchInput="",this.showNameDropdown=!1,this.clearOptions(),this.loadRegisteredUsers()},selectCpfOption(e){this.filteredUsers.push({id:e.id,value:e.value,label:e.label,type:"cpf"}),this.cpfSearchInput="",this.showCpfDropdown=!1,this.clearOptions(),this.loadRegisteredUsers()},selectEmailOption(e){this.filteredUsers.push({id:e.id,value:e.value,label:e.label,type:"email"}),this.emailSearchInput="",this.showEmailDropdown=!1,this.clearOptions(),this.loadRegisteredUsers()},clearOptions(e){setTimeout(()=>{switch(e){case"name":this.nameOptions=[];break;case"cpf":this.cpfOptions=[];break;case"email":this.emailOptions=[];break;default:this.nameOptions=[],this.cpfOptions=[],this.emailOptions=[];break}},500)},removeFilter(e){const t=this.filteredUsers.findIndex(s=>s.id===e||s.value===e);t!==-1&&this.filteredUsers.splice(t,1),this.loadRegisteredUsers()},clearFilteredUsers(){this.filteredUsers=[],this.loadRegisteredUsers()},toggleSelectAll(){this.allSelected?this.selectedUsers=[]:this.selectedUsers=this.enrolments.map(e=>e.id)},toggleSelectUser(e){const t=this.selectedUsers.indexOf(e);t===-1?this.selectedUsers.push(e):this.selectedUsers.splice(t,1)},isSelected(e){return this.selectedUsers.includes(e)},async handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,await this.loadRegisteredUsers()},mapSortFieldToBackend(e){return{fullName:"fullname",email:"email",cpf:"cpf",startDate:"startdate",endDate:"enddate",deadline:"enrolperiod",situation:"situation",status:"status"}[e]||"fullname"},addNewUser(){var e;if(this.classDetails&&((e=this.classDetails)==null?void 0:e.operational_cycle)===2){this.error="Não é possível matricular usuários em uma turma com ciclo operacional encerrado.";return}this.showEnrolmentModal=!0},closeEnrolmentModal(){this.showEnrolmentModal=!1},async goBack(){this.router.push({name:"editar-oferta",params:{id:this.offerid}})},viewUserProfile(e){if(!e)return;const t=`/user/view.php?id=${e}&course=${this.courseid}`;window.location.href=t},async handlePageViewChange(e){let t=this.offerclassid,s=this.courseid,i=this.courseContextId;const n={usuarios_matriculados:`/local/offermanager/new-subscribed-users/${t}`,grupos:`/group/index.php?id=${s}`,agrupamentos:`/group/groupings.php?id=${s}`,visao_geral:`/user/index.php?id=${s}`,permissoes:`/admin/roles/permissions.php?contextid=${i}`,outros_usuarios:`/enrol/otherusers.php?id=${s}`,verificar_permissoes:`/admin/roles/check.php?contextid=${i}`};n[e]&&(window.location.href=n[e])},async handleEnrolmentSuccess(){await this.loadRegisteredUsers()},async loadRoles(){const e=await pu(this.offercourseid);if(e.error)throw new Error("Erro ao requisitar papéis do curso");this.roleOptions=e.data.map(t=>({value:t.id,label:t.name}))},showEnrollmentDetails(e){this.selectedUser={fullName:e.fullName,enrol:e.enrol||"Inscrições manuais",status:e.status||0,statusName:e.statusName||"Ativo",startDate:e.startDate||"Não disponível",createdDate:e.createdDate||e.startDate||"Não disponível"},this.showEnrollmentModal=!0},closeEnrollmentModal(){this.showEnrollmentModal=!1,this.selectedUser=null},closeEditEnrollmentModal(){this.showEditEnrollmentModal=!1,this.selectedUser=null},async handleEditEnrollmentSuccess(e){if(this.showSuccessMessage("Matrícula editada com sucesso."),e.roleid){let t=null;if(this.roleOptions&&this.roleOptions.length>0){const i=this.roleOptions.find(n=>n.value===String(e.roleid));i&&(t=i.name)}if(!t){await this.loadRegisteredUsers(),this.showEditEnrollmentModal=!1,this.selectedUser=null;return}const s=this.enrolments.findIndex(i=>i.id===e.userId);if(s!==-1){if(t&&(this.enrolments[s].roles=t),e.status!==void 0&&(this.enrolments[s].status=e.status,e.status===1?this.enrolments[s].statusName="Ativo":e.status===0&&(this.enrolments[s].statusName="Suspenso")),e.timestart){const i=new Date(e.timestart*1e3);this.enrolments[s].startDate=i.toLocaleDateString("pt-BR")}if(e.timeend){const i=new Date(e.timeend*1e3);this.enrolments[s].endDate=i.toLocaleDateString("pt-BR")}}else await this.loadRegisteredUsers()}else await this.loadRegisteredUsers();this.showEditEnrollmentModal=!1,this.selectedUser=null},handleEditEnrollmentError(e){this.showErrorMessage(e||"Não foi possível editar a matrícula. Por favor, tente novamente.")},handleRoleUpdateSuccess(e){this.showSuccessMessage("Papel atualizado com sucesso.");const t=this.enrolments.findIndex(s=>s.id===e.userId);t!==-1?this.enrolments[t].roles=e.roleName:this.reloadTable()},handleRoleUpdateError(e){this.showErrorMessage(e||"Ocorreu um erro ao atualizar o papel do usuário.")},reloadTable(){this.loadRegisteredUsers()},editUser(e){let t=null;e.roles&&e.roleid&&(t=e.roleid),this.selectedUser={id:e.id,offeruserenrolid:e.offeruserenrolid,fullName:e.fullName,enrol:e.enrol,status:e.status,statusName:e.statusName,roles:e.roles,roleid:t,startDate:e.startDate,timestart:e.timestart,timeend:e.timeend,createdDate:e.createdDate||"-"},this.showEditEnrollmentModal=!0},async confirmeBulkDeleteEnrollment(){this.loading=!0;const e=[];for(const i of this.selectedUsers){const n=this.enrolments.find(a=>a.id===i);n&&n.offeruserenrolid&&e.push(n.offeruserenrolid)}if(e.length===0){this.showErrorMessage("Não foi possível encontrar os IDs das matrículas. Por favor, tente novamente."),this.loading=!1;return}const t=`Processando exclusão de ${e.length} matrícula(s)...`;this.showSuccessMessage(t);const s=await nE(e);if(s&&s.length>0){const i=s.filter(a=>a.operation_status).length,n=s.length-i;i>0?(this.showSuccessMessage(`${i} matrícula(s) cancelada(s) com sucesso.${n>0?` ${n} matrícula(s) não puderam ser canceladas.`:""}`),await this.loadRegisteredUsers(),this.selectedUsers=[]):this.showErrorMessage("Não foi possível cancelar as matrículas. Por favor, tente novamente.")}else this.showSuccessMessage(`${e.length} matrícula(s) cancelada(s) com sucesso.`),await this.loadRegisteredUsers(),this.selectedUsers=[];this.showBulkDeleteEnrollmentModal=!1,this.loading=!1},handleBulkAction(){if(this.selectedBulkAction){if(this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para realizar esta ação."),this.selectedBulkAction="";return}switch(this.selectedBulkAction){case"message":this.sendMessage();break;case"note":this.writeNote();break;case"download_csv":this.downloadData("csv");break;case"download_xlsx":this.downloadData("xlsx");break;case"download_html":this.downloadData("html");break;case"download_json":this.downloadData("json");break;case"download_ods":this.downloadData("ods");break;case"download_pdf":this.downloadData("pdf");break;case"edit_enrolment":this.editEnrolments();break;case"delete_enrolment":this.bulkDeleteEnrollment();break}this.selectedBulkAction=""}},sendMessage(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showErrorMessage("Por favor, selecione pelo menos um usuário para enviar mensagem.");return}this.showSendMessageModal(this.selectedUsers)},showSendMessageModal(e){if(typeof window.require!="function"){this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.");return}window.require(["core_message/message_send_bulk"],t=>{if(typeof t.showModal!="function"){this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.");return}t.showModal(e,()=>{this.selectedBulkAction=""})},t=>{this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.")})},writeNote(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para escrever anotação.");return}this.showAddNoteModal(this.courseid,this.selectedUsers)},showAddNoteModal(e,t){if(typeof window.require!="function"){this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.");return}window.require(["core_user/local/participants/bulkactions"],s=>{if(typeof s.showAddNote!="function"){this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.");return}const i={personal:"Pessoal",course:"Curso",site:"Site"};s.showAddNote(e,t,i,"").then(n=>(n.getRoot().on("hidden.bs.modal",()=>{this.selectedBulkAction=""}),n)).catch(n=>{this.showErrorMessage("Ocorreu um erro ao abrir o modal de anotações. Por favor, tente novamente mais tarde.")})},s=>{this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.")})},downloadData(e){this.selectedUsers.length!==0&&this.prepareLocalDownload(e)},prepareLocalDownload(e){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Nenhum usuário selecionado para download.");return}const t=[];if(Array.isArray(this.selectedUsers)&&this.selectedUsers.every(s=>typeof s=="number"))for(const s of this.selectedUsers){const i=this.enrolments.find(n=>n.id===s);if(i){const n={ID:i.id||"",Nome:i.fullName||i.name||"",Email:i.email||"",CPF:i.cpf||"",Papéis:i.roles||"",Grupos:i.groups||"","Data de Início":i.startDate||"","Data de Término":i.endDate||"",Prazo:i.deadline||"",Progresso:i.progress||"",Situação:i.situationName||i.situation||"",Nota:i.grade||"",Estado:i.statusName||""};t.push(n)}}else for(const s of this.selectedUsers)if(typeof s=="number"){const i=this.enrolments.find(n=>n.id===s);if(i){const n={ID:i.id||"",Nome:i.fullName||i.name||"",Email:i.email||"",CPF:i.cpf||"",Papéis:i.roles||"",Grupos:i.groups||"","Data de Início":i.startDate||"","Data de Término":i.endDate||"",Prazo:i.deadline||"",Progresso:i.progress||"",Situação:i.situationName||i.situation||"",Nota:i.grade||"",Estado:i.statusName||""};t.push(n)}}else if(typeof s=="object"&&s!==null){const i={ID:s.id||"",Nome:s.fullName||s.name||"",Email:s.email||"",CPF:s.cpf||"",Papéis:s.roles||"",Grupos:s.groups||"","Data de Início":s.startDate||"","Data de Término":s.endDate||"",Prazo:s.deadline||"",Progresso:s.progress||"",Situação:s.situationName||s.situation||"",Nota:s.grade||"",Estado:s.statusName||""};t.push(i)}if(t.length===0){this.showErrorMessage("Nenhum dado disponível para download.");return}switch(e){case"csv":this.downloadCSV(t);break;case"xlsx":this.downloadXLSX(t);break;case"html":this.downloadHTML(t);break;case"json":this.downloadJSON(t);break;case"ods":this.downloadODS(t);break;case"pdf":this.downloadPDF(t);break;default:this.showErrorMessage("Formato de download não suportado.");break}},downloadCSV(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]),i=s.map(h=>h.replace(/([A-Z])/g," $1").replace(/^./,m=>m.toUpperCase()).trim()),n=t+[i.join(","),...e.map(h=>s.map(m=>{const p=h[m]||"";return`"${String(p).replace(/"/g,'""')}"`}).join(","))].join(`
`),a=new Blob([n],{type:"text/csv;charset=utf-8;"}),u=URL.createObjectURL(a),c=document.createElement("a");c.setAttribute("href",u),c.setAttribute("download","usuarios_matriculados.csv"),c.style.visibility="hidden",document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(u)},downloadXLSX(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]),i=t+[s.join(","),...e.map(c=>s.map(h=>{const m=c[h]||"";return`"${String(m).replace(/"/g,'""')}"`}).join(","))].join(`
`),n=new Blob([i],{type:"text/csv;charset=utf-8;"}),a=URL.createObjectURL(n),u=document.createElement("a");u.setAttribute("href",a),u.setAttribute("download","usuarios_matriculados.csv"),u.style.visibility="hidden",document.body.appendChild(u),u.click(),document.body.removeChild(u),URL.revokeObjectURL(a),this.showSuccessMessage("Download concluído. O arquivo CSV pode ser aberto no Excel.")},downloadHTML(e){if(e.length===0)return;const t=Object.keys(e[0]),s=[];for(let I=0;I<t.length;I++){const ne=t[I].replace(/([A-Z])/g," $1").replace(/^./,Q=>Q.toUpperCase()).trim();s.push(ne)}let i="";for(let I=0;I<s.length;I++)i+="<th>"+s[I]+"</th>";let n="";for(let I=0;I<e.length;I++){let ne="<tr>";for(let Q=0;Q<t.length;Q++)ne+="<td>"+(e[I][t[Q]]||"")+"</td>";ne+="</tr>",n+=ne}const a='<!DOCTYPE html><html><head><meta charset="utf-8"><title>Usuários Matriculados</title>',u="<style>body{font-family:Arial,sans-serif;margin:20px;color:#333}h1{color:#2c3e50;text-align:center;margin-bottom:20px}table{border-collapse:collapse;width:100%;margin-bottom:20px;box-shadow:0 0 20px rgba(0,0,0,.1)}th,td{border:1px solid #ddd;padding:12px;text-align:left}th{background-color:#3498db;color:white;font-weight:bold;text-transform:uppercase;font-size:14px}tr:nth-child(even){background-color:#f2f2f2}tr:hover{background-color:#e9f7fe}.footer{text-align:center;margin-top:20px;font-size:12px;color:#7f8c8d}</style>",c="</head><body><h1>Usuários Matriculados</h1>",h="<table><thead><tr>",m="</tr></thead><tbody>",p="</tbody></table>",v='<div class="footer">Gerado em '+new Date().toLocaleString()+"</div>",w="</body></html>",D=a+u+c+h+i+m+n+p+v+w,k=new Blob([D],{type:"text/html;charset=utf-8;"}),L=URL.createObjectURL(k),re=document.createElement("a");re.setAttribute("href",L),re.setAttribute("download","usuarios_matriculados.html"),re.style.visibility="hidden",document.body.appendChild(re),re.click(),document.body.removeChild(re),URL.revokeObjectURL(L),this.showSuccessMessage("Download concluído. O arquivo HTML foi salvo com sucesso.")},downloadJSON(e){if(e.length===0)return;const t=JSON.stringify(e,null,2),s=new Blob([t],{type:"application/json;charset=utf-8;"}),i=URL.createObjectURL(s),n=document.createElement("a");n.setAttribute("href",i),n.setAttribute("download","usuarios_matriculados.json"),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(i)},downloadODS(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]);let i=[];i.push(s.join(",")),e.forEach(h=>{const m=s.map(p=>{const v=h[p]||"";return'"'+String(v).replace(/"/g,'""')+'"'});i.push(m.join(","))});const n=t+i.join(`
`),a=new Blob([n],{type:"text/csv;charset=utf-8;"}),u=URL.createObjectURL(a),c=document.createElement("a");c.setAttribute("href",u),c.setAttribute("download","usuarios_matriculados.csv"),c.style.visibility="hidden",document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(u),this.showSuccessMessage("Download concluído. O arquivo CSV pode ser importado no LibreOffice Calc para salvar como ODS.")},downloadPDF(e){e.length!==0&&(this.downloadHTML(e),this.showSuccessMessage("Página HTML aberta. Use a função de impressão do navegador (Ctrl+P) para salvar como PDF."))},editEnrolments(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showErrorMessage("Por favor, selecione pelo menos um usuário para editar matrícula.");return}if(this.selectedUsers.length===1){const e=this.selectedUsers[0],t=this.enrolments.find(s=>s.id===e);t?this.editUser(t):this.showErrorMessage("Usuário não encontrado. Por favor, tente novamente.")}else this.showBulkEditEnrollmentModal=!0},async handleBulkEditEnrollmentSuccess(e){this.showSuccessMessage(e.message||"Matrículas editadas com sucesso."),await this.loadRegisteredUsers(),this.selectedUsers=[],this.showBulkEditEnrollmentModal=!1},handleBulkEditEnrollmentError(e){const t="Não foi possível editar as matrículas. Por favor, tente novamente.";this.showErrorMessage(e||t)},bulkDeleteEnrollment(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para excluir matrícula.");return}this.showBulkDeleteEnrollmentModal=!0},handleBulkDeleteEnrollmentError(e){const t="Não foi possível excluir as matrículas. Por favor, tente novamente.";this.showErrorMessage(e||t)},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showWarningMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="warning",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},cancelUser(e){if(!e||!e.canCancel){this.showErrorMessage("Este usuário não pode ter sua matrícula cancelada.");return}this.selectedUser={id:e.id,offeruserenrolid:e.offeruserenrolid,fullName:e.fullName,email:e.email,cpf:e.cpf,status:e.status,statusName:e.statusName},this.showCancelEnrollmentModal=!0},async confirmCancelEnrollment(e){try{const t=await rE(e.user.offeruserenrolid,e.reason);t.canceled?(this.showSuccessMessage(t.message),await this.loadRegisteredUsers()):this.showErrorMessage(t.message)}catch(t){this.showErrorMessage(t.message||"Erro ao cancelar matrícula. Tente novamente.")}finally{this.$refs.cancelModal&&(this.$refs.cancelModal.isSubmitting=!1),this.showCancelEnrollmentModal=!1,this.selectedUser=null,this.loadRegisteredUsers()}}}},Hx={id:"offer-manager-component",class:"offer-manager"},qx={class:"page-header-controls"},zx={class:"page-view-selector"},Wx={class:"filters-section mb-3"},Gx={class:"row"},Kx={class:"col-md-3 mb-sm-0 mb-3"},Qx={class:"filter-input-container position-relative"},Yx={key:0,class:"dropdown-menu show position-absolute w-100",style:{top:"100%","z-index":"1000","max-height":"200px","overflow-y":"auto"}},Zx=["onClick"],Jx={class:"col-md-3 mb-sm-0 mb-3"},Xx={class:"filter-input-container position-relative"},eS={key:0,class:"dropdown-menu show position-absolute w-100",style:{top:"100%","z-index":"1000","max-height":"200px","overflow-y":"auto"}},tS=["onClick"],sS={class:"col-md-3 mb-sm-0 mb-3"},rS={class:"filter-input-container position-relative"},nS={key:0,class:"dropdown-menu show position-absolute w-100",style:{top:"100%","z-index":"1000","max-height":"200px","overflow-y":"auto"}},oS=["onClick"],iS={key:0,class:"my-4"},aS={key:1,class:"alert alert-danger"},lS={class:"table-container"},uS={class:"checkbox-container"},cS=["checked","indeterminate"],dS={class:"checkbox-container"},fS=["checked","onChange"],hS=["href","title"],pS={class:"user-name-link"},mS={class:"progress-container"},gS={class:"progress-text"},vS={class:"status-container"},_S={class:"status-actions"},bS=["onClick"],yS=["onClick"],wS=["disabled","onClick"],ES={class:"selected-users-actions"},CS={class:"bulk-actions-container"},DS={key:2,class:"bottom-enroll-button"};function xS(e,t,s,i,n,a){var Ae,ue,ae;const u=X("BackButton"),c=X("PageHeader"),h=X("HierarchicalSelect"),m=X("CustomButton"),p=X("FilterTag"),v=X("FilterTags"),w=X("UserAvatar"),D=X("RoleSelector"),k=X("CustomTable"),L=X("Pagination"),re=X("EnrollmentDetailsModal"),I=X("EnrolmentModalNew"),ne=X("EditEnrollmentModal"),Q=X("BulkEditEnrollmentModal"),ye=X("BulkDeleteEnrollmentModal"),Z=X("CancelEnrollmentModal"),he=X("LFLoading"),be=X("Toast");return S(),O("div",Hx,[M(c,{title:"Usuários matriculados"},{actions:Ne(()=>[M(u,{onClick:a.goBack},null,8,["onClick"])]),_:1}),d("div",qx,[d("div",zx,[M(h,{modelValue:n.selectedPageView,"onUpdate:modelValue":t[0]||(t[0]=V=>n.selectedPageView=V),options:n.pageViewOptions,onNavigate:a.handlePageViewChange},null,8,["modelValue","options","onNavigate"])]),!n.classDetails||((Ae=n.classDetails)==null?void 0:Ae.operational_cycle)!==2?(S(),Ut(m,{key:0,variant:"primary",label:"Matricular usuários",onClick:a.addNewUser},null,8,["onClick"])):ee("",!0)]),d("div",Wx,[d("div",Gx,[d("div",Kx,[d("div",Qx,[t[22]||(t[22]=d("label",{for:"name-filter",class:"form-label text-muted small"},"Filtrar por nome",-1)),ut(d("input",{id:"name-filter",type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[1]||(t[1]=V=>n.nameSearchInput=V),onInput:t[2]||(t[2]=(...V)=>a.handleNameInput&&a.handleNameInput(...V)),onFocus:t[3]||(t[3]=V=>n.showNameDropdown=n.nameOptions.length>0),onBlur:t[4]||(t[4]=V=>a.clearOptions("name"))},null,544),[[Xt,n.nameSearchInput]]),n.showNameDropdown&&n.nameOptions.length>0?(S(),O("div",Yx,[(S(!0),O(Ie,null,at(n.nameOptions,V=>(S(),O("button",{key:V.id,type:"button",class:"dropdown-item",onClick:te=>a.selectNameOption(V)},q(V.label),9,Zx))),128))])):ee("",!0)])]),d("div",Jx,[d("div",Xx,[t[23]||(t[23]=d("label",{for:"cpf-filter",class:"form-label text-muted small"},"Filtrar por CPF",-1)),ut(d("input",{id:"cpf-filter",type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[5]||(t[5]=V=>n.cpfSearchInput=V),onInput:t[6]||(t[6]=(...V)=>a.handleCpfInput&&a.handleCpfInput(...V)),onFocus:t[7]||(t[7]=V=>n.showCpfDropdown=n.cpfOptions.length>0),onBlur:t[8]||(t[8]=V=>a.clearOptions("cpf"))},null,544),[[Xt,n.cpfSearchInput]]),n.showCpfDropdown&&n.cpfOptions.length>0?(S(),O("div",eS,[(S(!0),O(Ie,null,at(n.cpfOptions,V=>(S(),O("button",{key:V.id,type:"button",class:"dropdown-item",onClick:te=>a.selectCpfOption(V)},q(V.label),9,tS))),128))])):ee("",!0)])]),d("div",sS,[d("div",rS,[t[24]||(t[24]=d("label",{for:"email-filter",class:"form-label text-muted small"},"Filtrar por E-mail",-1)),ut(d("input",{id:"email-filter",type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[9]||(t[9]=V=>n.emailSearchInput=V),onInput:t[10]||(t[10]=(...V)=>a.handleEmailInput&&a.handleEmailInput(...V)),onFocus:t[11]||(t[11]=V=>n.showEmailDropdown=n.emailOptions.length>0),onBlur:t[12]||(t[12]=V=>a.clearOptions("email"))},null,544),[[Xt,n.emailSearchInput]]),n.showEmailDropdown&&n.emailOptions.length>0?(S(),O("div",nS,[(S(!0),O(Ie,null,at(n.emailOptions,V=>(S(),O("button",{key:V.id,type:"button",class:"dropdown-item",onClick:te=>a.selectEmailOption(V)},q(V.label),9,oS))),128))])):ee("",!0)])])])]),M(v,null,{default:Ne(()=>[(S(!0),O(Ie,null,at(n.filteredUsers,V=>(S(),Ut(p,{key:V.id,onRemove:te=>a.removeFilter(V.id||V.value)},{default:Ne(()=>[Ue(q(V.label),1)]),_:2},1032,["onRemove"]))),128))]),_:1}),n.filteredUsers.length>0?(S(),O("div",iS,[d("button",{type:"button",class:"btn btn-secondary",onClick:t[13]||(t[13]=(...V)=>a.clearFilteredUsers&&a.clearFilteredUsers(...V))}," Limpar ")])):ee("",!0),n.error?(S(),O("div",aS,[t[25]||(t[25]=d("i",{class:"fas fa-exclamation-circle"},null,-1)),Ue(" "+q(n.error),1)])):ee("",!0),d("div",lS,[M(k,{headers:n.tableHeaders,items:n.enrolments,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"header-select":Ne(()=>[d("div",uS,[d("input",{type:"checkbox",checked:a.allSelected,indeterminate:a.someSelected&&!a.allSelected,onChange:t[14]||(t[14]=(...V)=>a.toggleSelectAll&&a.toggleSelectAll(...V)),class:"custom-checkbox"},null,40,cS)])]),"item-select":Ne(({item:V})=>[d("div",dS,[d("input",{type:"checkbox",checked:a.isSelected(V.id),onChange:te=>a.toggleSelectUser(V.id),class:"custom-checkbox"},null,40,fS)])]),"item-fullName":Ne(({item:V})=>[d("a",{class:"user-name-container",href:`/user/view.php?id=${V.id}`,title:"Ver perfil de "+V.fullName},[M(w,{"full-name":V.fullName,size:36},null,8,["full-name"]),d("span",pS,q(V.fullName),1)],8,hS)]),"item-email":Ne(({item:V})=>[Ue(q(V.email),1)]),"item-cpf":Ne(({item:V})=>[Ue(q(V.cpf),1)]),"item-roles":Ne(({item:V})=>[M(D,{userId:V.id,offeruserenrolid:V.offeruserenrolid,currentRole:V.roles,offerclassid:n.offerclassid,onSuccess:a.handleRoleUpdateSuccess,onError:a.handleRoleUpdateError,onReloadTable:a.reloadTable},null,8,["userId","offeruserenrolid","currentRole","offerclassid","onSuccess","onError","onReloadTable"])]),"item-groups":Ne(({item:V})=>[Ue(q(V.groups),1)]),"item-startDate":Ne(({item:V})=>[Ue(q(V.startDate),1)]),"item-endDate":Ne(({item:V})=>[Ue(q(V.endDate),1)]),"item-deadline":Ne(({item:V})=>[Ue(q(V.deadline),1)]),"item-progress":Ne(({item:V})=>[d("div",mS,[d("div",{class:"progress-bar",style:us({width:V.progress})},null,4),d("span",gS,q(V.progress),1)])]),"item-situation":Ne(({item:V})=>[Ue(q(V.situationName),1)]),"item-grade":Ne(({item:V})=>[Ue(q(V.grade),1)]),"item-status":Ne(({item:V})=>[d("div",vS,[d("span",{class:pe(["status-tag badge",V.status===0?"badge-success":"badge-danger"])},q(V.statusName),3),d("div",_S,[d("button",{class:"btn-information",onClick:te=>a.showEnrollmentDetails(V),title:"Informações da matrícula"},t[26]||(t[26]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round",class:"custom-icon"},[d("circle",{cx:"12",cy:"12",r:"10"}),d("line",{x1:"12",y1:"16",x2:"12",y2:"12"}),d("line",{x1:"12",y1:"8",x2:"12.01",y2:"8"})],-1)]),8,bS),d("button",{class:"btn-settings",onClick:te=>a.editUser(V),title:"Editar matrícula"},t[27]||(t[27]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round",class:"custom-icon"},[d("circle",{cx:"12",cy:"12",r:"3"}),d("path",{d:"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"})],-1)]),8,yS),d("a",{role:"button",class:"btn-link p-0",disabled:V.canCancel==!1,onClick:te=>a.cancelUser(V),title:"Cancelar matrícula"},t[28]||(t[28]=[d("i",{class:"fa fa-times-circle-o text-danger"},null,-1)]),8,wS)])])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"])]),M(L,{"current-page":n.currentPage,"onUpdate:currentPage":t[15]||(t[15]=V=>n.currentPage=V),"per-page":n.perPage,"onUpdate:perPage":t[16]||(t[16]=V=>n.perPage=V),total:n.totalEnrolments,loading:n.loading},null,8,["current-page","per-page","total","loading"]),d("div",ES,[d("div",CS,[t[30]||(t[30]=d("label",{for:"bulk-actions"},"Com usuários selecionados...",-1)),ut(d("select",{id:"bulk-actions",class:"form-control bulk-select","onUpdate:modelValue":t[17]||(t[17]=V=>n.selectedBulkAction=V),onChange:t[18]||(t[18]=(...V)=>a.handleBulkAction&&a.handleBulkAction(...V))},t[29]||(t[29]=[nb('<option value="" data-v-01a78f02>Escolher...</option><optgroup label="Comunicação" data-v-01a78f02><option value="message" data-v-01a78f02>Enviar uma mensagem</option><option value="note" data-v-01a78f02>Escrever uma nova anotação</option></optgroup><optgroup label="Baixar dados da tabela como:" data-v-01a78f02><option value="download_csv" data-v-01a78f02> Valores separados por vírgula (.csv) </option><option value="download_xlsx" data-v-01a78f02>Microsoft excel (.xlsx)</option><option value="download_html" data-v-01a78f02>Tabela HTML</option><option value="download_json" data-v-01a78f02> JavaScript Object Notation (.json) </option><option value="download_ods" data-v-01a78f02>OpenDocument (.ods)</option><option value="download_pdf" data-v-01a78f02> Formato de documento portável (.pdf) </option></optgroup><optgroup label="Inscrições" data-v-01a78f02><option value="edit_enrolment" data-v-01a78f02> Editar matrículas de usuários selecionados </option><option value="delete_enrolment" data-v-01a78f02> Excluir matrículas de usuários selecionados </option></optgroup>',4)]),544),[[Kl,n.selectedBulkAction]])])]),!n.classDetails||((ue=n.classDetails)==null?void 0:ue.operational_cycle)!==2?(S(),O("div",DS,[M(m,{variant:"primary",label:"Matricular usuários",onClick:a.addNewUser},null,8,["onClick"])])):ee("",!0),M(re,{show:n.showEnrollmentModal,user:n.selectedUser,"course-name":((ae=n.classDetails)==null?void 0:ae.course_fullname)||"",onClose:a.closeEnrollmentModal},null,8,["show","user","course-name","onClose"]),M(I,{show:n.showEnrolmentModal,offerclassid:n.offerclassid,roles:n.roleOptions,onClose:a.closeEnrolmentModal,onSuccess:a.handleEnrolmentSuccess},null,8,["show","offerclassid","roles","onClose","onSuccess"]),M(ne,{show:n.showEditEnrollmentModal,user:n.selectedUser,offerclassid:n.offerclassid,onClose:a.closeEditEnrollmentModal,onSuccess:a.handleEditEnrollmentSuccess,onError:a.handleEditEnrollmentError},null,8,["show","user","offerclassid","onClose","onSuccess","onError"]),M(Q,{show:n.showBulkEditEnrollmentModal,users:n.selectedUsers.map(V=>n.enrolments.find(te=>te.id===V)).filter(Boolean),offerclassid:n.offerclassid,onClose:t[19]||(t[19]=V=>this.showBulkEditEnrollmentModal=!1),onSuccess:a.handleBulkEditEnrollmentSuccess,onError:a.handleBulkEditEnrollmentError},null,8,["show","users","offerclassid","onSuccess","onError"]),M(ye,{show:n.showBulkDeleteEnrollmentModal,users:n.selectedUsers.map(V=>n.enrolments.find(te=>te.id===V)).filter(Boolean),offerclassid:n.offerclassid,onClose:t[20]||(t[20]=V=>n.showBulkDeleteEnrollmentModal=!1),onConfirm:a.confirmeBulkDeleteEnrollment,onError:a.handleBulkDeleteEnrollmentError},null,8,["show","users","offerclassid","onConfirm","onError"]),M(Z,{ref:"cancelModal",show:n.showCancelEnrollmentModal,user:n.selectedUser,offerclassid:n.offerclassid,onClose:t[21]||(t[21]=V=>n.showCancelEnrollmentModal=!1),onConfirm:a.confirmCancelEnrollment,onError:e.handleCancelEnrollmentError},null,8,["show","user","offerclassid","onConfirm","onError"]),M(he,{"is-loading":n.loading},null,8,["is-loading"]),M(be,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])])}const SS=je(jx,[["render",xS],["__scopeId","data-v-01a78f02"]]),aR="",OS={name:"CollapsibleTable",props:{headers:{type:Array,required:!0},items:{type:Array,required:!0},sortBy:{type:String,default:""},sortDesc:{type:Boolean,default:!1},expandable:{type:Boolean,default:!1}},data(){return{expandedRows:[]}},methods:{handleSort(e){this.$emit("sort",{sortBy:e,sortDesc:this.sortBy===e?!this.sortDesc:!1})},toggleExpand(e){const t=this.expandedRows.indexOf(e);t===-1?this.expandedRows.push(e):this.expandedRows.splice(t,1)}}},TS={class:"table-responsive"},NS={class:"table"},IS={key:0,class:"expand-column"},AS=["onClick","data-value"],MS={key:0,class:"sort-icon"},PS={key:0},kS={key:0,class:"expand-column"},VS=["onClick","title"],RS=["colspan"],US={class:"expanded-content"},FS={key:1},LS=["colspan"];function BS(e,t,s,i,n,a){return S(),O("div",TS,[d("table",NS,[d("thead",null,[d("tr",null,[s.expandable?(S(),O("th",IS)):ee("",!0),(S(!0),O(Ie,null,at(s.headers,u=>(S(),O("th",{key:u.value,onClick:c=>u.sortable?a.handleSort(u.value):null,class:pe({sortable:u.sortable}),"data-value":u.value},[Ue(q(u.text)+" ",1),u.sortable?(S(),O("span",MS,[d("i",{class:pe(["fas",{"fa-sort":s.sortBy!==u.value,"fa-sort-up":s.sortBy===u.value&&!s.sortDesc,"fa-sort-down":s.sortBy===u.value&&s.sortDesc}])},null,2)])):ee("",!0)],10,AS))),128))])]),s.items.length>0?(S(),O("tbody",PS,[(S(!0),O(Ie,null,at(s.items,(u,c)=>(S(),O(Ie,{key:u.id},[d("tr",{class:pe({expanded:n.expandedRows.includes(u.id)})},[s.expandable?(S(),O("td",kS,[d("button",{class:"btn-expand",onClick:h=>a.toggleExpand(u.id),title:n.expandedRows.includes(u.id)?"Recolher":"Expandir"},[d("div",{class:pe(["icon-container",{"is-expanded":n.expandedRows.includes(u.id)}])},t[0]||(t[0]=[d("svg",{width:"16",height:"16",viewBox:"0 0 24 24",class:"expand-icon"},[d("rect",{x:"5",y:"11",width:"14",height:"2",fill:"var(--primary)"}),d("rect",{x:"11",y:"5",width:"2",height:"14",fill:"var(--primary)",class:"vertical-line"})],-1)]),2)],8,VS)])):ee("",!0),(S(!0),O(Ie,null,at(s.headers,h=>(S(),O("td",{key:`${u.id}-${h.value}`},[Rt(e.$slots,"item-"+h.value,{item:u},()=>[Ue(q(u[h.value]),1)],!0)]))),128))],2),s.expandable?(S(),O("tr",{key:0,class:pe(["expanded-row",{"is-visible":n.expandedRows.includes(u.id)}])},[d("td",{colspan:s.headers.length+1},[d("div",US,[Rt(e.$slots,"expanded-content",{item:u},void 0,!0)])],8,RS)],2)):ee("",!0)],64))),128))])):(S(),O("tbody",FS,[d("tr",null,[d("td",{colspan:s.headers.length+(s.expandable?1:0)},[Rt(e.$slots,"empty-state",{},()=>[t[1]||(t[1]=d("div",{class:"empty-state"},[d("span",null,"Não existem registros")],-1))],!0)],8,LS)])]))])])}const $S=je(OS,[["render",BS],["__scopeId","data-v-05038124"]]),lR="",uR="",jS={name:"AddCourseModal",components:{CustomInput:Fo,CustomButton:Ln,CustomTable:hn,Pagination:pn,Autocomplete:$o,FilterTag:Bo},props:{modelValue:{type:Boolean,required:!0},offerId:{type:Number,required:!0}},emits:["update:modelValue","confirm"],data(){return{selectedCategory:null,selectedCategoryObject:null,selectedCourse:null,categoryOptions:[],courseOptions:[],currentPage:1,perPage:5,sortBy:"name",sortDesc:!1,loadingCategories:!1,loadingCourses:!1,loadingCurrentCourses:!1,coursesPage:1,coursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,loadingMoreCourses:!1,tableHeaders:[{text:"CURSO",value:"name",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1,align:"right"}],selectedCoursesPreview:[],existingCourses:[]}},computed:{filteredCourses(){const e=[...this.selectedCoursesPreview].sort((i,n)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<n[this.sortBy]?-1*a:i[this.sortBy]>n[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)},totalPages(){return Math.ceil(this.selectedCoursesPreview.length/this.perPage)},courseNoResultsText(){return this.loadingCourses?"Buscando cursos...":this.loadingMoreCourses?"Carregando mais cursos...":this.selectedCategory?this.courseOptions.length===0&&this.selectedCategory?"Todos os cursos já foram adicionados":"Nenhum curso encontrado":"Selecione uma categoria primeiro"}},watch:{modelValue(e){e?(this.loadCurrentCourses(),this.loadAllCategories()):(this.selectedCategory=null,this.selectedCategoryObject=null,this.selectedCourse=null,this.categoryOptions=[],this.courseOptions=[],this.selectedCoursesPreview=[])},selectedCategory(e){e||(this.courseOptions=[],this.selectedCourse=null,this.selectedCategoryObject=null,this.loadCurrentCourses())},courseOptions(e){e.length<10&&this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses&&this.$nextTick(()=>{this.loadMoreCourses(),console.log("Carregando mais cursos... via watch")})}},methods:{closeModal(){this.$emit("update:modelValue",!1),this.selectedCategory=null,this.selectedCategoryObject=null,this.selectedCourse=null,this.selectedCoursesPreview=[]},async confirm(){try{if(this.selectedCoursesPreview.length===0){console.warn("Nenhum curso selecionado para adicionar"),this.closeModal();return}const e=this.selectedCoursesPreview.map(t=>t.id);await nw(this.offerId,e),this.$emit("confirm",this.selectedCoursesPreview),this.closeModal()}catch(e){console.error("Erro ao salvar cursos:",e)}},async loadCurrentCourses(){console.log("loadCurrentCourses this.offerId:",this.offerId);try{this.loadingCurrentCourses=!0;const e=await fu(this.offerId);console.log("loadCurrentCourses response:",e),e&&e.data&&(Array.isArray(e.data)?this.existingCourses=e.data.map(t=>({id:t.courseid,name:t.fullname,offerCourseId:t.id})):this.existingCourses=[])}catch(e){console.error("Erro ao carregar cursos da oferta:",e),this.existingCourses=[]}finally{this.loadingCurrentCourses=!1}},async loadAllCategories(){try{this.loadingCategories=!0,this.categoryOptions=[];const e=await Uo("");e&&e.data&&(this.categoryOptions=e.data.map(t=>({value:t.id,label:t.name})))}catch(e){console.error("Erro ao carregar todas as categorias:",e),this.categoryOptions=[]}finally{this.loadingCategories=!1}},handleCategorySelect(e){if(!e){this.removeCategory();return}this.selectedCategoryObject=e,this.selectedCategory=e.value,this.courseOptions=[],this.selectedCourse=null,this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadCoursesForCategory(e.value)},async loadCoursesForCategory(e,t=1,s=!1,i=""){if(!e)return;const n=typeof e=="object"?e.value:e;try{t===1?(this.loadingCourses=!0,s||(this.courseOptions=[])):this.loadingMoreCourses=!0;const a=await rp(this.offerId,n,i,t,this.coursesPerPage);let u=null,c=[];try{if(Array.isArray(a)&&a.length>0?a[0].error===!1&&a[0].data?a[0].data.courses?(u=a[0].data,c=u.courses||[]):Array.isArray(a[0].data)?(c=a[0].data,u={page:1,total_pages:1}):a[0].data.data&&Array.isArray(a[0].data.data)&&(c=a[0].data.data,u={page:a[0].data.page||1,total_pages:a[0].data.total_pages||1}):(c=a,u={page:1,total_pages:1}):a&&typeof a=="object"&&(a.data&&a.data.courses?(c=a.data.courses,u={page:a.data.page||1,total_pages:a.data.total_pages||1}):a.courses?(c=a.courses,u={page:a.page||1,total_pages:a.total_pages||1}):a.data&&Array.isArray(a.data)&&(c=a.data,u={page:1,total_pages:1})),c.length===0&&a&&typeof a=="object"&&!Array.isArray(a)){for(const h in a)if(Array.isArray(a[h])){c=a[h],u={page:1,total_pages:1};break}}}catch(h){console.error("Erro ao processar resposta:",h)}if(u){if(this.coursesPage=u.page||1,this.coursesTotalPages=u.total_pages||1,this.hasMoreCourses=(u.page||1)<(u.total_pages||1),c&&c.length>0){const m=c.filter(p=>!this.existingCourses.some(v=>v.id===p.id)&&!this.selectedCoursesPreview.some(v=>v.id===p.id)).map(p=>({value:p.id,label:p.fullname}));s?this.courseOptions=[...this.courseOptions,...m]:this.courseOptions=m}}else console.warn("Formato de resposta inesperado")}catch(a){console.error("Erro ao carregar cursos da categoria:",a),s||(this.courseOptions=[])}finally{t===1?this.loadingCourses=!1:this.loadingMoreCourses=!1}},async loadMoreCourses(){if(this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses){const e=this.coursesPage+1;await this.loadCoursesForCategory(this.selectedCategory,e,!0)}},async handleCourseSearch(e){this.selectedCategory&&(console.log("Buscando cursos com termo:",e),this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,await this.loadCoursesForCategory(this.selectedCategory,1,!1,e||""))},handleCourseSelect(e){e&&!this.selectedCoursesPreview.some(t=>t.id===e.value)&&(this.selectedCoursesPreview.push({id:e.value,name:e.label}),this.courseOptions=this.courseOptions.filter(t=>t.value!==e.value),this.currentPage=1),this.selectedCourse=null},removeCourse(e){const t=this.selectedCoursesPreview.findIndex(s=>s.id===e.id);if(t!==-1){const s=this.selectedCoursesPreview.splice(t,1)[0];this.currentPage>1&&this.currentPage>Math.ceil(this.selectedCoursesPreview.length/this.perPage)&&(this.currentPage=Math.max(1,this.currentPage-1)),this.selectedCategory?rp(this.offerId,this.selectedCategory,"",1,this.coursesPerPage).then(i=>{let n=[];try{if(Array.isArray(i)&&i.length>0?i[0].error===!1&&i[0].data?i[0].data.courses?n=i[0].data.courses||[]:Array.isArray(i[0].data)?n=i[0].data:i[0].data.data&&Array.isArray(i[0].data.data)&&(n=i[0].data.data):n=i:i&&typeof i=="object"&&(i.data&&i.data.courses?n=i.data.courses:i.courses?n=i.courses:i.data&&Array.isArray(i.data)&&(n=i.data)),n.length===0&&i&&typeof i=="object"&&!Array.isArray(i)){for(const a in i)if(Array.isArray(i[a])){n=i[a];break}}}catch(a){console.error("Erro ao processar resposta em removeCourse:",a)}n&&n.length>0&&n.find(u=>u.id===s.id)&&this.courseOptions.push({value:s.id,label:s.name})}).catch(i=>{console.error("Erro ao verificar categoria do curso:",i)}):this.courseOptions.push({value:s.id,label:s.name})}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t},handlePageChange(e){this.currentPage=e},handlePerPageChange(e){this.perPage=e,this.currentPage=1},removeCategory(){this.selectedCategory=null,this.selectedCategoryObject=null,this.selectedCourse=null,this.courseOptions=[],this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadCurrentCourses()}}},HS={class:"modal-header"},qS={class:"modal-body"},zS={class:"search-section"},WS={class:"search-group"},GS={class:"search-group"},KS={class:"table-container"},QS={key:0,class:"empty-preview-message"},YS={class:"action-buttons"},ZS=["onClick"],JS={class:"modal-footer"};function XS(e,t,s,i,n,a){const u=X("Autocomplete"),c=X("CustomTable"),h=X("Pagination"),m=X("CustomButton");return s.modelValue?(S(),O("div",{key:0,class:"modal-overlay",onClick:t[6]||(t[6]=(...p)=>a.closeModal&&a.closeModal(...p))},[d("div",{class:"modal-content",onClick:t[5]||(t[5]=Pt(()=>{},["stop"]))},[d("div",HS,[t[8]||(t[8]=d("h2",null,"Adicionar curso",-1)),d("button",{class:"close-button",onClick:t[0]||(t[0]=(...p)=>a.closeModal&&a.closeModal(...p))},t[7]||(t[7]=[d("i",{class:"fas fa-times"},null,-1)]))]),d("div",qS,[t[11]||(t[11]=d("h3",{class:"section-title"},"SELECIONAR CURSO",-1)),d("div",zS,[d("div",WS,[M(u,{modelValue:n.selectedCategory,"onUpdate:modelValue":t[1]||(t[1]=p=>n.selectedCategory=p),items:n.categoryOptions,label:"Categoria",placeholder:"Pesquisar...","input-max-width":null,loading:n.loadingCategories,"show-filter-tags":!1,"show-selected-in-input":!0,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":n.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada",onSelect:a.handleCategorySelect},null,8,["modelValue","items","loading","no-results-text","onSelect"])]),d("div",GS,[M(u,{modelValue:n.selectedCourse,"onUpdate:modelValue":t[2]||(t[2]=p=>n.selectedCourse=p),items:n.courseOptions,label:"Curso",placeholder:"Pesquisar...","input-max-width":null,disabled:!n.selectedCategory,loading:n.loadingCourses||n.loadingMoreCourses,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"keep-open-on-select":!0,"no-results-text":a.courseNoResultsText,onSelect:a.handleCourseSelect,onLoadMore:a.loadMoreCourses,onSearch:a.handleCourseSearch,ref:"courseAutocomplete"},null,8,["modelValue","items","disabled","loading","no-results-text","onSelect","onLoadMore","onSearch"])])]),d("div",KS,[n.selectedCoursesPreview.length===0?(S(),O("div",QS,t[9]||(t[9]=[d("p",null,"Selecione cursos acima para adicioná-los à oferta",-1)]))):(S(),Ut(c,{key:1,headers:n.tableHeaders,items:a.filteredCourses,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"item-actions":Ne(({item:p})=>[d("div",YS,[d("button",{class:"btn-action btn-delete",onClick:v=>a.removeCourse(p),title:"Remover da lista"},t[10]||(t[10]=[d("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,ZS)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]))]),n.selectedCoursesPreview.length>0?(S(),Ut(h,{key:0,"current-page":n.currentPage,"onUpdate:currentPage":[t[3]||(t[3]=p=>n.currentPage=p),a.handlePageChange],"per-page":n.perPage,"onUpdate:perPage":[t[4]||(t[4]=p=>n.perPage=p),a.handlePerPageChange],total:n.selectedCoursesPreview.length},null,8,["current-page","per-page","total","onUpdate:currentPage","onUpdate:perPage"])):ee("",!0)]),d("div",JS,[M(m,{variant:"primary",label:"Confirmar",disabled:n.selectedCoursesPreview.length===0,onClick:a.confirm},null,8,["disabled","onClick"]),M(m,{variant:"secondary",label:"Cancelar",onClick:a.closeModal},null,8,["onClick"])])])])):ee("",!0)}const eO=je(jS,[["render",XS],["__scopeId","data-v-b7f62eb9"]]),cR="",dR="",tO={name:"DuplicateClassModal",components:{Autocomplete:$o,CustomTable:hn,Pagination:pn},props:{show:{type:Boolean,default:!1},turma:{type:Object,default:null},parentCourse:{type:Object,default:null},offerId:{type:[Number,String],default:null}},emits:["close","success","loading","error"],data(){return{selectedCategory:null,selectedCategoryObject:null,categoryOptions:[],loadingCategories:!1,selectedCourse:null,targetCourseOptions:[],selectedCoursesPreview:[],loadingCourses:!1,loadingMoreCourses:!1,coursesPage:1,coursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,currentPage:1,perPage:5,sortBy:"label",sortDesc:!1,tableHeaders:[{text:"CURSO",value:"label",sortable:!0},{text:"CATEGORIA",value:"category_name",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1,align:"right"}],duplicatingCourses:!1,duplicatedCount:0,totalToDuplicate:0,existingCourses:[]}},computed:{courseNoResultsText(){return this.selectedCategory?this.loadingCourses||this.loadingMoreCourses?"Carregando cursos...":this.targetCourseOptions.length===0?"Nenhum curso disponível":"Nenhum curso encontrado":"Selecione uma categoria primeiro"},filteredCourses(){const e=(this.currentPage-1)*this.perPage,t=e+this.perPage;return[...this.selectedCoursesPreview].sort((i,n)=>{const a=i[this.sortBy],u=n[this.sortBy];return a<u?this.sortDesc?1:-1:a>u?this.sortDesc?-1:1:0}).slice(e,t)}},watch:{show(e){e&&this.turma&&this.parentCourse?(this.resetForm(),this.$nextTick(()=>{this.loadAllCategories()})):this.resetForm()},turma(){this.show&&(this.resetForm(),this.loadAllCategories())},parentCourse(){this.show&&(this.resetForm(),this.loadAllCategories())},selectedCategory(e){e?this.loadCoursesForCategory(e):(this.targetCourseOptions=[],this.selectedCourse=null,this.selectedCategoryObject=null)}},methods:{resetForm(){this.selectedCategory=null,this.selectedCategoryObject=null,this.categoryOptions=[],this.loadingCategories=!1,this.selectedCourse=null,this.targetCourseOptions=[],this.selectedCoursesPreview=[],this.loadingCourses=!1,this.loadingMoreCourses=!1,this.coursesPage=1,this.coursesPerPage=20,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.currentPage=1,this.duplicatingCourses=!1,this.duplicatedCount=0,this.totalToDuplicate=0,this.existingCourses=[]},async loadAllCategories(){try{this.loadingCategories=!0,this.categoryOptions=[];const e=await Uo("",this.offerId);e&&e.data&&(this.categoryOptions=e.data.map(t=>({value:t.id,label:t.name})))}catch(e){console.error("Erro ao carregar categorias:",e),this.$emit("error","Erro ao carregar categorias. Por favor, tente novamente."),this.categoryOptions=[]}finally{this.loadingCategories=!1}},handleCategorySelect(e){if(!e){this.removeCategory();return}this.selectedCategoryObject=e,this.selectedCategory=e.value,this.targetCourseOptions=[],this.selectedCourse=null,this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1},removeCategory(){this.selectedCategory=null,this.selectedCategoryObject=null,this.selectedCourse=null,this.targetCourseOptions=[],this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1},async loadCoursesForCategory(e,t=1,s=!1,i=""){if(!e||!this.turma)return;const n=typeof e=="object"?e.value:e;try{t===1?(this.loadingCourses=!0,s||(this.targetCourseOptions=[])):this.loadingMoreCourses=!0;const a=await hw(this.turma.id);let u=[];a&&Array.isArray(a)&&(u=a);const c=this.parentCourse.offerCourseId||this.parentCourse.id;u=u.filter(m=>{const p=m.categoryid,v=String(p)===String(n),w=m.offercourseid||m.id,D=String(w)!==String(c),k=!i||m.name&&m.name.toLowerCase().includes(i.toLowerCase())||m.fullname&&m.fullname.toLowerCase().includes(i.toLowerCase());return v&&D&&k}),u=u.filter(m=>{const p=m.offercourseid||m.id;return!this.selectedCoursesPreview.some(v=>String(v.value)===String(p))});const h=u.map(m=>{let p=m.offercourseid||m.id;return p==null?null:{value:p,label:m.name||m.fullname||m.coursename||`Curso ${p}`,categoryid:m.categoryid,category_name:m.category_name}}).filter(m=>m!==null);s?this.targetCourseOptions=[...this.targetCourseOptions,...h]:this.targetCourseOptions=h,this.hasMoreCourses=h.length>=this.coursesPerPage,t>this.coursesPage&&(this.coursesPage=t)}catch(a){console.error("Erro ao carregar cursos da categoria:",a),this.$emit("error","Erro ao carregar cursos. Por favor, tente novamente."),s||(this.targetCourseOptions=[])}finally{t===1?this.loadingCourses=!1:this.loadingMoreCourses=!1}},async loadMoreCourses(){if(this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses){const e=this.coursesPage+1;await this.loadCoursesForCategory(this.selectedCategory,e,!0)}},async handleCourseSearch(e){this.selectedCategory&&(this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,await this.loadCoursesForCategory(this.selectedCategory,1,!1,e||""))},handleCourseSelect(e){e&&!this.selectedCoursesPreview.some(t=>t.value===e.value)&&(this.selectedCoursesPreview.push({value:e.value,label:e.label,categoryid:e.categoryid,category_name:e.category_name}),this.targetCourseOptions=this.targetCourseOptions.filter(t=>t.value!==e.value)),this.selectedCourse=null},removeCourse(e){const t=this.selectedCoursesPreview.findIndex(s=>s.value===e.value);if(t!==-1){const s=this.selectedCoursesPreview.splice(t,1)[0];this.targetCourseOptions.push(s)}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t},async handleConfirm(){if(!(!this.turma||this.selectedCoursesPreview.length===0))try{this.$emit("loading",!0);const e=this.turma.nome,t=parseInt(this.turma.id,10);if(isNaN(t))throw new Error("ID da turma inválido");this.duplicatingCourses=!0,this.totalToDuplicate=this.selectedCoursesPreview.length,this.duplicatedCount=0;const s=[];for(const i of this.selectedCoursesPreview){const n=parseInt(i.value,10);if(isNaN(n)){console.error(`ID inválido para o curso ${i.label}`);continue}try{this.$emit("loading",!0,`Duplicando para ${i.label} (${this.duplicatedCount+1}/${this.totalToDuplicate})`);const a=await fw(t,n);s.push({turmaNome:e,targetCourseName:i.label,turmaId:t,targetCourseId:n,result:a}),this.duplicatedCount++}catch(a){console.error(`Erro ao duplicar para o curso ${i.label}:`,a),this.$emit("error",`Erro ao duplicar para o curso ${i.label}: ${a.message}`)}}if(this.duplicatingCourses=!1,s.length>0)this.$emit("success",{turmaNome:e,duplicatedCount:s.length,totalSelected:this.totalToDuplicate,results:s}),this.resetForm(),this.$emit("close");else throw new Error("Nenhuma turma foi duplicada com sucesso.")}catch(e){this.$emit("error",e.message||"Erro ao duplicar turmas.")}finally{this.duplicatingCourses=!1,this.$emit("loading",!1)}}}},sO={class:"modal-header"},rO={class:"modal-title"},nO={class:"modal-body"},oO={class:"search-section"},iO={class:"search-group"},aO={class:"search-group"},lO={class:"table-container"},uO={key:0,class:"empty-preview-message"},cO={class:"action-buttons"},dO=["onClick"],fO={class:"modal-footer"},hO=["disabled"];function pO(e,t,s,i,n,a){var m;const u=X("Autocomplete"),c=X("CustomTable"),h=X("Pagination");return s.show?(S(),O("div",{key:0,class:"modal-backdrop",onClick:t[8]||(t[8]=p=>e.$emit("close"))},[d("div",{class:"modal-container",onClick:t[7]||(t[7]=Pt(()=>{},["stop"]))},[d("div",sO,[d("h3",rO,'Duplicar Turma "'+q((m=s.turma)==null?void 0:m.nome)+'"',1),d("button",{class:"close-button",onClick:t[0]||(t[0]=p=>e.$emit("close"))},t[9]||(t[9]=[d("i",{class:"fas fa-times"},null,-1)]))]),d("div",nO,[t[12]||(t[12]=d("h3",{class:"section-title"},"SELECIONAR CURSO",-1)),d("div",oO,[d("div",iO,[M(u,{modelValue:n.selectedCategory,"onUpdate:modelValue":t[1]||(t[1]=p=>n.selectedCategory=p),items:n.categoryOptions,label:"Categoria",placeholder:"Pesquisar...","input-max-width":null,loading:n.loadingCategories,"show-filter-tags":!1,"show-selected-in-input":!0,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":n.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada",onSelect:a.handleCategorySelect},null,8,["modelValue","items","loading","no-results-text","onSelect"])]),d("div",aO,[M(u,{modelValue:n.selectedCourse,"onUpdate:modelValue":t[2]||(t[2]=p=>n.selectedCourse=p),items:n.targetCourseOptions,label:"Curso",placeholder:"Pesquisar...","input-max-width":null,disabled:!n.selectedCategory,loading:n.loadingCourses||n.loadingMoreCourses,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":a.courseNoResultsText,onSelect:a.handleCourseSelect,onLoadMore:a.loadMoreCourses,onSearch:a.handleCourseSearch,ref:"courseAutocomplete"},null,8,["modelValue","items","disabled","loading","no-results-text","onSelect","onLoadMore","onSearch"])])]),d("div",lO,[n.selectedCoursesPreview.length===0?(S(),O("div",uO,t[10]||(t[10]=[d("p",null,"Selecione cursos acima para duplicar a turma",-1)]))):(S(),Ut(c,{key:1,headers:n.tableHeaders,items:a.filteredCourses,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"item-actions":Ne(({item:p})=>[d("div",cO,[d("button",{class:"btn-action btn-delete",onClick:v=>a.removeCourse(p),title:"Remover da lista"},t[11]||(t[11]=[d("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,dO)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]))]),n.selectedCoursesPreview.length>0?(S(),Ut(h,{key:0,"current-page":n.currentPage,"onUpdate:currentPage":t[3]||(t[3]=p=>n.currentPage=p),"per-page":n.perPage,"onUpdate:perPage":t[4]||(t[4]=p=>n.perPage=p),total:n.selectedCoursesPreview.length},null,8,["current-page","per-page","total"])):ee("",!0)]),d("div",fO,[d("button",{class:"btn-primary",onClick:t[5]||(t[5]=(...p)=>a.handleConfirm&&a.handleConfirm(...p)),disabled:n.selectedCoursesPreview.length===0}," Duplicar ",8,hO),d("button",{class:"btn-secondary",onClick:t[6]||(t[6]=p=>e.$emit("close"))},"Cancelar")])])])):ee("",!0)}const mO=je(tO,[["render",pO],["__scopeId","data-v-16d7751d"]]),fR="",gO={name:"HelpIcon",props:{title:{type:String,default:"Ajuda"},text:{type:String,required:!0},postion:{type:String,default:"right"}},computed:{content(){return`<div class="no-overflow"><p class="mb-0">${this.text}</p></div>`}}},vO=["data-content","aria-label"],_O=["title","aria-label"];function bO(e,t,s,i,n,a){return S(),O("a",{class:"btn btn-link p-0","data-container":"body","data-toggle":"popover","data-placement":"auto","data-content":a.content,"data-html":"true",tabindex:"0","data-trigger":"focus","aria-label":s.title,role:"button"},[d("i",{class:"icon fa fa-question-circle text-info fa-fw",title:s.title,"aria-label":s.title,role:"img"},null,8,_O)],8,vO)}const yu=je(gO,[["render",bO]]),hR="",yO={name:"EnrolTypeModal",components:{CustomSelect:mr,HelpIcon:yu},props:{show:{type:Boolean,default:!1},title:{type:String,default:"Tipo de Inscrição"},size:{type:String,default:"md",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Continuar"},cancelButtonText:{type:String,default:"Cancelar"},offercourseid:{type:[Number,String],required:!0},offerid:{type:[Number,String],required:!1,default:"0"}},emits:["close","confirm"],data(){return{selectedEnrolType:"",enrolmentMethods:[],loading:!1}},mounted(){this.$nextTick(()=>{this.initializePopovers()})},watch:{show(e){e&&(this.loadEnrolmentMethods(),this.$nextTick(()=>{this.initializePopovers()}))}},methods:{async loadEnrolmentMethods(){this.loading=!0;const e=await op(!0);e&&Array.isArray(e.data)&&(this.enrolmentMethods=e.data.map(t=>({value:t.enrol,label:t.name}))),this.loading=!1},handleConfirm(){this.selectedEnrolType&&this.$emit("confirm",{enrolType:this.selectedEnrolType,offercourseid:this.offercourseid,offerid:this.offerid})},initializePopovers(){typeof $<"u"&&typeof $.fn.popover<"u"?$('[data-toggle="popover"]').popover({container:"body",trigger:"focus",html:!0}):console.warn("jQuery ou Bootstrap não estão disponíveis para inicializar popovers")}}},wO={class:"modal-header"},EO={class:"modal-title"},CO={class:"modal-body"},DO={class:"enrol-type-modal"},xO={class:"form-group mb-3"},SO={class:"label-with-help"},OO={class:"limited-width-input",style:{"max-width":"280px"}},TO={class:"modal-footer"},NO={class:"footer-buttons"},IO=["disabled"];function AO(e,t,s,i,n,a){const u=X("HelpIcon"),c=X("CustomSelect");return s.show?(S(),O("div",{key:0,class:"modal-backdrop",onClick:t[5]||(t[5]=h=>s.closeOnBackdrop?e.$emit("close"):null)},[d("div",{class:pe(["modal-container",[`modal-${s.size}`]]),onClick:t[4]||(t[4]=Pt(()=>{},["stop"]))},[d("div",wO,[d("h3",EO,q(s.title),1),d("button",{class:"modal-close",onClick:t[0]||(t[0]=h=>e.$emit("close"))},t[6]||(t[6]=[d("i",{class:"fas fa-times"},null,-1)]))]),d("div",CO,[d("div",DO,[t[9]||(t[9]=d("p",{class:"modal-description"}," Selecione o tipo de inscrição para esta turma. Esta configuração não poderá ser alterada posteriormente. ",-1)),d("div",xO,[d("div",SO,[t[7]||(t[7]=d("label",{class:"form-label"},"Método de inscrição",-1)),t[8]||(t[8]=d("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw m-0",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),M(u,{title:"Ajuda com método de inscrição",text:`Inscrição automática por público-alvo em ofertas: Nesta opção o usuário será inscrito automaticamente nos cursos que forem atribuídos para o público-alvo dele.\r
Inscrição manual em ofertas: Nesta opção o usuário será matriculado manualmente (em lote ou individualmente) por um perfil autorizado, através da página 'Usuários matriculados' contida em cada turma.\r
Autoisncrição em ofertas: Nesta opção o usuário visualizará os cursos que forem atribuídos para o público-alvo dele, porém, ele precisa clicar no curso para fazer sua matrícula em uma turma. Ou seja, ele não será inscrito automaticamente como no Tipo de inscrição 'Inscrição por público-alvo em ofertas'.`})]),d("div",OO,[M(c,{modelValue:n.selectedEnrolType,"onUpdate:modelValue":t[1]||(t[1]=h=>n.selectedEnrolType=h),options:[{value:"",label:"Selecione um método..."},...n.enrolmentMethods],width:280,required:""},null,8,["modelValue","options"])])])])]),d("div",TO,[t[10]||(t[10]=d("div",{class:"form-info"},[d("span",{style:{color:"#f8f9fa","font-size":"15px"}},"Este formulário contém campos obrigatórios marcados com"),d("i",{class:"fas fa-exclamation-circle",style:{color:"#dc3545","font-size":"0.85rem","vertical-align":"middle"}})],-1)),d("div",NO,[d("button",{class:"btn btn-primary",onClick:t[2]||(t[2]=(...h)=>a.handleConfirm&&a.handleConfirm(...h)),disabled:!n.selectedEnrolType},q(s.confirmButtonText),9,IO),d("button",{class:"btn btn-secondary",onClick:t[3]||(t[3]=h=>e.$emit("close"))},q(s.cancelButtonText),1)])])],2)])):ee("",!0)}const MO=je(yO,[["render",AO],["__scopeId","data-v-4c36a864"]]),PO="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICAgIDxwYXRoIGQ9Ik03IDE0cy0xIDAtMS0xIDEtNCA1LTQgNSAzIDUgNC0xIDEtMSAxSDd6bTQtNmEzIDMgMCAxIDAgMC02IDMgMyAwIDAgMCAwIDZ6IiBmaWxsPSIjZmZmIi8+CiAgICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTUuMjE2IDE0QTIuMjM4IDIuMjM4IDAgMCAxIDUgMTNjMC0xLjM1NS42OC0yLjc1IDEuOTM2LTMuNzJBNi4zMjUgNi4zMjUgMCAwIDAgNSA5Yy00IDAtNSAzLTUgNHMxIDEgMSAxaDQuMjE2eiIgZmlsbD0iI2ZmZiIvPgogICAgPHBhdGggZD0iTTQuNSA4YTIuNSAyLjUgMCAxIDAgMC01IDIuNSAyLjUgMCAwIDAgMCA1eiIgZmlsbD0iI2ZmZiIvPgo8L3N2Zz4K",kO="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICAgIDxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNOCAyYS41LjUgMCAwIDEgLjUuNXY1aDVhLjUuNSAwIDAgMSAwIDFoLTV2NWEuNS41IDAgMCAxLTEgMHYtNWgtNWEuNS41IDAgMSAxIDAtMWg1di01QS41LjUgMCAwIDEgOCAyeiIgZmlsbD0iI2ZmZiIvPgo8L3N2Zz4K",pR="",VO={name:"NewOfferView",components:{CustomTable:hn,CustomSelect:mr,CustomInput:Fo,CustomButton:Ln,Pagination:pn,CollapsibleTable:$S,PageHeader:sa,BackButton:bu,Autocomplete:$o,TextEditor:_u,CustomCheckbox:Xi,FilterRow:ea,FilterGroup:ta,FilterTag:Bo,FilterTags:ra,AddCourseModal:eO,ConfirmationModal:gu,Toast:Lo,HelpIcon:yu,DuplicateClassModal:mO,EnrolTypeModal:MO,LFLoading:mu},setup(){const e=Zi(),t=Jh();return{router:e,route:t}},directives:{tooltip:{mounted(e,t){e.setAttribute("title",t.value)},updated(e,t){e.setAttribute("title",t.value)}}},data(){return{icons:{users:PO,plus:kO},showAddCourseModalVisible:!1,showCourseStatusModal:!1,selectedCourse:null,showWarning:!0,isEditing:!1,offerId:null,showDeleteCourseModal:!1,courseToDelete:null,showDeleteClassModal:!1,classToDelete:null,classParentCourse:null,showClassStatusModal:!1,showDuplicateClassModal:!1,showEnrolTypeModal:!1,selectedClass:null,classToDuplicate:null,classToDuplicateParentCourse:null,selectedCourseForClass:null,loading:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,offer:{id:null,name:"",offerType:"",description:"",status:0},formErrors:{name:{hasError:!1,message:"Nome da oferta é obrigatório"},audiences:{hasError:!1,message:"Selecione pelo menos um público-alvo"}},offerTypeOptions:[],typeOptionsEnabled:!1,audienceTypeOptions:[],allAudiences:[],selectedAudiences:[],categoryOptions:[],courseOptions:[],selectedCategory:null,selectedCourse:null,inputFilters:{course:"",category:"",onlyActive:!1},appliedFilters:{course:"",category:"",onlyActive:!1},courseTableHeaders:[{text:"NOME DO CURSO",value:"name",sortable:!0},{text:"CATEGORIA",value:"category",sortable:!0},{text:"NÚMERO DE TURMAS",value:"turmasCount",sortable:!0},{text:"STATUS DO CURSO",value:"status",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1}],selectedCourses:[],currentPage:1,perPage:5,totalItems:0,sortBy:"id",sortDesc:!1,coursesPage:1,coursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,loadingCourses:!1,loadingMoreCourses:!1,courseNoResultsText:"Nenhum curso encontrado"}},async mounted(){await this.loadInitialData(),this.offerId?(this.isEditing=!0,await this.loadOfferData(),await this.loadCategoryOptions(),await this.loadCourseOptions()):this.isEditing=!1,await this.loadTypeOptions(),await this.loadAllAudiences(),this.showWarning=!0},computed:{canManageCourses(){return this.isEditing&&this.offerId!==null},hasActiveFilters(){return this.appliedFilters.course}},methods:{getEnrolTypeLabel(e){if(!e)return"-";if(typeof e!="string")return String(e);const t=e.toLowerCase(),s={offer_manual:"Manual",offer_self:"Autoinscrição",manual:"Manual",self:"Autoinscrição",guest:"Visitante",cohort:"Coorte",database:"Base de dados",flatfile:"Arquivo plano",ldap:"LDAP",lti:"LTI",meta:"Meta-curso",mnet:"MNet",paypal:"PayPal",shibboleth:"Shibboleth"};if(s[e])return s[e];for(const[i,n]of Object.entries(s))if(i.toLowerCase()===t)return n;if(t==="offer_manual")return"Manual";if(t==="offer_self")return"Autoinscrição";for(const[i,n]of Object.entries(s))if(t.includes(i.toLowerCase()))return n;return e},formatDate(e){return e?new Date(e*1e3).toLocaleDateString("pt-BR"):"-"},async loadInitialData(){},async loadOfferData(){if(this.offerId){this.loading=!0;try{const e=await ep(this.offerId);e&&e.data&&(this.offer={id:e.data.id,name:e.data.name,offerType:e.data.typeid,description:e.data.description||"",status:e.data.status||0},this.selectedAudiences=e.data.audiences?e.data.audiences.map(t=>({value:t.id,label:t.name})):[],await this.loadCourses())}catch{this.showErrorMessage("Erro ao carregar dados da oferta.")}finally{this.loading=!1}}},async loadTypeOptions(){try{const e=await du();if(e&&e.data){const{enabled:t,types:s}=e.data;this.typeOptionsEnabled=!!t,t&&Array.isArray(s)?this.offerTypeOptions=s.map(i=>({value:i,label:i.charAt(0).toUpperCase()+i.slice(1)})):(this.offerTypeOptions=[],this.typeOptionsEnabled=!1)}else this.offerTypeOptions=[],this.typeOptionsEnabled=!1}catch{this.typeOptionsEnabled=!1}},async loadAllAudiences(){if(this.allAudiences.length>0){this.audienceTypeOptions=[...this.allAudiences];return}this.loading=!0;try{const e=await sp("");e&&e.items&&(this.allAudiences=e.items.map(t=>({value:t.id,label:t.name})),this.audienceTypeOptions=[...this.allAudiences])}catch{this.showErrorMessage("Erro ao carregar públicos-alvo.")}finally{this.loading=!1}},async applyFilters(){this.appliedFilters=JSON.parse(JSON.stringify(this.inputFilters));try{this.loading=!0,await this.loadCourses()}catch{this.showErrorMessage("Erro ao aplicar filtros. Por favor, tente novamente.")}finally{this.loading=!1}this.currentPage=1},clearFilters(){this.inputFilters={course:"",category:"",onlyActive:!1},this.appliedFilters={course:"",category:"",onlyActive:!1},this.selectedCategory=null,this.selectedCourse=null,this.loadCourses(),this.loadCourseOptions()},async removeFilter(e){try{this.loading=!0,this.inputFilters[e]="",this.appliedFilters[e]="",e==="category"?(this.selectedCategory=null,await this.loadCourseOptions()):e==="course"&&(this.selectedCourse=null),this.hasActiveFilters?await this.applyFilters():await this.loadCourses()}catch{this.showErrorMessage("Erro ao remover filtro. Por favor, tente novamente.")}finally{this.loading=!1}},async loadCategoryOptions(){if(this.offerId)try{this.loading=!0;const e=await Uo("",this.offerId);e&&e.data&&Array.isArray(e.data)?this.categoryOptions=e.data.map(t=>({value:t.id,label:t.name})):this.categoryOptions=[]}catch{this.showErrorMessage("Erro ao carregar categorias.")}finally{this.loading=!1}},async loadCourseOptions(e=null,t=!0){if(this.offerId)try{if(this.loading=!0,e){await this.updateCourseOptionsByCategory(e);return}t?(this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadingCourses=!0,this.courseOptions=[]):this.loadingMoreCourses=!0;const s=await fu(this.offerId,{onlyActive:!1,page:this.coursesPage,perPage:this.coursesPerPage,sortBy:this.sortBy,sortDesc:this.sortDesc});if(s&&s.data&&s.data.courses){const{page:i,total_pages:n,courses:a}=s.data;this.coursesPage=i||this.coursesPage,this.coursesTotalPages=n||1,this.hasMoreCourses=this.coursesPage<this.coursesTotalPages;const u=a.map(c=>({value:c.courseid||c.id,label:c.fullname}));t?this.courseOptions=u:this.courseOptions=[...this.courseOptions,...u],this.courseNoResultsText=this.courseOptions.length===0?"Nenhum curso disponível":"Nenhum curso encontrado"}else if(s&&s.data&&Array.isArray(s.data)){const i=s.data.map(n=>({value:n.id||n.courseid,label:n.fullname}));t?this.courseOptions=i:this.courseOptions=[...this.courseOptions,...i],this.hasMoreCourses=!1,this.courseNoResultsText=this.courseOptions.length===0?"Nenhum curso disponível":"Nenhum curso encontrado"}else t&&(this.courseOptions=[]),this.hasMoreCourses=!1,this.courseNoResultsText="Nenhum curso disponível"}catch{this.showErrorMessage("Erro ao carregar cursos."),t&&(this.courseOptions=[]),this.hasMoreCourses=!1}finally{t?this.loadingCourses=!1:this.loadingMoreCourses=!1,this.loading=!1}},async loadMoreCourses(){this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses&&(this.coursesPage+=1,this.selectedCategory?await this.updateCourseOptionsByCategory({value:this.selectedCategory,label:this.inputFilters.category},!1):await this.loadCourseOptions(null,!1))},async handleCourseSearch(e){this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadingCourses=!0,this.courseOptions=[];try{this.selectedCategory&&(this.selectedCategory=null);const t=await rw(this.offerId,e);t&&t.data&&Array.isArray(t.data)&&(this.courseOptions=t.data.map(s=>({value:s.courseid||s.id,label:s.fullname}))),this.courseNoResultsText=this.courseOptions.length===0?"Nenhum curso encontrado para a busca":"Nenhum curso encontrado"}catch{this.showErrorMessage("Erro ao buscar cursos."),this.courseOptions=[]}finally{this.loadingCourses=!1}},async updateCourseOptionsByCategory(e,t=!0){if(!(!this.offerId||!e)){this.loading=!0;try{t?(this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadingCourses=!0,this.courseOptions=[]):this.loadingMoreCourses=!0;let s=e.value;if(isNaN(parseInt(s))){const n=await Uo(e.label,this.offerId);if(n&&n.data&&n.data.length>0){const a=n.data.find(u=>u.name.toLowerCase()===e.label.toLowerCase());if(a)s=a.id;else{this.showErrorMessage("Erro ao identificar a categoria selecionada."),t?this.loadingCourses=!1:this.loadingMoreCourses=!1;return}}else{this.showErrorMessage("Erro ao buscar categorias."),t?this.loadingCourses=!1:this.loadingMoreCourses=!1;return}}const i=await np(this.offerId,s);if(i&&i.data&&Array.isArray(i.data)){const n=i.data.map(a=>({value:a.id||a.courseid,label:a.fullname}));t?(this.courseOptions=n,this.selectedCourse=null,this.inputFilters.course="",this.appliedFilters.course=""):this.courseOptions=[...this.courseOptions,...n],this.hasMoreCourses=!1,this.courseNoResultsText=this.courseOptions.length===0?"Nenhum curso disponível nesta categoria":"Nenhum curso encontrado"}else t&&(this.courseOptions=[],this.selectedCourse=null,this.inputFilters.course="",this.appliedFilters.course=""),this.hasMoreCourses=!1,this.courseNoResultsText="Nenhum curso disponível nesta categoria"}catch{this.showErrorMessage("Erro ao carregar cursos da categoria."),t&&(this.courseOptions=[]),this.hasMoreCourses=!1}finally{t?this.loadingCourses=!1:this.loadingMoreCourses=!1,this.loading=!1}}},async handleCategorySelect(e){e&&(this.inputFilters.category=e.label,this.currentPage=1,await this.applyFilters(),await this.updateCourseOptionsByCategory(e,!0))},async handleCourseSelect(e){e&&(this.inputFilters.course=e,this.currentPage=1,this.selectedCategory=null,await this.applyFilters())},handleOnlyActiveChange(){this.appliedFilters.onlyActive=this.inputFilters.onlyActive,this.currentPage=1,this.loadCourses()},goBack(){this.router.push({name:"listar-ofertas"})},async searchCategories(){if(this.offerId)try{this.loading=!0;const e=await Uo(this.appliedFilters.category,this.offerId);if(e&&e.data&&e.data.length>0){let t=[];for(const s of e.data){const i=s.id;if(!i)continue;const n=await np(this.offerId,i);if(n&&n.data){const a=n.data.map(u=>({id:u.id||u.courseid,offerCourseId:u.id,name:u.fullname,category:s.name||"-",turmasCount:Array.isArray(u.turmas)?u.turmas.length:0,status:u.status===1||u.status==="1"?"Ativo":"Inativo",can_delete:u.can_delete!==void 0?u.can_delete:!0,can_activate:u.can_activate!==void 0?u.can_activate:!0,turmas:Array.isArray(u.turmas)?u.turmas.map(c=>({id:c.id,nome:c.name,enrol_type:c.enrol_type||"-",vagas:c.max_users?c.max_users:"Ilimitado",inscritos:c.enrolled_users||0,dataInicio:c.start_date||"-",dataFim:c.end_date||"-"})):[]}));t=[...t,...a]}}this.selectedCourses=t}else this.selectedCourses=[]}catch{this.showErrorMessage("Erro ao buscar categorias. Por favor, tente novamente.")}finally{this.loading=!1}},showAddCourseModal(){this.showAddCourseModalVisible=!0},async handleAddCourseConfirm(e){try{this.loading=!0;for(const t of e)await J0(this.offerId,t);await this.loadCourses(),await this.loadCategoryOptions(),await this.loadCourseOptions(),this.showSuccessMessage("Curso(s) adicionado(s) com sucesso à oferta.")}catch(t){this.showErrorMessage(t.message||"Ocorreu um erro ao adicionar os cursos.")}finally{this.loading=!1}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,this.loadCourses()},handlePageChange(e){this.currentPage=e,this.loadCourses()},addTurma(e){this.selectedCourseForClass=e,this.showEnrolTypeModal=!0},handleEnrolTypeConfirm(e){this.showEnrolTypeModal=!1,this.router.push({name:"NewClass",params:{offercourseid:e.offercourseid,offerid:e.offerid||this.offerId||"0"},query:{enrol_type:e.enrolType}})},editTurma(e){const t=this.selectedCourses.find(s=>s.turmas&&s.turmas.some(i=>i.id===e.id));t?this.router.push({name:"EditClass",params:{offercourseid:t.offerCourseId,classid:e.id}}):this.showErrorMessage("Não foi possível editar a turma. Curso pai não encontrado.")},toggleClassStatus(e){this.selectedClass={...e,status:e.status||"Ativo"},this.showClassStatusModal=!0},async confirmToggleClassStatus(){if(this.selectedClass)try{this.loading=!0;const e=this.selectedClass.nome,t=this.selectedClass.status!=="Ativo";await pw(this.selectedClass.id,t);const s=this.selectedCourses.findIndex(i=>i.turmas.some(n=>n.id===this.selectedClass.id));if(s!==-1){const i=this.selectedCourses[s],n=i.turmas.findIndex(a=>a.id===this.selectedClass.id);n!==-1&&(i.turmas[n].status=t?"Ativo":"Inativo")}await this.loadCourses(),this.showSuccessMessage(t?`Turma "${e}" ativada com sucesso.`:`Turma "${e}" inativada com sucesso.`),this.selectedClass=null,this.showClassStatusModal=!1}catch(e){this.showErrorMessage(e.message||"Erro ao alterar status da turma.")}finally{this.loading=!1}},removeTurma(e,t){const s=e.turmas[t];s.can_delete&&(this.classToDelete=s,this.classParentCourse=e,this.showDeleteClassModal=!0)},viewRegisteredUsers(e){this.router.push({name:"usuarios-matriculados",params:{offerclassid:parseInt(e.id)}})},duplicateTurma(e,t){this.classToDuplicate=e,this.classToDuplicateParentCourse=t,this.showDuplicateClassModal=!0},async handleDuplicateSuccess(e){await this.loadCourses(),e.duplicatedCount?this.showSuccessMessage(`Turma "${e.turmaNome}" duplicada com sucesso para ${e.duplicatedCount} curso(s).`):this.showSuccessMessage(`Turma "${e.turmaNome}" duplicada com sucesso para o curso "${e.targetCourseName}".`)},async confirmDeleteClass(){if(!(!this.classToDelete||!this.classParentCourse))try{this.loading=!0;const e=this.classToDelete.nome;await uw(this.classToDelete.id);const t=this.classParentCourse.turmas.findIndex(s=>s.id===this.classToDelete.id);t!==-1&&(this.classParentCourse.turmas.splice(t,1),this.classParentCourse.turmasCount=this.classParentCourse.turmas.length),this.showSuccessMessage(`Turma ${e} excluída com sucesso.`),this.classToDelete=null,this.classParentCourse=null,this.showDeleteClassModal=!1}catch(e){this.showErrorMessage(e.message||"Erro ao excluir turma.")}finally{this.loading=!1}},updateTurmasCount(e){const t=this.selectedCourses.findIndex(s=>s.id===e);if(t!==-1){const s=this.selectedCourses[t];s.turmasCount=Array.isArray(s.turmas)?s.turmas.length:0,this.selectedCourses[t]={...s}}},async loadCourses(){if(this.offerId)try{this.loading=!0;const e={onlyActive:this.appliedFilters.onlyActive,page:this.currentPage,perPage:this.perPage,sortBy:this.sortBy,sortDesc:this.sortDesc};this.appliedFilters.course&&(e.courseIds=[this.appliedFilters.course.value]),this.appliedFilters.courseSearch&&(e.courseSearch=this.appliedFilters.course.label),this.appliedFilters.category&&(e.categorySearch=this.appliedFilters.category);const t=await fu(this.offerId,e);if(t&&t.data){let s=[],i=this.currentPage,n=1,a=0;t.data.courses&&({page:i,total_pages:n,total_items:a,courses:s}=t.data),this.currentPage=i||this.currentPage;const u=[];for(const c of s)try{const h=await aw(c.id);let m=[];h&&typeof h=="object"&&h.error===!1&&Array.isArray(h.data)&&h.data.length>0&&(m=h.data.map(p=>{let v=p.enrol_type||p.enrol||"-";return{id:p.id,nome:p.name,enrol_type:v,vagas:p.max_users?p.max_users:"Ilimitado",inscritos:p.enrolled_users||0,dataInicio:this.formatDate(p.startdate),dataFim:this.formatDate(p.enddate),status:p.status===0||p.status==="0"?"Inativo":"Ativo",can_activate:p.can_activate!==void 0?p.can_activate:!0,can_delete:p.can_delete!==void 0?p.can_delete:!0}})),u.push({id:c.courseid||c.id,offerCourseId:c.id,name:c.fullname,category:c.category_name||c.category||"-",turmasCount:m.length,status:c.status===1||c.status==="1"?"Ativo":"Inativo",can_delete:c.can_delete!==void 0?c.can_delete:!0,can_activate:c.can_activate!==void 0?c.can_activate:!0,turmas:m})}catch{u.push({id:c.courseid||c.id,offerCourseId:c.id,name:c.fullname,category:c.category_name||c.category||"-",turmasCount:0,status:c.status===1||c.status==="1"?"Ativo":"Inativo",can_delete:c.can_delete!==void 0?c.can_delete:!0,can_activate:c.can_activate!==void 0?c.can_activate:!0,turmas:[]})}this.selectedCourses=u,a!=null?this.totalItems=a:n>0?this.totalItems=n*this.perPage:this.totalItems=u.length,await this.loadCategoryOptions(),await this.loadCourseOptions()}else this.selectedCourses=[],this.totalItems=0,this.categoryOptions=[],this.courseOptions=[]}catch{this.showErrorMessage("Erro ao carregar cursos da oferta. Por favor, tente novamente."),this.selectedCourses=[],this.totalItems=0}finally{this.loading=!1}},async loadOffer(e){try{this.loading=!0,this.inputFilters={course:"",category:""},this.appliedFilters={course:"",category:""},this.selectedCategory=null,this.selectedCourse=null;const[t,s]=await Promise.all([ep(e),sp("")]),i=Array.isArray(t)?t[0]:t;if(!i.error&&i.data){const n=i.data;if(this.offer={name:n.name,offerType:n.type,description:n.description,id:n.id,status:n.status},s&&Array.isArray(s.items)){const a=n.audiences||[];this.selectedAudiences=s.items.filter(u=>a.includes(u.id)).map(u=>({value:u.id,label:u.name.toUpperCase()}))}this.isEditing=!0,await this.loadCourses()}else throw new Error(i.message||"Erro ao carregar dados da oferta")}catch(t){this.showErrorMessage(t.message||"Erro ao carregar oferta.")}finally{this.loading=!1}},handleSelectAllAudiences(){const e=new Set(this.allAudiences.map(i=>i.value)),t=new Set(this.selectedAudiences.map(i=>i.value));let s=!0;for(const i of e)if(!t.has(i)){s=!1;break}s&&this.selectedAudiences.length===this.allAudiences.length?this.selectedAudiences=[]:this.selectedAudiences=[...this.allAudiences]},validate(){Object.keys(this.formErrors).forEach(t=>{this.formErrors[t].hasError=!1});let e=!1;return this.offer.name||(this.formErrors.name.hasError=!0,e=!0),this.selectedAudiences.length===0&&(this.formErrors.audiences.hasError=!0,e=!0),e&&this.showErrorMessage("Há campos obrigatórios a serem preenchidos."),!e},validateField(e){switch(e){case"name":this.formErrors.name.hasError=!this.offer.name;break;case"audiences":this.formErrors.audiences.hasError=this.selectedAudiences.length===0;break}return!this.formErrors[e].hasError},async saveOffer(){var e;if(this.loading=!0,!this.validate()){this.loading=!1;return}try{const t={name:this.offer.name,description:this.offer.description,type:this.offer.offerType,status:this.offer.status,audiences:this.selectedAudiences.map(i=>i.value)};let s;if(this.isEditing&&this.offerId)if(t.id=this.offerId,s=await tp(t),s&&!s.error)if(await this.updateOfferAudiences())this.showSuccessMessage("Oferta atualizada com sucesso!");else throw new Error("Oferta atualizada, mas houve falha ao atualizar públicos-alvo.");else{const i=(s==null?void 0:s.message)||(s==null?void 0:s.error)||"Falha ao atualizar oferta.";throw new Error(i)}else if(s=await tp(t),s&&s.data&&s.data.id){const i=s.data.id;this.offerId=i,this.offer.id=i,this.isEditing=!0,this.showSuccessMessage("Oferta salva com sucesso!");const n=`/edit-offer/${this.offerId}`;this.router.replace(n)}else{const i=(s==null?void 0:s.message)||((e=s==null?void 0:s[0])==null?void 0:e.message)||"Falha ao salvar a oferta.";throw new Error(i)}}catch(t){this.showErrorMessage(t.message||"Erro ao salvar oferta. Verifique os dados e tente novamente.")}finally{this.loading=!1}},async updateOfferAudiences(){if(!this.offerId)return!1;try{const e=this.selectedAudiences.map(s=>s.value),t=await tw(this.offerId,e);return!!(t&&!t.error)}catch{return this.showErrorMessage("Erro ao atualizar públicos-alvo."),!1}finally{}},toggleCourseStatus(e){e.can_activate&&(this.selectedCourse=e,this.showCourseStatusModal=!0)},getStatusButtonTitle(e){return e.status==="Ativo"?e.can_activate?"Inativar":"Não é possível inativar este curso":e.can_activate?"Ativar":"Não é possível ativar este curso"},async confirmToggleCourseStatus(){if(this.selectedCourse)try{this.loading=!0;const e=this.selectedCourse.status!=="Ativo",t=this.selectedCourse.name,s=this.selectedCourse.offerCourseId||this.selectedCourse.id;await ew(this.offerId,s,e);const i=this.selectedCourses.findIndex(n=>n.id===this.selectedCourse.id);i!==-1&&(this.selectedCourses[i].status=e?"Ativo":"Inativo"),this.showCourseStatusModal=!1,this.selectedCourse=null,await this.loadCourses(),await this.loadCategoryOptions(),await this.loadCourseOptions(),this.showSuccessMessage(e?`Curso "${t}" ativado com sucesso.`:`Curso "${t}" inativado com sucesso.`)}catch(e){this.showErrorMessage(e.message||"Erro ao alterar status do curso.")}finally{this.loading=!1}},deleteCourse(e){e.can_delete&&(this.courseToDelete=e,this.showDeleteCourseModal=!0)},async confirmDeleteCourse(){if(this.courseToDelete)try{this.loading=!0;const e=this.courseToDelete.name,t=this.courseToDelete.offerCourseId||this.courseToDelete.id;await X0(this.offerId,t),this.selectedCourses=this.selectedCourses.filter(s=>s.id!==this.courseToDelete.id),await this.loadCourses(),await this.loadCategoryOptions(),await this.loadCourseOptions(),this.showSuccessMessage(`Curso "${e}" excluído com sucesso.`),this.courseToDelete=null,this.showDeleteCourseModal=!1}catch(e){this.showErrorMessage(e.message||"Erro ao remover curso.")}finally{this.loading=!1}},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}},async created(){try{const t=await du();if(t&&t.data){const{enabled:s,types:i,default:n}=t.data;this.typeOptionsEnabled=s,s&&Array.isArray(i)&&(this.offerTypeOptions=i.map(a=>({value:a,label:a})),n&&!this.isEditing&&(this.offer.offerType=n))}}catch(t){this.showErrorMessage(t.message||"Erro ao carregar opções de tipos.")}const e=this.route.params.id;e&&(this.offerId=parseInt(e),this.loadOffer(this.offerId))},watch:{"inputFilters.course"(e,t){e.length===0&&t.length>0&&this.loadCourses()},"inputFilters.category"(e,t){e.length===0&&t.length>0&&this.loadCourses()},selectedCategory(e){e||(this.inputFilters.category="",this.appliedFilters.category&&(this.appliedFilters.category="",this.loadCourses()),this.loadCourseOptions())},selectedCourse(e){e||(this.inputFilters.course="",this.appliedFilters.course.id&&(this.appliedFilters.course.id="",this.loadCourses()))},currentPage(){this.loadCourses()},perPage(){this.currentPage=1,this.loadCourses()}}},RO={id:"new-offer-component",class:"new-offer"},UO={key:0,class:"alert alert-warning"},FO={class:"section-container"},LO={class:"form-row mb-3"},BO={class:"form-group"},$O={class:"input-container"},jO={key:0,class:"form-group"},HO={class:"input-container"},qO={class:"form-row mb-3",style:{"margin-bottom":"1.5rem"}},zO={class:"form-group"},WO={class:"label-container"},GO={class:"label-with-help"},KO={class:"input-container"},QO={class:"form-group text-editor-container"},YO={class:"limited-width-editor"},ZO={key:0,class:"section-title"},JO={key:1,class:"message-container"},XO={key:2},eT={class:"filters-left-group"},tT={class:"filters-right-group"},sT={class:"empty-state"},rT={class:"no-results"},nT=["title"],oT={class:"action-buttons"},iT=["onClick"],aT=["src"],lT=["onClick","disabled","title"],uT=["onClick","disabled","title"],cT={class:"turmas-container"},dT={class:"turmas-content"},fT={key:0},hT={class:"turma-col"},pT=["title"],mT={class:"turma-col"},gT={class:"turma-col"},vT={class:"turma-col"},_T={class:"turma-col"},bT={class:"turma-col"},yT={class:"turma-col"},wT={class:"action-buttons"},ET=["onClick"],CT=["src"],DT=["onClick"],xT=["onClick"],ST=["title","onClick"],OT=["onClick","disabled","title"],TT={key:1,class:"empty-turmas"},NT={class:"d-flex justify-content-end align-items-center"},IT={class:"actions-container offer-actions"};function AT(e,t,s,i,n,a){var V,te,Ke,vt,mt,ft,Se,we,Ft,es,bt;const u=X("BackButton"),c=X("PageHeader"),h=X("CustomInput"),m=X("CustomSelect"),p=X("HelpIcon"),v=X("Autocomplete"),w=X("TextEditor"),D=X("FilterGroup"),k=X("CustomCheckbox"),L=X("FilterTag"),re=X("FilterTags"),I=X("FilterRow"),ne=X("CollapsibleTable"),Q=X("Pagination"),ye=X("CustomButton"),Z=X("AddCourseModal"),he=X("ConfirmationModal"),be=X("DuplicateClassModal"),Ae=X("EnrolTypeModal"),ue=X("LFLoading"),ae=X("Toast");return S(),O("div",RO,[M(c,{title:n.isEditing?`Editar oferta: ${n.offer.name}`:"Nova oferta"},{actions:Ne(()=>[M(u,{onClick:a.goBack},null,8,["onClick"])]),_:1},8,["title"]),n.showWarning?(S(),O("div",UO,t[21]||(t[21]=[d("i",{class:"fas fa-exclamation-triangle"},null,-1),Ue(" Para que uma instância de oferta seja ativada e disponibilize os cursos para os públicos-alvo configurados, é necessário garantir que pelo menos um curso, um grupo de público-alvo, e uma turma estejam configurados à instância da oferta. ")]))):ee("",!0),d("div",FO,[t[27]||(t[27]=d("h2",{class:"section-title"},"CONFIGURAÇÕES GERAIS",-1)),d("div",LO,[d("div",BO,[t[22]||(t[22]=d("div",{class:"label-container"},[d("div",{class:"label-with-help"},[d("label",{class:"form-label"},"Nome da Oferta"),d("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})])],-1)),d("div",$O,[M(h,{modelValue:n.offer.name,"onUpdate:modelValue":t[0]||(t[0]=de=>n.offer.name=de),placeholder:"Oferta 0001",width:400,required:"","has-error":n.formErrors.name.hasError,"error-message":n.formErrors.name.message,onValidate:t[1]||(t[1]=de=>a.validateField("name"))},null,8,["modelValue","has-error","error-message"])])]),n.typeOptionsEnabled?(S(),O("div",jO,[t[23]||(t[23]=d("div",{class:"label-container"},[d("div",{class:"label-with-help"},[d("label",{class:"form-label"},"Tipo da oferta")])],-1)),d("div",HO,[M(m,{modelValue:n.offer.offerType,"onUpdate:modelValue":t[2]||(t[2]=de=>n.offer.offerType=de),options:n.offerTypeOptions,width:400},null,8,["modelValue","options"])])])):ee("",!0)]),d("div",qO,[d("div",zO,[d("div",WO,[d("div",GO,[t[24]||(t[24]=d("label",{class:"form-label"},"Público-alvo",-1)),t[25]||(t[25]=d("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),M(p,{title:"Ajuda com público-alvo",text:"Selecione pelo menos um público-alvo para a oferta."})])]),d("div",KO,[M(v,{class:"autocomplete-audiences",modelValue:n.selectedAudiences,"onUpdate:modelValue":[t[3]||(t[3]=de=>n.selectedAudiences=de),t[4]||(t[4]=de=>a.validateField("audiences"))],items:n.audienceTypeOptions,placeholder:"Pesquisar público-alvo...","input-max-width":400,required:!0,"show-all-option":!0,"has-error":n.formErrors.audiences.hasError,"error-message":n.formErrors.audiences.message,onSelectAll:a.handleSelectAllAudiences},null,8,["modelValue","items","has-error","error-message","onSelectAll"])])])]),d("div",QO,[t[26]||(t[26]=d("div",{class:"label-container"},[d("div",{class:"label-with-help"},[d("label",{class:"form-label"},"Descrição da oferta")])],-1)),d("div",YO,[M(w,{modelValue:n.offer.description,"onUpdate:modelValue":t[5]||(t[5]=de=>n.offer.description=de),placeholder:"Digite a descrição da oferta aqui...",rows:5},null,8,["modelValue"])])])]),d("div",{class:pe(["section-container",{"no-title-section":!n.isEditing}])},[n.isEditing?(S(),O("h2",ZO,"CURSOS")):ee("",!0),!a.canManageCourses||!n.isEditing?(S(),O("div",JO,t[28]||(t[28]=[d("div",{class:"lock-message"},[d("i",{class:"fas fa-lock lock-icon"}),d("span",null,"Salve a oferta primeiro para gerenciar os cursos")],-1)]))):ee("",!0),n.isEditing&&a.canManageCourses?(S(),O("div",XO,[M(I,{inline:"",class:"courses-filter-row"},{default:Ne(()=>[d("div",eT,[M(D,null,{default:Ne(()=>[M(v,{modelValue:n.selectedCategory,"onUpdate:modelValue":t[6]||(t[6]=de=>n.selectedCategory=de),items:n.categoryOptions,placeholder:"Pesquisar...",label:"Categoria","input-max-width":null,"has-search-icon":!0,"auto-open":!1,"show-filter-tags":!1,"show-selected-in-input":!0,"no-results-text":n.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada",onSelect:a.handleCategorySelect,class:"category-filter"},null,8,["modelValue","items","no-results-text","onSelect"])]),_:1}),M(D,null,{default:Ne(()=>[M(v,{modelValue:n.selectedCourse,"onUpdate:modelValue":t[7]||(t[7]=de=>n.selectedCourse=de),items:n.courseOptions,placeholder:"Pesquisar...",label:"Curso","input-max-width":null,"has-search-icon":!0,"auto-open":!0,loading:n.loadingCourses||n.loadingMoreCourses,"no-results-text":n.courseNoResultsText,onSelect:a.handleCourseSelect,onLoadMore:a.loadMoreCourses,onSearch:a.handleCourseSearch,ref:"courseAutocomplete",class:"course-filter"},null,8,["modelValue","items","loading","no-results-text","onSelect","onLoadMore","onSearch"])]),_:1}),M(D,{isCheckbox:!0,class:"checkbox-filter-group"},{default:Ne(()=>[M(k,{modelValue:n.inputFilters.onlyActive,"onUpdate:modelValue":t[8]||(t[8]=de=>n.inputFilters.onlyActive=de),id:"onlyActive",label:"Não exibir inativos",onChange:a.handleOnlyActiveChange},null,8,["modelValue","onChange"])]),_:1}),n.appliedFilters.course?(S(),Ut(re,{key:0,class:"mt-3"},{default:Ne(()=>[M(L,{onRemove:t[9]||(t[9]=de=>a.removeFilter("course"))},{default:Ne(()=>[Ue(" Curso: "+q(n.appliedFilters.course.label),1)]),_:1})]),_:1})):ee("",!0)]),d("div",tT,[d("button",{class:"btn btn-primary add-course-button",onClick:t[10]||(t[10]=(...de)=>a.showAddCourseModal&&a.showAddCourseModal(...de))}," Adicionar curso ")])]),_:1}),M(ne,{headers:n.courseTableHeaders,items:n.selectedCourses,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort,expandable:!0},{"empty-state":Ne(()=>[d("div",sT,[d("span",rT,q(n.loading?"Carregando registros...":"Não existem registros"),1)])]),"item-name":Ne(({item:de})=>[d("span",{title:de.name},q(de.name.length>50?de.name.slice(0,50)+"...":de.name),9,nT)]),"item-actions":Ne(({item:de})=>[d("div",oT,[d("button",{class:"btn-action btn-add",onClick:We=>a.addTurma(de),title:"Adicionar turma"},[d("img",{src:n.icons.plus,alt:"Adicionar turma"},null,8,aT)],8,iT),d("button",{class:pe(["btn-action",de.status==="Ativo"?"btn-deactivate":"btn-activate"]),onClick:We=>a.toggleCourseStatus(de),disabled:de.status==="Inativo"&&!de.can_activate||!de.can_activate,title:a.getStatusButtonTitle(de)},[d("i",{class:pe(de.status==="Ativo"?"fas fa-eye":"fas fa-eye-slash")},null,2)],10,lT),d("button",{class:"btn-action btn-delete",onClick:We=>a.deleteCourse(de),disabled:!de.can_delete,title:de.can_delete?"Excluir":"Não é possível excluir este curso"},t[29]||(t[29]=[d("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,uT)])]),"expanded-content":Ne(({item:de})=>[d("div",cT,[t[34]||(t[34]=d("div",{class:"turmas-header"},[d("div",{class:"turma-col"},"NOME DA TURMA"),d("div",{class:"turma-col"},"TIPO DE INSCRIÇÃO"),d("div",{class:"turma-col"},"QTD. DE VAGAS"),d("div",{class:"turma-col"},"QTD. DE ALUNOS INSCRITOS"),d("div",{class:"turma-col"},"DATA INÍCIO DA TURMA"),d("div",{class:"turma-col"},"DATA FIM DA TURMA"),d("div",{class:"turma-col"},"AÇÕES")],-1)),d("div",dT,[de.turmas&&de.turmas.length>0?(S(),O("div",fT,[(S(!0),O(Ie,null,at(de.turmas,(We,hs)=>(S(),O("div",{class:"turmas-row",key:hs},[d("div",hT,[d("span",{title:We.nome},q(We.nome.length>20?We.nome.slice(0,20)+"...":We.nome),9,pT)]),d("div",mT,q(a.getEnrolTypeLabel(We.enrol_type)),1),d("div",gT,q(We.vagas),1),d("div",vT,q(We.inscritos),1),d("div",_T,q(We.dataInicio),1),d("div",bT,q(We.dataFim),1),d("div",yT,[d("div",wT,[d("button",{class:"btn-action btn-users",onClick:zt=>a.viewRegisteredUsers(We),title:"Usuários Matriculados"},[d("img",{src:n.icons.users,alt:"Usuários Matriculados"},null,8,CT)],8,ET),d("button",{class:"btn-action btn-edit",onClick:zt=>a.editTurma(We),title:"Editar"},t[30]||(t[30]=[d("i",{class:"fas fa-pencil-alt"},null,-1)]),8,DT),d("button",{class:"btn-action btn-duplicate",onClick:zt=>a.duplicateTurma(We,de),title:"Duplicar Turma"},t[31]||(t[31]=[d("i",{class:"fas fa-copy"},null,-1)]),8,xT),d("button",{class:pe(["btn-action",We.status==="Ativo"?"btn-deactivate":"btn-activate"]),title:We.status==="Ativo"?"Inativar":"Ativar",onClick:zt=>a.toggleClassStatus(We)},[d("i",{class:pe(We.status==="Ativo"?"fas fa-eye":"fas fa-eye-slash")},null,2)],10,ST),d("button",{class:"btn-action btn-delete",onClick:zt=>a.removeTurma(de,hs),disabled:!We.can_delete,title:We.can_delete?"Excluir":"Não é possível excluir esta turma"},t[32]||(t[32]=[d("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,OT)])])]))),128))])):(S(),O("div",TT,t[33]||(t[33]=[d("span",null,"Nenhuma turma encontrada para este curso",-1)])))])])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]),M(Q,{ref:"pagination","current-page":n.currentPage,"onUpdate:currentPage":[t[11]||(t[11]=de=>n.currentPage=de),a.handlePageChange],"per-page":n.perPage,"onUpdate:perPage":t[12]||(t[12]=de=>n.perPage=de),total:n.totalItems},null,8,["current-page","per-page","total","onUpdate:currentPage"])])):ee("",!0)],2),t[35]||(t[35]=d("hr",null,null,-1)),d("div",NT,[d("div",IT,[M(ye,{variant:"primary",label:"Salvar",onClick:a.saveOffer},null,8,["onClick"]),M(ye,{variant:"secondary",label:"Cancelar",onClick:a.goBack},null,8,["onClick"])])]),t[36]||(t[36]=d("div",{class:"required-fields-message"},[d("div",{class:"form-info"},[Ue(" Este formulário contém campos obrigatórios marcados com "),d("i",{class:"fa fa-exclamation-circle text-danger"})])],-1)),M(Z,{modelValue:n.showAddCourseModalVisible,"onUpdate:modelValue":t[13]||(t[13]=de=>n.showAddCourseModalVisible=de),"offer-id":n.offerId,onConfirm:a.handleAddCourseConfirm},null,8,["modelValue","offer-id","onConfirm"]),M(he,{show:n.showCourseStatusModal,title:((V=n.selectedCourse)==null?void 0:V.status)==="Ativo"?"Ao inativar este curso da oferta, o curso e as turmas associadas serão tratados da seguinte forma:":"Confirmar Ativação",message:((te=n.selectedCourse)==null?void 0:te.status)==="Ativo"?"":"Tem certeza que deseja ativar este curso?","list-items":((Ke=n.selectedCourse)==null?void 0:Ke.status)==="Ativo"?["O curso não será mais disponibilizados na oferta, mas as turmas e matrículas permanecerão ativas.","Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.","Novos alunos não poderão ser inscritos através da oferta."]:[],"confirm-button-text":((vt=n.selectedCourse)==null?void 0:vt.status)==="Ativo"?"Inativar curso":"Ativar","cancel-button-text":"Cancelar",icon:((mt=n.selectedCourse)==null?void 0:mt.status)==="Ativo"?"warning":"question",onClose:t[14]||(t[14]=de=>n.showCourseStatusModal=!1),onConfirm:a.confirmToggleCourseStatus},null,8,["show","title","message","list-items","confirm-button-text","icon","onConfirm"]),M(he,{show:n.showDeleteCourseModal,title:"A exclusão deste curso da instância de oferta é uma ação irreversível",message:"Ele será desassociado e as turmas relacionadas serão removidas. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir curso","cancel-button-text":"Cancelar",icon:"warning",onClose:t[15]||(t[15]=de=>n.showDeleteCourseModal=!1),onConfirm:a.confirmDeleteCourse},null,8,["show","onConfirm"]),M(he,{show:n.showDeleteClassModal,title:"A exclusão desta turma é uma ação irreversível",message:"Todas as configurações realizadas serão excluídas e a turma será removida do curso. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir Turma","cancel-button-text":"Cancelar",icon:"warning",onClose:t[16]||(t[16]=de=>n.showDeleteClassModal=!1),onConfirm:a.confirmDeleteClass},null,8,["show","onConfirm"]),M(he,{show:n.showClassStatusModal,title:((ft=n.selectedClass)==null?void 0:ft.status)==="Ativo"?"Ao inativar esta turma, as matrículas e o curso associados serão tratados da seguinte forma:":"Confirmar Ativação",message:((Se=n.selectedClass)==null?void 0:Se.status)==="Ativo"?"":"Tem certeza que deseja ativar esta turma?","list-items":((we=n.selectedClass)==null?void 0:we.status)==="Ativo"?["Se o curso não possuir outra turma disponível, ele não será mais disponibilizado para novos usuários da oferta. No entanto, matrículas já realizadas permanecerão ativas.","Usuários já matriculados manterão o acesso ao curso normalmente até o encerramento da turma ou da sua matrícula.","Novos alunos não poderão ser matriculados através da oferta."]:[],"confirm-button-text":((Ft=n.selectedClass)==null?void 0:Ft.status)==="Ativo"?"Inativar Turma":"Ativar","cancel-button-text":"Cancelar",icon:((es=n.selectedClass)==null?void 0:es.status)==="Ativo"?"warning":"question",onClose:t[17]||(t[17]=de=>n.showClassStatusModal=!1),onConfirm:a.confirmToggleClassStatus},null,8,["show","title","message","list-items","confirm-button-text","icon","onConfirm"]),M(be,{show:n.showDuplicateClassModal,turma:n.classToDuplicate,parentCourse:n.classToDuplicateParentCourse,offerId:n.offerId,onClose:t[18]||(t[18]=de=>n.showDuplicateClassModal=!1),onSuccess:a.handleDuplicateSuccess,onLoading:t[19]||(t[19]=de=>n.loading=de),onError:a.showErrorMessage},null,8,["show","turma","parentCourse","offerId","onSuccess","onError"]),M(Ae,{show:n.showEnrolTypeModal,offercourseid:(bt=n.selectedCourseForClass)==null?void 0:bt.offerCourseId,offerid:n.offerId||"0",onClose:t[20]||(t[20]=de=>n.showEnrolTypeModal=!1),onConfirm:a.handleEnrolTypeConfirm},null,8,["show","offercourseid","offerid","onConfirm"]),M(ue,{"is-loading":n.loading},null,8,["is-loading"]),M(ae,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])])}const lp=je(VO,[["render",AT],["__scopeId","data-v-d92a2fa6"]]),mR="",MT={name:"NewClassView",components:{CustomInput:Fo,CustomSelect:mr,CustomButton:Ln,PageHeader:sa,BackButton:bu,Autocomplete:$o,TextEditor:_u,CustomCheckbox:Xi,FilterRow:ea,FilterGroup:ta,Toast:Lo,HelpIcon:yu,FilterTag:Bo,FilterTags:ra},setup(){const e=Zi(),t=Jh();return{router:e,route:t}},directives:{tooltip:{mounted(e,t){e.setAttribute("title",t.value)},updated(e,t){e.setAttribute("title",t.value)}}},props:{offercourseid:{type:Number,required:!0},classid:{type:Number,required:!1,default:null}},data(){return{loading:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,isEditing:!1,enrolmentMethods:[],classData:{enrol:"",offercourseid:null,classname:"",startdate:"",teachers:[],optional_fields:{enableenddate:!1,enddate:"",enablepreenrolment:!1,preenrolmentstartdate:"",preenrolmentenddate:"",description:"",enableenrolperiod:!1,enrolperiod:null,minusers:null,maxusers:null,roleid:null,enablereenrol:!1,reenrolmentsituations:[],enableextension:!1,extensionperiod:null,extensiondaysavailable:null,extensionmaxrequests:null,extensionallowedsituations:[]}},selectedTeachers:[],teacherSearchTerm:"",debounceTimer:null,teacherList:[],showTeacherDropdown:!1,highlightedIndex:-1,extensionSituations:[],reenrolSituations:[],roleOptions:[],situationList:[],offerCourse:null,formErrors:{enrol:{hasError:!1,message:"Método de inscrição é obrigatório"},classname:{hasError:!1,message:"Nome da turma é obrigatório"},startdate:{hasError:!1,message:"Data de início é obrigatória"},roleid:{hasError:!1,message:"Papel padrão é obrigatório"},enddate:{hasError:!1,message:"Data fim da turma é obrigatória quando habilitada"},preenrolmentstartdate:{hasError:!1,message:"Data início de pré-inscrição é obrigatória quando habilitada"},preenrolmentenddate:{hasError:!1,message:"Data fim de pré-inscrição é obrigatória quando habilitada"},enrolperiod:{hasError:!1,message:"Prazo de conclusão é obrigatório quando habilitado e não pode exceder o período entre as datas de início e fim da turma"},extensionperiod:{hasError:!1,message:"Dias para prorrogação é obrigatório quando habilitada"},extensiondaysavailable:{hasError:!1,message:"Dias antes do término é obrigatório quando habilitada"},extensionmaxrequests:{hasError:!1,message:"Máximo de solicitações é obrigatório quando habilitada"},extensionsituations:{hasError:!1,message:"É necessário selecionar pelo menos uma situação de matrícula para prorrogação"},minusers:{hasError:!1,message:"Mínimo de usuários deve ser maior ou igual a zero"},maxusers:{hasError:!1,message:"Máximo de usuários deve ser maior ou igual a zero"}},validationAlert:{show:!1,message:"Há campos obrigatórios a serem preenchidos. Por favor, verifique os campos destacados."}}},async created(){if(!this.offercourseid)throw new Error("offercourseid não foi definido.");this.classid?this.isEditing=!0:this.route.query.classid&&this.route.query.edit==="true"&&(this.isEditing=!0,this.$nextTick(()=>{this.router.replace({name:"EditClass",params:{offercourseid:this.offercourseid,classid:this.classid}})})),await this.loadInitialData(),this.isEditing&&this.classid&&(await this.loadClassData(),this.$nextTick(()=>{this.restartComponent()}))},mounted(){window.scrollTo(0,0),document.addEventListener("click",this.handleClickOutside)},beforeUnmount(){document.removeEventListener("click",this.handleClickOutside),this.debounceTimer&&clearTimeout(this.debounceTimer)},computed:{extensionSituationList(){let e=[0,1];return this.situationList.filter(t=>e.includes(t.value))},reenrolSituationList(){let e=[6,7,8,4,5];return this.situationList.filter(t=>e.includes(t.value))},maxEnrolPeriod(){if(this.classData.startdate&&this.classData.optional_fields.enddate&&this.classData.optional_fields.enableenddate){const e=this.calculateDaysDifference(this.classData.startdate,this.classData.optional_fields.enddate);return e>=1?e:1}return null},isOneDayClass(){return this.classData.startdate&&this.classData.optional_fields.enableenddate&&this.classData.optional_fields.enddate&&this.classData.startdate===this.classData.optional_fields.enddate},shouldDisableEnrolPeriod(){return this.isOneDayClass}},watch:{extensionSituations:{handler(e){this.classData.optional_fields.extensionallowedsituations=e.map(t=>t.value)},deep:!0},reenrolSituations:{handler(e){this.classData.optional_fields.reenrolmentsituations=e.map(t=>t.value)},deep:!0},"classData.optional_fields.enableenrolperiod":function(e){!e&&this.classData.optional_fields.enableextension&&(this.classData.optional_fields.enableextension=!1,this.showWarningMessage("Prorrogação de matrícula foi desabilitada automaticamente porque o Prazo de conclusão da turma foi desabilitado.")),this.validateField("extensionsituations")},"classData.startdate":function(){this.classData.optional_fields.enableenddate&&this.classData.optional_fields.enddate&&this.validateField("enddate"),this.classData.optional_fields.enableenrolperiod&&this.classData.optional_fields.enrolperiod&&this.validateField("enrolperiod"),this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates(),this.checkAndDisableEnrolPeriodForOneDayClass()},"classData.optional_fields.enddate":function(){this.classData.optional_fields.enableenddate&&this.validateField("enddate"),this.classData.optional_fields.enableenrolperiod&&this.classData.optional_fields.enrolperiod&&this.validateField("enrolperiod"),this.classData.optional_fields.enablepreenrolment&&this.classData.optional_fields.enableenddate&&this.validatePreenrolmentDates(),this.checkAndDisableEnrolPeriodForOneDayClass()},"classData.optional_fields.enableenddate":function(e){e&&this.classData.optional_fields.enddate&&this.validateField("enddate"),!e&&this.classData.optional_fields.enableenrolperiod&&this.classData.optional_fields.enrolperiod&&this.validateField("enrolperiod"),this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates(),this.checkAndDisableEnrolPeriodForOneDayClass()},"classData.optional_fields.enrolperiod":function(e){this.classData.optional_fields.enableenrolperiod&&e&&this.validateField("enrolperiod")},"classData.optional_fields.preenrolmentstartdate":function(){this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates()},"classData.optional_fields.preenrolmentenddate":function(){this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates()},"classData.optional_fields.minusers":function(){this.validateField("minusers"),this.classData.optional_fields.maxusers!==null&&this.classData.optional_fields.maxusers!==void 0&&this.validateField("maxusers")},"classData.optional_fields.maxusers":function(){this.validateField("maxusers"),this.classData.optional_fields.minusers!==null&&this.classData.optional_fields.minusers!==void 0&&this.validateField("minusers")}},methods:{calculateDaysDifference(e,t){if(!e||!t)return 0;const s=new Date(e),i=new Date(t);if(isNaN(s.getTime())||isNaN(i.getTime()))return 0;if(s.getTime()===i.getTime())return 1;const n=Math.abs(i-s);return Math.ceil(n/(1e3*60*60*24))},checkAndDisableEnrolPeriodForOneDayClass(){this.isOneDayClass&&this.classData.optional_fields.enableenrolperiod&&(this.classData.optional_fields.enableenrolperiod=!1,this.classData.optional_fields.enrolperiod=null,this.classData.optional_fields.enableextension&&(this.classData.optional_fields.enableextension=!1,this.classData.optional_fields.extensionperiod=null,this.classData.optional_fields.extensiondaysavailable=null,this.classData.optional_fields.extensionmaxrequests=null,this.classData.optional_fields.extensionallowedsituations=[],this.extensionSituations=[]),this.showWarningMessage("Prazo de conclusão foi desabilitado automaticamente porque a turma tem duração de apenas um dia (data início = data fim)."))},async loadInitialData(){try{this.loading=!0,this.isEditing||(this.classData.offercourseid=this.offercourseid),await this.loadOfferCourse(),await this.loadRoles(),await this.loadSituations(),await this.loadEnrolmentMethods()}catch{this.showErrorMessage("Alguns dados não puderam ser carregados.")}finally{this.loading=!1}},async loadOfferCourse(){try{const e=await iw(this.offercourseid);this.offerCourse=e==null?void 0:e.data}catch{this.showErrorMessage("Erro ao carregar informações do curso da oferta.")}},async loadRoles(){const e=await pu(this.offercourseid);if(e.data&&(this.roleOptions=e.data.map(t=>({value:t.id,label:t.name})),!this.classData.optional_fields.roleid)){const t=this.roleOptions.find(s=>s.value===5);this.classData.optional_fields.roleid=(t==null?void 0:t.value)??this.roleOptions[0].value}},async loadSituations(){const e=await dw();e.data&&(this.situationList=e.data.map(t=>({value:t.id,label:t.name})))},async loadEnrolmentMethods(){const e=this.route.query.enrol_type;!this.isEditing&&e&&(this.classData.enrol=e);const t=await op(!0);t&&Array.isArray(t)&&(this.enrolmentMethods=t.map(s=>({value:s.enrol,label:s.name})))},validate(){Object.keys(this.formErrors).forEach(n=>{this.formErrors[n].hasError=!1}),this.validationAlert.show=!1;let e=!1;this.classData.classname||(this.formErrors.classname.hasError=!0,e=!0),this.classData.startdate?this.classData.optional_fields.enableenddate&&this.classData.optional_fields.enddate&&new Date(this.classData.startdate)>new Date(this.classData.optional_fields.enddate)?(this.formErrors.startdate.message="Data de início deve ser igual ou anterior à data fim da turma",this.formErrors.startdate.hasError=!0,e=!0):this.formErrors.startdate.hasError=!1:(this.formErrors.startdate.message="Data de início é obrigatória",this.formErrors.startdate.hasError=!0,e=!0),this.classData.optional_fields.roleid||(this.formErrors.roleid.hasError=!0,e=!0),this.classData.optional_fields.enableenddate&&(this.classData.optional_fields.enddate?this.classData.startdate&&new Date(this.classData.optional_fields.enddate)<new Date(this.classData.startdate)?(this.formErrors.enddate.message="Data fim da turma deve ser igual ou posterior à data de início",this.formErrors.enddate.hasError=!0,e=!0):this.formErrors.enddate.hasError=!1:(this.formErrors.enddate.message="Data fim da turma é obrigatória quando habilitada",this.formErrors.enddate.hasError=!0,e=!0)),this.classData.optional_fields.enablepreenrolment&&(this.classData.optional_fields.preenrolmentstartdate||(this.formErrors.preenrolmentstartdate.hasError=!0,e=!0),this.classData.optional_fields.preenrolmentenddate||(this.formErrors.preenrolmentenddate.hasError=!0,e=!0)),this.validatePreenrolmentDates()||(e=!0),this.classData.optional_fields.enableenrolperiod&&(this.classData.optional_fields.enrolperiod?this.maxEnrolPeriod!==null&&parseInt(this.classData.optional_fields.enrolperiod)>this.maxEnrolPeriod&&(this.formErrors.enrolperiod.message=`Prazo de conclusão não pode exceder ${this.maxEnrolPeriod} dias (período entre as datas de início e fim da turma)`,this.formErrors.enrolperiod.hasError=!0,e=!0):(this.formErrors.enrolperiod.message="Prazo de conclusão é obrigatório quando habilitado",this.formErrors.enrolperiod.hasError=!0,e=!0)),this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&(this.classData.optional_fields.extensionperiod||(this.formErrors.extensionperiod.hasError=!0,e=!0),this.classData.optional_fields.extensiondaysavailable||(this.formErrors.extensiondaysavailable.hasError=!0,e=!0),this.classData.optional_fields.extensionmaxrequests||(this.formErrors.extensionmaxrequests.hasError=!0,e=!0),(!this.extensionSituations||this.extensionSituations.length===0)&&(this.formErrors.extensionsituations.hasError=!0,e=!0));const s=this.validateField("minusers"),i=this.validateField("maxusers");return(!s||!i)&&(e=!0),e&&(this.validationAlert.show=!0,this.showErrorMessage(this.validationAlert.message),window.scrollTo({top:0,behavior:"smooth"})),!e},validateField(e){switch(e){case"enrol":this.formErrors.enrol.hasError=!1;break;case"classname":this.formErrors.classname.hasError=!this.classData.classname;break;case"startdate":const s=this.classData.startdate,i=s&&this.classData.optional_fields.enableenddate&&this.classData.optional_fields.enddate&&new Date(this.classData.startdate)>new Date(this.classData.optional_fields.enddate);s?i?(this.formErrors.startdate.message="Data de início deve ser igual ou anterior à data fim da turma",this.formErrors.startdate.hasError=!0):(this.formErrors.startdate.message="Data de início é obrigatória",this.formErrors.startdate.hasError=!1):(this.formErrors.startdate.message="Data de início é obrigatória",this.formErrors.startdate.hasError=!0);break;case"roleid":this.formErrors.roleid.hasError=!this.classData.optional_fields.roleid;break;case"enddate":const n=this.classData.optional_fields.enableenddate,a=this.classData.optional_fields.enddate,u=n&&a&&this.classData.startdate&&new Date(this.classData.optional_fields.enddate)<new Date(this.classData.startdate);n&&!a?(this.formErrors.enddate.message="Data fim da turma é obrigatória quando habilitada",this.formErrors.enddate.hasError=!0):u?(this.formErrors.enddate.message="Data fim da turma deve ser igual ou posterior à data de início",this.formErrors.enddate.hasError=!0):(this.formErrors.enddate.message="Data fim da turma é obrigatória quando habilitada",this.formErrors.enddate.hasError=!1);break;case"preenrolmentstartdate":this.formErrors.preenrolmentstartdate.hasError=this.classData.optional_fields.enablepreenrolment&&!this.classData.optional_fields.preenrolmentstartdate,this.validatePreenrolmentDates();break;case"preenrolmentenddate":this.formErrors.preenrolmentenddate.hasError=this.classData.optional_fields.enablepreenrolment&&!this.classData.optional_fields.preenrolmentenddate,this.validatePreenrolmentDates();break;case"enrolperiod":const c=this.classData.optional_fields.enableenrolperiod,h=this.classData.optional_fields.enrolperiod!==null&&this.classData.optional_fields.enrolperiod!==void 0&&this.classData.optional_fields.enrolperiod!=="",m=this.maxEnrolPeriod!==null&&h&&parseInt(this.classData.optional_fields.enrolperiod)>this.maxEnrolPeriod;c&&!h?(this.formErrors.enrolperiod.message="Prazo de conclusão é obrigatório quando habilitado",this.formErrors.enrolperiod.hasError=!0):c&&m?(this.formErrors.enrolperiod.message=`Prazo de conclusão não pode exceder ${this.maxEnrolPeriod} dias (período entre as datas de início e fim da turma)`,this.formErrors.enrolperiod.hasError=!0):(this.formErrors.enrolperiod.message="Prazo de conclusão é obrigatório quando habilitado e não pode exceder o período entre as datas de início e fim da turma",this.formErrors.enrolperiod.hasError=!1);break;case"extensionperiod":this.formErrors.extensionperiod.hasError=this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&!this.classData.optional_fields.extensionperiod;break;case"extensiondaysavailable":this.formErrors.extensiondaysavailable.hasError=this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&!this.classData.optional_fields.extensiondaysavailable;break;case"extensionmaxrequests":this.formErrors.extensionmaxrequests.hasError=this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&!this.classData.optional_fields.extensionmaxrequests;break;case"extensionsituations":this.formErrors.extensionsituations.hasError=this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&(!this.extensionSituations||this.extensionSituations.length===0);break;case"minusers":this.validateMinUsers();break;case"maxusers":this.validateMaxUsers();break}const t=Object.values(this.formErrors).some(s=>s.hasError);return this.validationAlert.show=t,!this.formErrors[e].hasError},validatePreenrolmentDates(){let e=!0;if(this.formErrors.preenrolmentstartdate.hasError=!1,this.formErrors.preenrolmentenddate.hasError=!1,this.classData.optional_fields.enablepreenrolment){const t=this.classData.startdate,s=this.classData.optional_fields.enableenddate?this.classData.optional_fields.enddate:null,i=this.classData.optional_fields.preenrolmentstartdate,n=this.classData.optional_fields.preenrolmentenddate,a=this.offerCourse.startdate,u=this.offerCourse.enddate;i||(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início de pré-inscrição é obrigatória",e=!1),n||(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim de pré-inscrição é obrigatória",e=!1),new Date(n)<new Date(i)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser posterior à data início",e=!1),new Date(i)>new Date(t)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início deve ser igual ou anterior à data início da turma",e=!1),new Date(i)<new Date(a)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início deve ser igual ou posterior à data início do curso",e=!1),u&&new Date(i)>new Date(u)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data de início deve ser igual ou anterior à data fim do curso",e=!1),s&&new Date(n)>=new Date(s)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser anterior à data fim da turma",e=!1),u&&new Date(n)>new Date(u)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser igual ou anterior à data fim do curso",e=!1)}return e},validateMinUsers(){const e=this.classData.optional_fields.minusers,t=this.classData.optional_fields.maxusers,s=parseInt(e),i=parseInt(t);return this.formErrors.minusers.hasError=!1,s===0?!0:i>0&&s>i?(this.formErrors.minusers.message="Mínimo de usuários inscritos deve ser menor que o máximo de usuários inscritos",this.formErrors.minusers.hasError=!0,!1):!0},validateMaxUsers(){const e=this.classData.optional_fields.minusers,t=this.classData.optional_fields.maxusers,s=parseInt(t),i=parseInt(e);return this.formErrors.maxusers.hasError=!1,s===0?!0:i>0&&s<i?(this.formErrors.maxusers.message="Máximo de usuários inscritos deve ser maior que o mínimo de usuários inscritos",this.formErrors.maxusers.hasError=!0,!1):!0},async loadClassData(){this.loading=!0;const e=await hu(this.classid);if(e.error==!0)throw new Error(e.exception);this.classData=e.data,e.data.optional_fields&&this.processOptionalFields(e.data.optional_fields),e.data.teachers&&Array.isArray(e.data.teachers)&&(this.selectedTeachers=e.data.teachers),this.updateUIAfterLoading(),document.title=`Editar Turma: ${this.classData.classname}`,this.loading=!1},processOptionalFields(e){this.processDateFields(e),this.processEnrolmentFields(e),this.processUserLimits(e),this.processDescriptionAndRole(e),this.processReenrolment(e),this.processExtensionFields(e)},processDateFields(e){e.enableenddate&&(this.classData.optional_fields.enableenddate=!0,this.classData.optional_fields.enddate=e.enddate||null),e.enablepreenrolment&&(this.classData.optional_fields.enablepreenrolment=!0,this.classData.optional_fields.preenrolmentstartdate=e.preenrolmentstartdate||null,this.classData.optional_fields.preenrolmentenddate=e.preenrolmentenddate||null)},processEnrolmentFields(e){e.enableenrolperiod?(this.classData.optional_fields.enableenrolperiod=!0,this.classData.optional_fields.enrolperiod=e.enrolperiod>0?e.enrolperiod:null):this.classData.optional_fields.enrolperiod=null},processUserLimits(e){this.classData.optional_fields.minusers=e.minusers>0?e.minusers:null,this.classData.optional_fields.maxusers=e.maxusers>0?e.maxusers:null},processDescriptionAndRole(e){this.classData.optional_fields.roleid=e.roleid||null,this.classData.optional_fields.description=e.description||""},processReenrolment(e){e.enablereenrol?(this.classData.optional_fields.enablereenrol=!0,this.classData.optional_fields.reenrolmentsituations=e.reenrolmentsituations||[],Array.isArray(e.reenrolmentsituations)&&(this.reenrolSituations=e.reenrolmentsituations.map(t=>this.situationList.find(s=>s.value===parseInt(t))))):this.classData.optional_fields.reenrolmentsituations=[]},processExtensionFields(e){e.enableextension&&e.enableenrolperiod?(this.classData.optional_fields.enableextension=!0,this.processExtensionPeriods(e),this.processExtensionSituations(e)):this.resetExtensionFields()},processExtensionPeriods(e){this.classData.optional_fields.extensionperiod=e.extensionperiod>0?e.extensionperiod:null,this.classData.optional_fields.extensiondaysavailable=e.extensiondaysavailable>0?e.extensiondaysavailable:null,this.classData.optional_fields.extensionmaxrequests=e.extensionmaxrequests>0?e.extensionmaxrequests:null},processExtensionSituations(e){Array.isArray(e.extensionallowedsituations)&&(this.extensionSituations=e.extensionallowedsituations.map(t=>this.situationList.find(s=>s.value===t)))},resetExtensionFields(){this.classData.optional_fields.extensionperiod=null,this.classData.optional_fields.extensiondaysavailable=null,this.classData.optional_fields.extensionmaxrequests=null,this.classData.optional_fields.extensionallowedsituations=[],this.extensionSituations=[]},handleTeacherInput(){const e=this.teacherSearchTerm.trim();this.debounceTimer&&clearTimeout(this.debounceTimer),e.length>=3?(this.showTeacherDropdown=!0,this.highlightedIndex=-1,this.debounceTimer=setTimeout(async()=>{await this.fetchPotentialTeachers(e)},500)):(this.showTeacherDropdown=!1,this.teacherList=[])},async fetchPotentialTeachers(e){let t=this.selectedTeachers.map(i=>i.id||i.value)??[];const s=await cw(this.offercourseid,this.classid,e,t);this.teacherList=!s.error&&s.data?s.data:[]},removeTeacher(e){this.selectedTeachers=this.selectedTeachers.filter(t=>t.id!==e)},handleTeacherInputFocus(){this.teacherSearchTerm.length>=3&&this.teacherList.length>0&&(this.showTeacherDropdown=!0)},selectTeacher(e){this.selectedTeachers.push({id:e.id,value:e.id,fullname:e.fullname,email:e.email}),this.teacherSearchTerm="",this.showTeacherDropdown=!1,this.highlightedIndex=-1,this.teacherList=[],this.$nextTick(()=>{this.$refs.teacherSearchInput&&this.$refs.teacherSearchInput.focus()})},handleKeydown(e){if(!(!this.showTeacherDropdown||this.teacherList.length===0))switch(e.key){case"ArrowDown":e.preventDefault(),this.highlightedIndex=Math.min(this.highlightedIndex+1,this.teacherList.length-1);break;case"ArrowUp":e.preventDefault(),this.highlightedIndex=Math.max(this.highlightedIndex-1,0);break;case"Enter":e.preventDefault(),this.highlightedIndex>=0&&this.highlightedIndex<this.teacherList.length&&this.selectTeacher(this.teacherList[this.highlightedIndex]);break;case"Escape":e.preventDefault(),this.showTeacherDropdown=!1,this.highlightedIndex=-1;break}},handleClickOutside(e){this.$refs.teacherSearchContainer&&!this.$refs.teacherSearchContainer.contains(e.target)&&(this.showTeacherDropdown=!1,this.highlightedIndex=-1)},updateUIAfterLoading(){this.$nextTick(()=>{this.updateFormFields(),this.$forceUpdate(),(!this.classData.classname||!this.classData.enrol)&&this.showErrorMessage("Dados incompletos após carregamento.")})},updateFormFields(){this.updateSelectField("enrolSelect",this.classData.enrol),this.updateInputField("classnameInput",this.classData.classname),this.updateInputField("startdateInput",this.classData.startdate)},updateSelectField(e,t){if(this.$refs[e]){this.$refs[e].value=t;const s=new Event("change");this.$refs[e].$el.dispatchEvent(s)}},updateInputField(e,t){if(this.$refs[e]&&t){this.$refs[e].value=t;const s=new Event("input");this.$refs[e].$el.dispatchEvent(s)}},async saveClass(){if(!this.validate())return;this.loading=!0;const e=JSON.parse(JSON.stringify(this.classData));e.teachers=this.selectedTeachers.map(n=>n.id),!e.optional_fields.enableextension||!e.optional_fields.enableenrolperiod?(e.optional_fields.extensionperiod=void 0,e.optional_fields.extensiondaysavailable=void 0,e.optional_fields.extensionmaxrequests=void 0,e.optional_fields.extensionallowedsituations=[],e.optional_fields.enableenrolperiod||(e.optional_fields.enableextension=!1)):e.optional_fields.extensionallowedsituations=this.extensionSituations.map(n=>n.value),e.optional_fields.enableenrolperiod||(e.optional_fields.enrolperiod=void 0),e.optional_fields.enablereenrol?e.optional_fields.reenrolmentsituations=this.reenrolSituations.map(n=>n.value):e.optional_fields.reenrolmentsituations=[],["enrolperiod","extensionperiod","extensiondaysavailable","extensionmaxrequests","minusers","maxusers"].forEach(n=>{const a=e.optional_fields[n];(a===0||a===null||a===""||a===void 0)&&(e.optional_fields[n]=void 0)}),this.isEditing&&"enrol"in e&&delete e.enrol;const i=(this.isEditing?["offercourseid","classname","startdate"]:["offercourseid","classname","startdate","enrol"]).filter(n=>!e[n]);if(i.length>0){this.showErrorMessage(`Campos obrigatórios ausentes: ${i.join(", ")}`),this.loading=!1;return}if(e.offercourseid=parseInt(e.offercourseid),this.isEditing&&this.classid){e.offerclassid=this.classid;let n=await lw(e);!n.error&&n.data?(this.showSuccessMessage(n.data.message),this.loadClassData()):this.showErrorMessage(n.exception.message)}else{let n=await ow(e);!n.error&&n.data?(this.showSuccessMessage(n.data.message),this.isEditing=!0,this.router.push({name:"EditClass",params:{offercourseid:this.offercourseid,classid:n.data.offerclassid}})):this.showErrorMessage(n.exception.message)}this.loading=!1},goBack(){this.offerCourse.offerid?this.router.push({name:"editar-oferta",params:{id:this.offerCourse.offerid}}):this.router.push({name:"listar-ofertas"})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showWarningMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="warning",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},updateFormFields(){if(this.$refs.enrolSelect&&this.classData.enrol)try{this.$refs.enrolSelect.value=this.classData.enrol;const e=new Event("change");this.$refs.enrolSelect.$el.dispatchEvent(e),this.$refs.enrolSelect.$emit("input",this.classData.enrol),this.$refs.enrolSelect.$forceUpdate()}catch{}if(this.$refs.classnameInput&&this.classData.classname)try{this.$refs.classnameInput.value=this.classData.classname;const e=new Event("input");this.$refs.classnameInput.$el.dispatchEvent(e),this.$refs.classnameInput.$emit("input",this.classData.classname),this.$refs.classnameInput.$forceUpdate()}catch{}if(this.$refs.startdateInput&&this.classData.startdate)try{this.$refs.startdateInput.value=this.classData.startdate;const e=new Event("input");this.$refs.startdateInput.$el.dispatchEvent(e),this.$refs.startdateInput.$emit("input",this.classData.startdate),this.$refs.startdateInput.$forceUpdate()}catch{}this.$forceUpdate()},handleSelectAllExtensionSituations(){const e=this.extensionSituationList.every(t=>this.extensionSituations.some(s=>s.value===t.value));this.extensionSituations=e?[]:[...this.extensionSituationList],this.classData.optional_fields.extensionallowedsituations=this.extensionSituations.map(t=>t.value)},handleSelectAllReenrolSituations(){const e=this.reenrolSituationList.every(t=>this.reenrolSituations.some(s=>s.value===t.value));this.reenrolSituations=e?[]:[...this.reenrolSituationList],this.classData.optional_fields.reenrolmentsituations=this.reenrolSituations.map(t=>t.value)},restartComponent(){window.scrollTo(0,0),this.updateFormFields(),this.$forceUpdate(),setTimeout(()=>{this.updateFormFields(),this.$forceUpdate(),window.scrollTo(0,0)},500)}}},PT={class:"new-class",ref:"classView"},kT={class:"page-header-container"},VT={key:0,class:"validation-alert"},RT={class:"section-container"},UT={class:"form-group mb-4 mb-sm-3"},FT={class:"label-with-help"},LT={class:"limited-width-input",style:{"max-width":"280px"}},BT={class:"form-row mb-3"},$T={class:"form-group"},jT={class:"label-with-help"},HT={class:"limited-width-input"},qT={class:"label-with-help"},zT={class:"input-with-checkbox"},WT={class:"limited-width-input"},GT={class:"form-row mb-3"},KT={class:"label-with-help"},QT={class:"label-with-help"},YT={key:2,class:"form-group"},ZT={class:"form-group mb-3"},JT={class:"label-with-help"},XT={class:"limited-width-editor"},eN={class:"form-row mb-3"},tN={key:0,class:"form-group"},sN={class:"label-with-help"},rN={class:"limited-width-input"},nN={key:1,class:"form-group"},oN={class:"label-with-help"},iN={class:"limited-width-input"},aN={class:"form-group"},lN={class:"label-with-help"},uN={class:"limited-width-input"},cN={class:"form-row mb-3"},dN={class:"label-with-help"},fN={class:"input-with-checkbox"},hN={class:"limited-width-input"},pN={class:"section-container"},mN={class:"form-row mb-3"},gN={class:"label-with-help"},vN={class:"form-row mb-3"},_N={class:"limited-width-input"},bN={class:"form-row mb-3"},yN={class:"limited-width-input"},wN={class:"form-row mb-3"},EN={class:"limited-width-input"},CN={class:"limited-width-select"},DN={key:0,class:"text-danger"},xN={key:1,class:"section-container"},SN={class:"form-row mb-3"},ON={class:"form-group"},TN={class:"label-with-help"},NN={class:"limited-width-select"},IN={class:"section-container"},AN={class:"form-group mb-3"},MN={class:"label-with-help"},PN={class:"limited-width-select"},kN={class:"position-relative",ref:"teacherSearchContainer"},VN={class:"input-wrapper with-icon"},RN={key:0,class:"dropdown-menu show position-absolute w-100 border rounded-bottom shadow-sm",style:{top:"100%",left:"0",right:"0","max-height":"200px","overflow-y":"auto","z-index":"1000","border-top":"none","border-radius":"0 0 0.375rem 0.375rem"},ref:"teacherDropdown"},UN=["onClick","onMouseenter"],FN={key:0,class:"text-muted small"},LN={key:1,class:"dropdown-menu show position-absolute w-100 border rounded-bottom shadow-sm",style:{top:"100%",left:"0",right:"0","z-index":"1000","border-top":"none","border-radius":"0 0 0.375rem 0.375rem"}},BN={class:"my-4"},$N=["onClick"],jN={class:"actions-container"},HN={key:2,class:"loading"};function qN(e,t,s,i,n,a){const u=X("BackButton"),c=X("PageHeader"),h=X("HelpIcon"),m=X("CustomInput"),p=X("CustomCheckbox"),v=X("TextEditor"),w=X("CustomSelect"),D=X("Autocomplete"),k=X("CustomButton"),L=X("Toast"),re=p_("tooltip");return S(),O("div",PT,[d("div",kT,[M(c,{title:n.isEditing?"Editar turma":"Nova turma"},{actions:Ne(()=>[M(u,{onClick:a.goBack},null,8,["onClick"])]),_:1},8,["title"])]),n.validationAlert.show?(S(),O("div",VT,[t[36]||(t[36]=d("i",{class:"fas fa-exclamation-triangle"},null,-1)),d("span",null,q(n.validationAlert.message),1)])):ee("",!0),d("div",RT,[t[48]||(t[48]=d("h2",{class:"section-title"},"CONFIGURAÇÕES GERAIS",-1)),d("div",UT,[d("div",FT,[t[37]||(t[37]=d("label",{class:"form-label"},[Ue("Nome da turma "),d("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})],-1)),M(h,{title:"Ajuda com nome da turma",text:"Insira um nome para a turma. Exemplo: Turma ADM 2025."})]),d("div",LT,[M(m,{modelValue:n.classData.classname,"onUpdate:modelValue":t[0]||(t[0]=I=>n.classData.classname=I),placeholder:"Digite o nome da turma",width:280,required:"",ref:"classnameInput","has-error":n.formErrors.classname.hasError,"error-message":n.formErrors.classname.message,onValidate:t[1]||(t[1]=I=>a.validateField("classname"))},null,8,["modelValue","has-error","error-message"])])]),d("div",BT,[d("div",$T,[d("div",jT,[t[38]||(t[38]=d("label",{class:"form-label"},[Ue("Data início da turma "),d("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})],-1)),M(h,{title:"Ajuda com data início da turma",text:"Insira uma data de início para a turma. Exemplo: 16/03/2025."})]),d("div",HT,[M(m,{modelValue:n.classData.startdate,"onUpdate:modelValue":t[2]||(t[2]=I=>n.classData.startdate=I),type:"date",width:180,required:"",class:"date-input",ref:"startdateInput","has-error":n.formErrors.startdate.hasError,"error-message":n.formErrors.startdate.message,onValidate:t[3]||(t[3]=I=>a.validateField("startdate"))},null,8,["modelValue","has-error","error-message"])])]),d("div",{class:pe(["form-group",{disabled:!n.classData.optional_fields.enableenddate}])},[d("div",qT,[t[39]||(t[39]=d("label",{class:"form-label"},[Ue("Data fim da turma "),d("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})],-1)),M(h,{title:"Ajuda com data fim da turma",text:"Insira uma data fim para a turma. Exemplo: 16/12/2025."})]),d("div",zT,[d("div",WT,[M(m,{modelValue:n.classData.optional_fields.enddate,"onUpdate:modelValue":t[4]||(t[4]=I=>n.classData.optional_fields.enddate=I),type:"date",width:180,disabled:!n.classData.optional_fields.enableenddate,required:"",class:"date-input","has-error":n.formErrors.enddate.hasError,"error-message":n.formErrors.enddate.message,onValidate:t[5]||(t[5]=I=>a.validateField("enddate"))},null,8,["modelValue","disabled","has-error","error-message"])]),M(p,{modelValue:n.classData.optional_fields.enableenddate,"onUpdate:modelValue":t[6]||(t[6]=I=>n.classData.optional_fields.enableenddate=I),id:"enableEndDate",label:"Habilitar data fim da turma",class:"inline-checkbox",disabled:!1},null,8,["modelValue"])])],2)]),d("div",GT,[n.classData.enrol=="offer_self"?(S(),O("div",{key:0,class:pe(["form-group",{disabled:!n.classData.optional_fields.enablepreenrolment}])},[d("div",KT,[t[40]||(t[40]=d("label",{class:"form-label"},"Data início pré-inscrição",-1)),M(h,{title:"Ajuda com data início pré-inscrição",text:"Data de início do período de pré-inscrição."})]),M(m,{modelValue:n.classData.optional_fields.preenrolmentstartdate,"onUpdate:modelValue":t[7]||(t[7]=I=>n.classData.optional_fields.preenrolmentstartdate=I),type:"date",width:180,disabled:!n.classData.optional_fields.enablepreenrolment,class:"date-input","has-error":n.formErrors.preenrolmentstartdate.hasError,"error-message":n.formErrors.preenrolmentstartdate.message,onValidate:t[8]||(t[8]=I=>a.validateField("preenrolmentstartdate"))},null,8,["modelValue","disabled","has-error","error-message"])],2)):ee("",!0),n.classData.enrol=="offer_self"?(S(),O("div",{key:1,class:pe(["form-group",{disabled:!n.classData.optional_fields.enablepreenrolment}])},[d("div",QT,[t[41]||(t[41]=d("label",{class:"form-label"},"Data fim pré-inscrição",-1)),M(h,{title:"Ajuda com data fim pré-inscrição",text:"Data de término do período de pré-inscrição."})]),M(m,{modelValue:n.classData.optional_fields.preenrolmentenddate,"onUpdate:modelValue":t[9]||(t[9]=I=>n.classData.optional_fields.preenrolmentenddate=I),type:"date",width:180,disabled:!n.classData.optional_fields.enablepreenrolment,class:"date-input","has-error":n.formErrors.preenrolmentenddate.hasError,"error-message":n.formErrors.preenrolmentenddate.message,onValidate:t[10]||(t[10]=I=>a.validateField("preenrolmentenddate"))},null,8,["modelValue","disabled","has-error","error-message"])],2)):ee("",!0),n.classData.enrol=="offer_self"?(S(),O("div",YT,[t[42]||(t[42]=d("div",{class:"label-with-help d-none d-sm-flex"},[d("label",{class:"form-label"}," ")],-1)),M(p,{modelValue:n.classData.optional_fields.enablepreenrolment,"onUpdate:modelValue":t[11]||(t[11]=I=>n.classData.optional_fields.enablepreenrolment=I),id:"enablePreEnrolment",label:"Habilitar pré-inscrição",disabled:!1},null,8,["modelValue"])])):ee("",!0)]),d("div",ZT,[d("div",JT,[t[43]||(t[43]=d("label",{class:"form-label"},"Descrição da turma",-1)),M(h,{title:"Ajuda com descrição da turma",text:"Esta descrição estará disponível para os usuários na página intermediária do curso. Exemplo: Esta turma se destina a usuários com os cargos administrativos que foram selecionados para a realização dos cursos obrigatórios do ano de 2025."})]),d("div",XT,[M(v,{modelValue:n.classData.optional_fields.description,"onUpdate:modelValue":t[12]||(t[12]=I=>n.classData.optional_fields.description=I),placeholder:"Digite a descrição da turma aqui...",rows:5,disabled:!1},null,8,["modelValue"])])]),d("div",eN,[n.classData.enrol=="offer_self"?(S(),O("div",tN,[d("div",sN,[t[44]||(t[44]=d("label",{class:"form-label"},"Mínimo de usuários inscritos",-1)),M(h,{title:"Ajuda com mínimo de usuários",text:"Número mínimo de usuários para a turma."})]),d("div",rN,[M(m,{modelValue:n.classData.optional_fields.minusers,"onUpdate:modelValue":t[13]||(t[13]=I=>n.classData.optional_fields.minusers=I),type:"number",width:180,"has-error":n.formErrors.minusers.hasError,"error-message":n.formErrors.minusers.message,onValidate:t[14]||(t[14]=I=>a.validateField("minusers")),min:0},null,8,["modelValue","has-error","error-message"])])])):ee("",!0),n.classData.enrol=="offer_self"?(S(),O("div",nN,[d("div",oN,[t[45]||(t[45]=d("label",{class:"form-label"},"Máximo de usuários inscritos",-1)),M(h,{title:"Ajuda com máximo de usuários",text:"Número máximo de usuários para a turma."})]),d("div",iN,[M(m,{modelValue:n.classData.optional_fields.maxusers,"onUpdate:modelValue":t[15]||(t[15]=I=>n.classData.optional_fields.maxusers=I),type:"number",width:180,"has-error":n.formErrors.maxusers.hasError,"error-message":n.formErrors.maxusers.message,onValidate:t[16]||(t[16]=I=>a.validateField("maxusers")),min:0},null,8,["modelValue","has-error","error-message"])])])):ee("",!0),d("div",aN,[d("div",lN,[t[46]||(t[46]=d("label",{class:"form-label"},[Ue("Papel atribuído por padrão "),d("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})],-1)),M(h,{title:"Ajuda com papel atribuído por padrão",text:"O papel atribuído será o papel que o usuário receberá dentro do curso após sua matrícula na turma. Exemplo: Estudante, Professor, Coordenador, etc…"})]),d("div",uN,[M(w,{modelValue:n.classData.optional_fields.roleid,"onUpdate:modelValue":t[17]||(t[17]=I=>n.classData.optional_fields.roleid=I),options:n.roleOptions,width:180,required:"","has-error":n.formErrors.roleid.hasError,"error-message":n.formErrors.roleid.message,onValidate:t[18]||(t[18]=I=>a.validateField("roleid"))},null,8,["modelValue","options","has-error","error-message"])])])]),d("div",cN,[d("div",{class:pe(["form-group",{disabled:!n.classData.optional_fields.enableenrolperiod}])},[d("div",dN,[t[47]||(t[47]=d("label",{class:"form-label"},[Ue("Prazo de conclusão da turma "),d("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})],-1)),M(h,{title:"Ajuda com prazo de conclusão da turma",text:"O prazo de conclusão refere-se a quantidade de dias que um usuário terá para realizar o curso. Após esse período, se o usuário não obtiver uma situação de êxito na matrícula (Aprovado ou Concluído) durante seu progresso no curso, o sistema encerrará sua matrícula."+(a.maxEnrolPeriod?` O valor máximo permitido é de ${a.maxEnrolPeriod} dias, que corresponde ao período entre as datas de início e fim da turma.`:"")},null,8,["text"])]),d("div",fN,[d("div",hN,[M(m,{modelValue:n.classData.optional_fields.enrolperiod,"onUpdate:modelValue":t[19]||(t[19]=I=>n.classData.optional_fields.enrolperiod=I),type:"number",width:180,disabled:!n.classData.optional_fields.enableenrolperiod,required:"","has-error":n.formErrors.enrolperiod.hasError,"error-message":n.formErrors.enrolperiod.message,max:a.maxEnrolPeriod,onValidate:t[20]||(t[20]=I=>a.validateField("enrolperiod"))},null,8,["modelValue","disabled","has-error","error-message","max"])]),ut(M(p,{modelValue:n.classData.optional_fields.enableenrolperiod,"onUpdate:modelValue":t[21]||(t[21]=I=>n.classData.optional_fields.enableenrolperiod=I),id:"enableEnrolPeriod",label:"Habilitar prazo de conclusão",class:"inline-checkbox",disabled:a.shouldDisableEnrolPeriod},null,8,["modelValue","disabled"]),[[re,a.shouldDisableEnrolPeriod?"Não é possível habilitar prazo de conclusão para turmas de um dia (data início = data fim)":""]])])],2)])]),d("div",pN,[d("div",mN,[d("div",{class:pe(["form-group",{disabled:!n.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[d("div",gN,[t[49]||(t[49]=d("label",{class:"form-label"},"Prorrogação de matrícula",-1)),M(h,{title:"Ajuda com prorrogação de matrícula",text:"A prorrogação estende o Prazo de conclusão do usuário, permitindo sua permanência na turma enquanto ela estiver ativa. No entanto, não redefine seu progresso, garantindo que ele retome o curso de onde parou. Nota: A prorrogação só pode ser habilitada quando o Prazo de conclusão da turma estiver habilitado."})]),ut(M(p,{modelValue:n.classData.optional_fields.enableextension,"onUpdate:modelValue":t[22]||(t[22]=I=>n.classData.optional_fields.enableextension=I),id:"enableExtension",label:"Habilitar Prorrogação de matrícula",disabled:!n.classData.optional_fields.enableenrolperiod},null,8,["modelValue","disabled"]),[[re,n.classData.optional_fields.enableenrolperiod?"":"É necessário habilitar o Prazo de conclusão da turma primeiro"]])],2)]),d("div",vN,[d("div",{class:pe(["form-group",{disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[50]||(t[50]=d("div",{class:"label-with-help"},[d("label",{class:"form-label"},[Ue("Quantos dias serão acrescentados para prorrogação? "),d("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})])],-1)),d("div",_N,[M(m,{modelValue:n.classData.optional_fields.extensionperiod,"onUpdate:modelValue":t[23]||(t[23]=I=>n.classData.optional_fields.extensionperiod=I),type:"number",width:180,disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,required:"","has-error":n.formErrors.extensionperiod.hasError,"error-message":n.formErrors.extensionperiod.message,onValidate:t[24]||(t[24]=I=>a.validateField("extensionperiod"))},null,8,["modelValue","disabled","has-error","error-message"])])],2)]),d("div",bN,[d("div",{class:pe(["form-group",{disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[51]||(t[51]=d("div",{class:"label-with-help"},[d("label",{class:"form-label"},[Ue("Quantos dias antes do término do prazo de matrícula o botão de prorrogação deve ser exibido? "),d("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})])],-1)),d("div",yN,[M(m,{modelValue:n.classData.optional_fields.extensiondaysavailable,"onUpdate:modelValue":t[25]||(t[25]=I=>n.classData.optional_fields.extensiondaysavailable=I),type:"number",width:180,disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,required:"","has-error":n.formErrors.extensiondaysavailable.hasError,"error-message":n.formErrors.extensiondaysavailable.message,onValidate:t[26]||(t[26]=I=>a.validateField("extensiondaysavailable"))},null,8,["modelValue","disabled","has-error","error-message"])])],2)]),d("div",wN,[d("div",{class:pe(["form-group",{disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[52]||(t[52]=d("div",{class:"label-with-help"},[d("label",{class:"form-label"},[Ue("Quantas vezes o usuário pode pedir prorrogação? "),d("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})])],-1)),d("div",EN,[M(m,{modelValue:n.classData.optional_fields.extensionmaxrequests,"onUpdate:modelValue":t[27]||(t[27]=I=>n.classData.optional_fields.extensionmaxrequests=I),type:"number",width:180,disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,required:"","has-error":n.formErrors.extensionmaxrequests.hasError,"error-message":n.formErrors.extensionmaxrequests.message,onValidate:t[28]||(t[28]=I=>a.validateField("extensionmaxrequests"))},null,8,["modelValue","disabled","has-error","error-message"])])],2)]),d("div",{class:pe(["form-group mb-3",{disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[53]||(t[53]=d("div",{class:"label-with-help"},[d("label",{class:"form-label"},"Para quais situações de matrícula é permitida a prorrogação?")],-1)),d("div",CN,[M(D,{modelValue:n.extensionSituations,"onUpdate:modelValue":t[29]||(t[29]=I=>n.extensionSituations=I),items:a.extensionSituationList,placeholder:"Selecione as situações...",disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,width:280,"show-all-option":!0,"auto-open":!1,onSelectAll:a.handleSelectAllExtensionSituations},null,8,["modelValue","items","disabled","onSelectAll"]),n.formErrors.extensionsituations.hasError?(S(),O("div",DN,q(n.formErrors.extensionsituations.message),1)):ee("",!0)])],2)]),n.classData.enrol=="offer_self"?(S(),O("div",xN,[d("div",SN,[d("div",ON,[d("div",TN,[t[54]||(t[54]=d("label",{class:"form-label"},"Habilitar rematrícula",-1)),M(h,{title:"Ajuda com habilitar rematrícula",text:"Permite que usuários se matriculem novamente na turma após concluírem ou saírem dela."})]),M(p,{modelValue:n.classData.optional_fields.enablereenrol,"onUpdate:modelValue":t[30]||(t[30]=I=>n.classData.optional_fields.enablereenrol=I),id:"enableReenrol",label:"Habilitar rematrícula",disabled:!1},null,8,["modelValue"])])]),d("div",{class:pe(["form-group mb-3",{disabled:!n.classData.optional_fields.enablereenrol}])},[t[55]||(t[55]=d("div",{class:"label-with-help"},[d("label",{class:"form-label"},"Quais situações de matrícula permitem rematrícula?")],-1)),d("div",NN,[M(D,{modelValue:n.reenrolSituations,"onUpdate:modelValue":t[31]||(t[31]=I=>n.reenrolSituations=I),items:a.reenrolSituationList,placeholder:"Selecione as situações...",disabled:!n.classData.optional_fields.enablereenrol,width:280,"show-all-option":!0,"auto-open":!1,onSelectAll:a.handleSelectAllReenrolSituations},null,8,["modelValue","items","disabled","onSelectAll"])])],2)])):ee("",!0),d("div",IN,[d("div",AN,[d("div",MN,[t[56]||(t[56]=d("label",{class:"form-label"},"Atribuir corpo docente",-1)),M(h,{title:"Ajuda com atribuir corpo docente",text:"Ao selecionar usuários para a composição do corpo docente, ele será matriculado na turma com o papel “Professor”."})]),d("div",PN,[d("div",kN,[d("div",VN,[ut(d("input",{type:"text","onUpdate:modelValue":t[32]||(t[32]=I=>n.teacherSearchTerm=I),placeholder:"Pesquisar ...",class:"form-control custom-input",onInput:t[33]||(t[33]=(...I)=>a.handleTeacherInput&&a.handleTeacherInput(...I)),onFocus:t[34]||(t[34]=(...I)=>a.handleTeacherInputFocus&&a.handleTeacherInputFocus(...I)),onKeydown:t[35]||(t[35]=(...I)=>a.handleKeydown&&a.handleKeydown(...I)),ref:"teacherSearchInput"},null,544),[[Xt,n.teacherSearchTerm]])]),n.showTeacherDropdown&&n.teacherList.length>0?(S(),O("div",RN,[(S(!0),O(Ie,null,at(n.teacherList,(I,ne)=>(S(),O("div",{key:I.id,class:pe(["dropdown-item",{active:n.highlightedIndex===ne}]),onClick:Q=>a.selectTeacher(I),onMouseenter:Q=>n.highlightedIndex=ne},[d("div",null,[d("div",null,q(I.fullname),1),I.email?(S(),O("div",FN,q(I.email),1)):ee("",!0)])],42,UN))),128))],512)):ee("",!0),n.showTeacherDropdown&&n.teacherSearchTerm.length>=3&&n.teacherList.length===0?(S(),O("div",LN,t[57]||(t[57]=[d("div",{class:"dropdown-item-text text-center fst-italic"},"Nenhum professor encontrado",-1)]))):ee("",!0)],512),d("div",BN,[(S(!0),O(Ie,null,at(n.selectedTeachers,I=>(S(),O("a",{key:I.id,class:"tag badge bg-primary text-white p-2 cursor-pointer mr-2",onClick:ne=>a.removeTeacher(I.id)},[t[58]||(t[58]=d("i",{class:"fas fa-times mr-1"},null,-1)),Ue(" "+q(I.fullname),1)],8,$N))),128))])])])]),t[60]||(t[60]=d("div",{class:"required-fields-message"},[d("div",{class:"form-info"},[Ue(" Este formulário contém campos obrigatórios marcados com "),d("i",{class:"fa fa-exclamation-circle text-danger"})])],-1)),d("div",jN,[M(k,{variant:"primary",label:"Salvar",loading:n.loading,onClick:a.saveClass},null,8,["loading","onClick"]),M(k,{variant:"secondary",label:"Cancelar",onClick:a.goBack},null,8,["onClick"])]),n.loading?(S(),O("div",HN,t[59]||(t[59]=[d("div",{class:"spinner-border",role:"status"},[d("span",{class:"sr-only"},"Carregando...")],-1)]))):ee("",!0),M(L,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])],512)}const up=je(MT,[["render",qN],["__scopeId","data-v-0c94c468"]]),zN=[{path:"/",name:"listar-ofertas",component:Z1,meta:{title:"Gerenciar Ofertas"}},{path:"/new-offer",name:"nova-oferta",component:lp,meta:{title:"Nova Oferta"}},{path:"/edit-offer/:id",name:"editar-oferta",component:lp,props:!0,meta:{title:"Editar Oferta"}},{path:"/new-class/:offercourseid",name:"NewClass",component:up,props:!0,meta:{title:"Nova Turma"}},{path:"/edit-class/:offercourseid/:classid",name:"EditClass",component:up,props:!0,meta:{title:"Editar Turma"}},{path:"/new-subscribed-users/:offerclassid",name:"usuarios-matriculados",component:SS,props:!0,meta:{title:"Usuários matriculados"}},{path:"/:pathMatch(.*)*",redirect:"/"}],jo=G0({history:n0("/local/offermanager/"),routes:zN,scrollBehavior(){return{top:0}}});jo.beforeEach((e,t,s)=>{document.title=e.meta.title||"Gerenciar Ofertas",s()}),jo.onError(e=>{console.error("Erro de navegação:",e),(e.name==="NavigationDuplicated"||e.message.includes("No match")||e.message.includes("missing required param"))&&jo.push("/")});const gR="",WN=()=>{const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)};return{init:(e,t={})=>{WN();const s=Wb(Ty);if(s.use(Dy()),s.use(jo),t&&t.route){let n={"new-offer":"/new-offer","edit-offer":"/edit-offer","new-subscribed-users":"/new-subscribed-users","new-class":"/new-class","edit-class":"/edit-class"}[t.route]||"/";t.route==="edit-offer"&&t.offerId&&(n=`/edit-offer/${t.offerId}`),t.route==="new-subscribed-users"&&t.subscribeId&&(n=`/new-subscribed-users/${t.subscribeId}`),t.route==="new-class"&&t.offercourseid&&(n=`/new-class/${t.offercourseid}`),t.route==="edit-class"&&t.offercourseid&&t.classid&&(n=`/edit-class/${t.offercourseid}/${t.classid}`),jo.replace(n)}return s.mount(e),s}}});
//# sourceMappingURL=app-lazy.min.js.map
