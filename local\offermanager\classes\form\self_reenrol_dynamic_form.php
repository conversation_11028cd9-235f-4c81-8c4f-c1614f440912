<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\form;

use core_form\dynamic_form;
use local_offermanager\persistent\offer_user_enrol_model;
use stdClass;
use context; // Added use statement
use context_user; // Added use statement
use moodle_url;
use dml_exception;
use moodle_exception;
use context_offer_class;
use html_writer;
use coding_exception;

defined('MOODLE_INTERNAL') || die();

/**
 * Class self_reenrol_dynamic_form
 *
 * Dynamic form for user self re-enrolment in an offer class.
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class self_reenrol_dynamic_form extends dynamic_form {

    /**
     * Define form elements.
     *
     * @return void
     * @throws coding_exception
     * @throws dml_exception
     */
    protected function definition(): void {
        $mform = $this->_form;
        $offeruserenrolid = $this->optional_param('offeruserenrolid', 0, PARAM_INT);

        $offer_user_enrol = offer_user_enrol_model::get_record(['id' => $offeruserenrolid]);
        $offerclass = $offer_user_enrol->get_offer_class();
        $description = $offerclass->get_description_html();
        $enrolperiod_str = $offerclass->get_formated_enrol_period();

        $classname = $offerclass->get_mapped_field('classname');

        $mform->addElement('hidden', 'id');
        $mform->setType('id', PARAM_INT);

        // Form header and info.
        $mform->addElement('header', 'reenrolheader', get_string('reenrolme', 'local_offermanager', (object)['classname' => $classname]));

        if ($description) {
            $mform->addElement(
                'static',
                'description',
                get_string('description'),
                $description
            );
        }

        $mform->addElement(
            'static',
            'enrolperiod',
            get_string('enrolperiod', 'local_offermanager'),
            $enrolperiod_str
        );

        $mform->addElement(
            'html',
            html_writer::div(get_string('reenrol_info', 'local_offermanager'),'my-2 px-4')
        );
    }

    /**
     * Get the context for dynamic submission.
     *
     * @return context The context.
     */
    public function get_context_for_dynamic_submission(): context {
        global $USER;
        return context_user::instance($USER->id);
    }

    /**
     * Check access for dynamic submission.
     *
     * @return void
     * @throws dml_exception
     * @throws moodle_exception
     */
    protected function check_access_for_dynamic_submission(): void {
        global $USER, $DB;

        if (!isloggedin() || isguestuser()) {
            throw new moodle_exception('requireloginerror', 'moodle'); 
        }

        $offeruserenrolid = $this->optional_param('offeruserenrolid', 0, PARAM_INT);

        if (!$offeruserenrolid) {
             throw new moodle_exception('invalidparameter', 'debug');
        }

        $offeruserenrol = offer_user_enrol_model::get_record(['id' => $offeruserenrolid, 'userid' => $USER->id]);

        $pluginname = $offeruserenrol->get('enrol');

        $offerclass = $offeruserenrol->get_offer_class();
        $offerclass_context = context_offer_class::instance($offerclass->get('id'));

        // Use the specific capability for self-enrolment.
        require_capability("enrol/{$pluginname}:enrolself", $offerclass_context);
    }

    /**
     * Load in existing data as form defaults.
     *
     * @return void
     * @throws dml_exception
     * @throws moodle_exception
     */
    public function set_data_for_dynamic_submission(): void {
        global $USER;

        $offeruserenrolid = $this->optional_param('offeruserenrolid', 0, PARAM_INT);
        if (!$offeruserenrolid) {
            throw new moodle_exception('invalidparameter', 'debug');
        }

        $offer_user_enrol = offer_user_enrol_model::get_record(['id' => $offeruserenrolid, 'userid' => $USER->id]);
        if (!$offer_user_enrol) {
            throw new moodle_exception('error:invalid_offer_user_enrol', 'local_offermanager');
        }

        $course = $offer_user_enrol->get_course();

        $fields = new stdClass;
        $fields->id = $offeruserenrolid;
        $this->set_data($fields);
    }

    /**
     * Get the page URL for dynamic submission.
     *
     * @return moodle_url The page URL.
     */
    protected function get_page_url_for_dynamic_submission(): moodle_url {
        // Redirect back to the user's dashboard or course page after re-enrolment.
        // Adjust as needed.
        return new moodle_url('/my/courses.php');
    }

    /**
     * Process dynamic form submission.
     *
     * @return array Result array containing success status.
     * @throws moodle_exception
     * @throws dml_exception
     */
    public function process_dynamic_submission(): array {
        global $USER;

        $data = $this->get_data();
        $offeruserenrolid = (int) $data->id;

        if (!$offeruserenrolid) {
            throw new moodle_exception('missingparam', 'error', 'offeruserenrolid');
        }

        $offer_user_enrol = offer_user_enrol_model::get_record(['id' => $offeruserenrolid, 'userid' => $USER->id]);

        if (!$offer_user_enrol) {
            throw new moodle_exception('error:invalid_offer_user_enrol', 'local_offermanager');
        }

        $success = $offer_user_enrol->process_reenrolment();

        if (!$success) {
             throw new moodle_exception('error:cannot_reenrol', 'local_offermanager');
        }

        return ['result' => $success];
    }
}
