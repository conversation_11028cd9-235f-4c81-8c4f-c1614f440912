<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\event;

use context_system;
use local_offermanager\persistent\offer_model;

/**
 * Event offer_created
 *
 * @package    local_offermanager
 * @copyright  2025 2015 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class offer_created extends \core\event\base
{
    /**
     * Set basic properties for the event.
     */
    protected function init()
    {
        $this->data['objecttable'] = 'local_offermanager';
        $this->data['crud'] = 'c';
        $this->data['edulevel'] = self::LEVEL_OTHER;
    }

    /**
     * Método estático para criar uma instância do evento.
     *
     * @param offer_model $offer.
     * @return self
     */
    public static function instance(offer_model $offer)
    {
        $data = [
            'objectid' => $offer->get('id'),
            'context' => context_system::instance()
        ];

        $event = self::create($data);
        return $event;
    }

    public static function get_name()
    {
        return get_string('event:offercreated', 'local_offermanager');
    }

    public function get_description()
    {
        return "O usuário com id '{$this->userid}' criou a oferta com id '{$this->objectid}'.";
    }
}
