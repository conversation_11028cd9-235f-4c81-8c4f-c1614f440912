<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\event;

use core\event\base;
use local_offermanager\persistent\offer_class_model;
use context_offer_class;

defined('MOODLE_INTERNAL') || die();

/**
 * Event triggered when an offer class operational cycle starts.
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class offer_class_cycle_started extends base {

    /**
     * Init method.
     */
    protected function init() {
        $this->data['objecttable'] = offer_class_model::TABLE;
        $this->data['crud'] = 'u';
        $this->data['edulevel'] = self::LEVEL_PARTICIPATING;
    }

    /**
     * Returns the event name.
     *
     * @return string Event name.
     */
    public static function get_name() {
        return get_string('event:offerclasscyclestarted', 'local_offermanager');
    }

    /**
     * Returns the event description.
     *
     * @return string Event description.
     */
    public function get_description() {
        return "O ciclo operacional para a turma com id '{$this->objectid}' foi iniciado.";
    }

    /**
     * Returns the URL related to the event.
     *
     * @return \moodle_url|null URL or null if not applicable.
     */
    public function get_url() {
        // try {
        //     $offerclass = offer_class_model::get_record(['id' => $this->objectid]);
        //     if ($offerclass) {
        //         $course = $offerclass->get_course();
        //         return new \moodle_url('/course/view.php', ['id' => $course->id]);
        //     }
        // } catch (\moodle_exception $e) {
        //     // Fallback if course cannot be retrieved.
        // }
        return null;
    }

    /**
     * Returns the context instance.
     *
     * @return \context The context.
     */
    public function get_context() {
        if (!isset($this->context)) {
            $this->context = context_offer_class::instance($this->objectid);
        }
        return $this->context;
    }

    /**
     * Factory method to create the event.
     *
     * @param offer_class_model $offerclass The offer class instance.
     * @return self
     */
    public static function create_from_instance(offer_class_model $offerclass): self {
        global $USER;

        return self::create([
            'context' => context_offer_class::instance($offerclass->get('id')),
            'objectid' => $offerclass->get('id'),
            'relateduserid' => $offerclass->get('usercreated'),
            'userid' => $USER->id
        ]);
    }
}