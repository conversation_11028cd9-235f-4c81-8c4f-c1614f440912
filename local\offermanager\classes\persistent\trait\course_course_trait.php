<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\persistent\trait;

use context_course;
use core_course_category;

defined('MOODLE_INTERNAL') || die();

/**
 * Trait course_course_trait
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
trait course_course_trait
{
    /**
     * <PERSON><PERSON>na todas as categorias disponíveis.
     *
     * @param string $search_string String de busca para filtrar categorias.
     * @return array
     */
    public static function get_all_categories(string $search_string = '')
    {
        global $DB;

        $params = [];
        $select = 'visible = 1';

        if ($search_string) {
            $select .= ' AND ' . $DB->sql_like('name', ':search_string', false, false);
            $params['search_string'] = "%$search_string%";
        }

        return array_values($DB->get_records_select(
            'course_categories',
            $select,
            $params,
            'id',
            'id, name'
        ));
    }

    /**
     * Retorna o curso associado a este registro.
     *
     * @return object
     */
    public function get_course()
    {
        $courseid = $this->get('courseid');
        if (!$courseid) {
            return false;
        }
        return get_course($courseid);
    }

    /**
     * Retorna o campo do curso associado a este registro.
     *
     * @return object
     */
    public function get_course_field($field = 'id')
    {
        $course = $this->get_course();

        return $course->{$field} ?? null;
    }

    /**
     * Retorna a categoria do curso associado a este registro.
     *
     * @return core_course_category
     */
    public function get_course_category(): core_course_category
    {
        $course = $this->get_course();

        return core_course_category::get($course->category, MUST_EXIST, true);
    }

    /**
     * Retorna a lista de papéis disponíveis no curso.
     *
     * @return array Lista de papéis com id e nome
     */
    public function get_course_roles(): array
    {
        $course = $this->get_course();
        $coursecontext = context_course::instance($course->id);

        return get_assignable_roles($coursecontext);
    }

    /**
     * Verifica se o usuário possui alguma atividade registrada no curso.
     *
     * @param int $userid ID do usuário.
     * @param int $courseid ID do curso.
     * @return bool True se o usuário possui atividade, false caso contrário.
     */
    public function user_has_activity(int $userid, int $courseid): bool
    {
        global $DB;

        $hascompletion = $DB->record_exists('course_completions', ['userid' => $userid, 'course' => $courseid]);
        if ($hascompletion) {
            return true;
        }

        $hasmodcompletion = $DB->record_exists_sql(
            "SELECT 1
            FROM {course_modules_completion} cmc
                JOIN {course_modules} cm ON (cmc.coursemoduleid = cm.id)
            WHERE cmc.userid = :userid AND cm.course = :course
            ",
            [
                'userid' => $userid,
                'course' => $courseid
            ]
        );

        if ($hasmodcompletion) {
            return true;
        }

        $hasgrade = $DB->record_exists_sql(
            "SELECT gg.id
            FROM {grade_grades} gg
            JOIN {grade_items} gi ON (gi.id = gg.itemid)
            WHERE gg.userid = :userid
            AND gi.courseid = :courseid
            ",
            [
                'userid' => $userid,
                'courseid' => $courseid
            ]
        );

        if ($hasgrade) {
            return true;
        }

        return false;
    }
}
