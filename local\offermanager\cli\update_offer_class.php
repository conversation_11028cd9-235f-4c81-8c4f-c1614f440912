<?php
define('CLI_SCRIPT', true);

require(__DIR__ . '/../../../config.php');
require_once($CFG->libdir.'/clilib.php');

use local_offermanager\external\offer_class_external;

$longoptions = [
    'offerclassid' => false,
    'classname' => false,
    'startdate' => false,
    'teachers' => false,
    'optional_fields' => false,
    'help' => false
];

$shortoptions = [
    'i' => 'offerclassid',
    'n' => 'classname',
    's' => 'startdate',
    't' => 'teachers',
    'o' => 'optional_fields',
    'h' => 'help'
];

list($options, $unrecognized) = cli_get_params($longoptions, $shortoptions);

if (!empty($options['help'])) {
    echo "Atualiza uma turma vinculada a uma oferta de curso\n";
    echo "Uso: php update_offer_class.php [opções]\n";
    echo "Parâmetros obrigatórios:\n";
    echo "  -i --offerclassid=ID          ID da turma\n";
    echo "  -n --classname=NOME           Nome da turma\n";
    echo "Parâmetros opcionais:\n";
    echo "  -s --startdate=TIMESTAMP      Data de início (timestamp UNIX). Padrão: agora\n";
    echo "  -t --teachers=JSON_ARRAY      IDs dos professores, ex: '[1,2,3]'\n";
    echo "  -o --optional_fields=JSON_OBJ Campos opcionais em JSON\n";
    echo "  -h --help                     Exibe esta ajuda\n";
    echo "Exemplo:\n";
    echo "  php update_offer_class.php --offerclassid=10 --classname='Turma Atualizada'\n";
    exit(0);
}

$required = ['offerclassid'];
foreach ($required as $param) {
    if (empty($options[$param])) {
        cli_error("Parâmetro obrigatório ausente: --$param");
    }
}

$offerclassid = (int )$options['offerclassid'];
$classname = $options['classname'] ?? null;
$startdate = isset($options['startdate']) && is_numeric($options['startdate']) ? (int)$options['startdate'] : null;

$teachers = [];
if (!empty($options['teachers'])) {
    $decoded = json_decode($options['teachers'], true);
    if (!is_array($decoded)) {
        cli_error("Formato inválido para --teachers. Use JSON array, ex: '[1,2]'");
    }
    $teachers = $decoded;
}

$optional_fields = [];
if (!empty($options['optional_fields'])) {
    $decoded = json_decode($options['optional_fields'], true);
    if (!is_array($decoded)) {
        cli_error("Formato inválido para --optional_fields. Use JSON object");
    }
    $optional_fields = $decoded;
}

try {
    $result = offer_class_external::update(
        $offerclassid,
        $classname,
        $startdate,
        $teachers,
        $optional_fields
    );
    cli_writeln("Turma atualizada com sucesso: $result");
} catch (Exception $e) {
    cli_error('Erro ao atualizar turma: ' . $e->getMessage());
}