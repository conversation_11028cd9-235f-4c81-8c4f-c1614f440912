<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\classes\persistent;

use local_offermanager\persistent\offer_model;
use local_offermanager\constants;
use local_offermanager\persistent\offer_course_model;
use local_offermanager\persistent\offer_audience_model;
use local_offermanager\event\offer_activated;
use local_offermanager\event\offer_updated;
use local_offermanager\event\offer_inactivated;
use local_offermanager\enrol_setup;
use moodle_exception;
use stdClass;

/**
 * Tests for Offer Manager
 *
 * @package    local_offermanager
 * @category   test
 * @copyright  2025 2015 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
final class offer_model_test extends \advanced_testcase
{
    /**
     * Testa a criação de uma oferta.
     */
    public function test_create_offer()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta de Teste',
            'description' => 'Descrição da oferta de teste',
            'type' => 'online',
            'status' => constants::OFFER_STATUS_INACTIVE,
        ]);

        $offer->save();

        $this->assertNotEmpty($offer->get('id'));

        $this->assertEquals('Oferta de Teste', $offer->get('name'));
        $this->assertEquals('Descrição da oferta de teste', $offer->get('description'));
        $this->assertEquals('online', $offer->get('type'));
        $this->assertEquals(constants::OFFER_STATUS_INACTIVE, $offer->get('status'));
    }

    /**
     * Testa a atualização de uma oferta.
     */
    public function test_update_offer()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta de Teste',
            'description' => 'Descrição da oferta de teste',
            'type' => 'online',
            'status' => constants::OFFER_STATUS_INACTIVE,
        ]);
        $offer->save();

        $offer->set('name', 'Oferta Atualizada');
        $offer->set('description', 'Descrição atualizada');
        $offer->set('type', 'presencial');

        $offer->save();

        $offer->read();

        $this->assertEquals('Oferta Atualizada', $offer->get('name'));
        $this->assertEquals('Descrição atualizada', $offer->get('description'));
        $this->assertEquals('presencial', $offer->get('type'));
        $this->assertEquals(constants::OFFER_STATUS_INACTIVE, $offer->get('status'));
    }


    /**
     * Testa a validação de name quando sendo criado.
     */
    public function test_create_name_validation()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) []);

        $this->expectExceptionMessage(get_string('error:offer_name_required', 'local_offermanager'));

        $offer->save();
    }

    /**
     * Testa o evento de criação de oferta.
     */
    public function test_offer_created_event()
    {
        $this->resetAfterTest(true);

        $sink = $this->redirectEvents();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta de Teste',
            'description' => 'Descrição da oferta de teste',
            'type' => 'online',
            'status' => constants::OFFER_STATUS_INACTIVE,
        ]);
        $offer->save();

        $events = $sink->get_events();
        $sink->close();
        $this->assertCount(1, $events);
        $this->assertInstanceOf('\local_offermanager\event\offer_created', $events[0]);
    }

    /**
     * Testa o evento de atualização de oferta.
     */
    public function test_offer_updated_event()
    {
        $this->resetAfterTest(true);
        $sink = $this->redirectEvents();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta de Teste',
            'description' => 'Descrição da oferta de teste',
            'type' => 'online',
            'status' => constants::OFFER_STATUS_INACTIVE,
        ]);
        $offer->save();

        $offer->set('name', 'Oferta Atualizada');
        $offer->save();

        $events = $sink->get_events();
        $sink->close();
        $this->assertCount(2, $events);
        $this->assertInstanceOf('\local_offermanager\event\offer_updated', $events[1]);
    }


    /**
     * Testa o método fetch_types sem uma string de busca.
     */
    public function test_fetch_types_without_search_string()
    {
        $this->resetAfterTest(true);

        $offer1 = new offer_model(0, (object) [
            'name' => 'Oferta 1',
            'type' => 'online',
            'status' => constants::OFFER_STATUS_INACTIVE,
        ]);
        $offer1->save();

        $offer2 = new offer_model(0, (object) [
            'name' => 'Oferta 2',
            'type' => 'presencial',
            'status' => constants::OFFER_STATUS_INACTIVE,
        ]);

        $offer2->save();

        $offer3 = new offer_model(0, (object) [
            'name' => 'Oferta 3',
            'type' => 'online',
            'status' => constants::OFFER_STATUS_INACTIVE,
        ]);

        $offer3->save();

        $types = offer_model::fetch_types();

        $this->assertCount(2, $types);
        $this->assertContains('online', $types);
        $this->assertContains('presencial', $types);
    }

    /**
     * Testa o método get_audiences.
     */
    public function test_get_audiences()
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta de Teste',
            'description' => 'Descrição da oferta de teste',
            'type' => 'online',
            'status' => constants::OFFER_STATUS_INACTIVE,
        ]);

        $offer->save();

        $audienceid_1 = $DB->insert_record(
            'local_audience_audiences',
            [
                'name' => 'Público-alvo 1'
            ]
        );

        $audienceid_2 = $DB->insert_record(
            'local_audience_audiences',
            [
                'name' => 'Público-alvo 2'
            ]
        );

        $offer->add_audience($audienceid_1);
        $offer->add_audience($audienceid_2);

        $audiences = $offer->get_audiences();

        $this->assertCount(2, $audiences);
        $this->assertEquals($audienceid_1, $audiences[0]->get('audienceid'));
        $this->assertEquals($audienceid_2, $audiences[1]->get('audienceid'));
    }

    /**
     * Testa o método get_audience.
     */
    public function test_get_audience(): void
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta de Teste'
        ]);
        $offer->save();

        $audienceid_1 = $DB->insert_record('local_audience_audiences', [
            'name' => 'Público-alvo 1',
        ]);

        $offer->add_audience($audienceid_1);

        $offerAudience = $offer->get_audience($audienceid_1);

        $this->assertInstanceOf(offer_audience_model::class, $offerAudience);
        $this->assertEquals($offer->get('id'), $offerAudience->get('offerid'));
        $this->assertEquals($audienceid_1, $offerAudience->get('audienceid'));

        $nonExistentAudienceId = 999;
        $this->assertFalse($offer->get_audience($nonExistentAudienceId));
    }

    /**
     * Testa o método get_audience_ids.
     */
    public function test_get_audience_ids(): void
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta de Teste'
        ]);
        $offer->save();

        $audienceid_1 = $DB->insert_record('local_audience_audiences', [
            'name' => 'Público-alvo 1',
        ]);
        $audienceid_2 = $DB->insert_record('local_audience_audiences', [
            'name' => 'Público-alvo 2',
        ]);

        $offer->add_audience($audienceid_1);
        $offer->add_audience($audienceid_2);

        $audienceIds = $offer->get_audience_ids();

        $this->assertCount(2, $audienceIds);
        $this->assertContains($audienceid_1, $audienceIds);
        $this->assertContains($audienceid_2, $audienceIds);
    }

    /**
     * Testa o método update_audiences.
     */
    public function test_update_audiences(): void
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta de Teste'
        ]);
        $offer->save();

        $audienceid_1 = $DB->insert_record('local_audience_audiences', [
            'name' => 'Público-alvo 1',
        ]);
        $audienceid_2 = $DB->insert_record('local_audience_audiences', [
            'name' => 'Público-alvo 2',
        ]);
        $audienceid_3 = $DB->insert_record('local_audience_audiences', [
            'name' => 'Público-alvo 3',
        ]);

        $offer->add_audience($audienceid_1);
        $offer->add_audience($audienceid_2);

        $audienceIds = $offer->get_audience_ids();

        $this->assertCount(2, $audienceIds);
        $this->assertContains($audienceid_1, $audienceIds);
        $this->assertContains($audienceid_2, $audienceIds);
        $this->assertNotContains($audienceid_3, $audienceIds);

        $offer->update_audiences([$audienceid_2, $audienceid_3]);

        $updatedAudienceIds = $offer->get_audience_ids();

        $this->assertCount(2, $updatedAudienceIds);
        $this->assertContains($audienceid_2, $updatedAudienceIds);
        $this->assertContains($audienceid_3, $updatedAudienceIds);
        $this->assertNotContains($audienceid_1, $updatedAudienceIds);
    }

    /**
     * Testa o método update_audiences com uma lista vazia.
     */
    public function test_update_audiences_empty_list(): void
    {
        global $DB;

        $this->resetAfterTest(true);

        // Cria uma oferta.
        $offer = new offer_model(0, (object) [
            'name' => 'Oferta de Teste',
            'description' => 'Descrição da oferta de teste',
            'type' => 'online',
            'status' => constants::OFFER_STATUS_INACTIVE,
        ]);
        $offer->save();

        // Cria um público-alvo.
        $audienceid_1 = $DB->insert_record('local_audience_audiences', [
            'name' => 'Público-alvo 1',
        ]);

        // Adiciona o público-alvo à oferta.
        $offer->add_audience($audienceid_1);

        // Atualiza a lista de públicos-alvo para uma lista vazia.
        $offer->update_audiences([]);

        // Obtém os IDs dos públicos-alvo após a atualização.
        $updatedAudienceIds = $offer->get_audience_ids();

        // Verifica se todos os públicos-alvo foram removidos.
        $this->assertEmpty($updatedAudienceIds);
    }

    /**
     * Testa a adição de um público-alvo à oferta.
     */
    public function test_add_audience_success(): void
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta de Teste'
        ]);
        $offer->save();

        $audienceid_1 = $DB->insert_record('local_audience_audiences', [
            'name' => 'Público-alvo 1',
        ]);

        $offerAudience = $offer->add_audience($audienceid_1);

        $this->assertNotEmpty($offerAudience->get('id'));
        $this->assertEquals($offer->get('id'), $offerAudience->get('offerid'));
        $this->assertEquals($audienceid_1, $offerAudience->get('audienceid'));

        $this->assertTrue(offer_audience_model::record_exists($offerAudience->get('id')));
    }

    /**
     * Testa a tentativa de adicionar um público-alvo duplicado à oferta.
     */
    public function test_add_audience_duplicate(): void
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta de Teste',
        ]);
        $offer->save();

        $audienceid_1 = $DB->insert_record('local_audience_audiences', [
            'name' => 'Público-alvo 1',
        ]);

        $offer->add_audience($audienceid_1);

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:duplicate_offer_audience', 'local_offermanager'));
        $offer->add_audience($audienceid_1);
    }

    /**
     * Testa a tentativa de adicionar um público-alvo inválido à oferta.
     */
    public function test_add_audience_invalid(): void
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta de Teste'
        ]);
        $offer->save();

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:audience_not_found', 'local_offermanager'));
        $offer->add_audience(999);
    }

    /**
     * Testa o método remove_audience.
     */
    public function test_remove_audience()
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta de Teste',
            'description' => 'Descrição da oferta de teste',
            'type' => 'online',
            'status' => constants::OFFER_STATUS_INACTIVE,
        ]);
        $offer->save();

        $audienceid_1 = $DB->insert_record(
            'local_audience_audiences',
            [
                'name' => 'Público-alvo 1'
            ]
        );

        $offer->add_audience($audienceid_1);

        $result = $offer->remove_audience($audienceid_1);

        $this->assertTrue($result);

        $audiences = $offer->get_audiences();
        $this->assertCount(0, $audiences);
    }

    /**
     * Testa a remoção de um público-alvo que não existe.
     */
    public function test_remove_nonexistent_audience()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta de Teste',
            'description' => 'Descrição da oferta de teste',
            'type' => 'online',
            'status' => constants::OFFER_STATUS_INACTIVE,
        ]);
        $offer->save();

        $this->expectExceptionMessage(get_string('error:offer_audience_not_found', 'local_offermanager'));
        $offer->remove_audience(999); // ID que não existe.
    }



    /**
     * Testa a vinculação de um curso a uma oferta.
     */
    public function test_add_course_and_other_related_functions(): void
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $courses = $offer->get_courses();

        $this->assertEmpty($courses);
        $this->assertFalse($offer->has_courses());

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $this->assertInstanceOf(offer_course_model::class, $offer_course);
        $this->assertEquals($course->id, $offer_course->get('courseid'));

        $courses = $offer->get_courses();

        $this->assertCount(1, $courses);

        $course = $courses[0];
        $this->assertInstanceOf(offer_course_model::class, $offer_course);
        $this->assertTrue($offer->has_courses());
    }

    /**
     * Testa a remover avinculação entre um curso e uma oferta.
     */
    public function test_remove_course(): void
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $courses = $offer->get_courses();

        $this->assertEmpty($courses);
        $this->assertFalse($offer->has_courses());

        $course = $this->getDataGenerator()->create_course();
        $other_course = $this->getDataGenerator()->create_course();
        $offer_course = $offer->add_course($course->id);
        $other_offer_course = $offer->add_course($other_course->id);

        $courses = $offer->get_courses();

        $this->assertCount(2, $courses);

        $other_offer_course->delete();

        $courses = $offer->get_courses();

        $this->assertCount(1, $courses);
    }

    /**
     * Testa a exclusão de uma oferta.
     */
    public function test_delete_offer()
    {
        $this->resetAfterTest(true);

        $this->setAdminUser();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta de Teste'
        ]);

        $offer->save();


        $offerid = $offer->get('id');
        $offer->delete();

        $this->expectException(\dml_missing_record_exception::class);
        new offer_model($offerid);
    }

    /**
     * Testa a exclusão bem-sucedida de uma oferta.
     */
    public function test_delete_success(): void
    {
        $this->resetAfterTest(true);
        $this->setAdminUser();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);
        $offer->save();

        $course = $this->getDataGenerator()->create_course();
        $offer_course = $offer->add_course($course->id);

        $this->assertTrue($offer->can_delete());
        $offer->delete();
        $this->assertFalse(offer_model::record_exists($offer->get('id')));
    }

    /**
     * Testa a possibilidade na exclusão de uma oferta com instâncias de inscrição.
     */
    public function test_delete_sucess_with_only_enrol_instances(): void
    {
        global $DB;

        $this->resetAfterTest(true);
        $this->setAdminUser();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste',
            'status' => constants::OFFER_STATUS_INACTIVE,
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offer_course->add_class($enrol_instance->id);

        $this->assertTrue($offer->can_delete());
    }

    /**
     * Testa a falha na exclusão de uma oferta com matrículas de usuários.
     */
    public function test_delete_failure_with_user_enrolments(): void
    {
        global $DB;

        $this->resetAfterTest(true);
        $this->setAdminUser();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offerclass = $offer_course->add_class($enrol_instance->id);

        $user = $this->getDataGenerator()->create_user();
        $this->getDataGenerator()->enrol_user($user->id, $course->id, $$offerclass->get_mapped_field('roleid'), $enrol_instance->enrol);

        $this->assertFalse($offer->can_delete());

        $this->expectException(\moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:cannot_delete_offer', 'local_offermanager'));
        $offer->delete();
    }

    /**
     * Testa o evento de exclusão de oferta.
     */
    public function test_offer_deleted_event()
    {
        $this->resetAfterTest(true);
        $this->setAdminUser();

        $sink = $this->redirectEvents();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta de Teste',
            'description' => 'Descrição da oferta de teste',
            'type' => 'online',
            'status' => constants::OFFER_STATUS_INACTIVE,
        ]);
        $offer->save();

        $offer->delete();

        $events = $sink->get_events();
        $sink->close();
        $this->assertCount(2, $events);
        $this->assertInstanceOf('\local_offermanager\event\offer_deleted', $events[1]);
    }

    /**
     * Testa a ativação bem-sucedida de uma oferta.
     */
    public function test_activate_success(): void
    {
        global $DB;

        $this->resetAfterTest(true);
        $this->setAdminUser();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $audienceid = $DB->insert_record(
            'local_audience_audiences',
            [
                'name' => 'Público-alvo 1'
            ]
        );

        $offer->add_audience($audienceid);

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offer_course->add_class($enrol_instance->id);

        $this->assertTrue($offer->activate());
        $this->assertEquals(constants::OFFER_STATUS_ACTIVE, $offer->get('status'));
    }

    /**
     * Testa a falha na ativação de uma oferta sem turmas configuradas.
     */
    public function test_activate_failure_no_classes(): void
    {
        global $DB;

        $this->resetAfterTest(true);
        $this->setAdminUser();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $audienceid = $DB->insert_record(
            'local_audience_audiences',
            [
                'name' => 'Público-alvo 1'
            ]
        );

        $offer->add_audience($audienceid);

        $course = $this->getDataGenerator()->create_course();

        $offer->add_course($course->id);

        $this->expectException(\moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:cannot_activate_offer', 'local_offermanager'));
        $offer->activate();
    }

    /**
     * Testa a falha na ativação de uma oferta sem público-alvo.
     */
    public function test_activate_failure_no_audience(): void
    {
        global $DB;

        $this->resetAfterTest(true);
        $this->setAdminUser();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offer_course->add_class($enrol_instance->id);

        // Tenta ativar a oferta (deve falhar porque não há público-alvo).
        $this->expectException(\moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:cannot_activate_offer', 'local_offermanager'));
        $offer->activate();
    }

    /**
     * Testa a falha na ativação de uma oferta já ativa.
     */
    public function test_activate_failure_already_active(): void
    {
        global $DB;

        $this->resetAfterTest(true);
        $this->setAdminUser();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $audienceid = $DB->insert_record(
            'local_audience_audiences',
            [
                'name' => 'Público-alvo 1'
            ]
        );

        $offer->add_audience($audienceid);

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offer_course->add_class($enrol_instance->id);

        $this->assertTrue($offer->activate());

        $this->expectException(\moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:offer_already_active', 'local_offermanager'));
        $offer->activate();
    }

    /**
     * Testa a inativação bem-sucedida de uma oferta.
     */
    public function test_inactivate_success(): void
    {
        global $DB;

        $this->resetAfterTest(true);
        $this->setAdminUser();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $audienceid = $DB->insert_record(
            'local_audience_audiences',
            [
                'name' => 'Público-alvo 1'
            ]
        );

        $offer->add_audience($audienceid);

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offer_course->add_class($enrol_instance->id);

        $this->assertTrue($offer->activate());

        $this->assertEquals(constants::OFFER_STATUS_ACTIVE, $offer->get('status'));

        $this->assertTrue($offer->inactivate());

        $this->assertEquals(constants::OFFER_STATUS_INACTIVE, $offer->get('status'));
    }

    /**
     * Testa a falha na inativação de uma oferta já inativa.
     */
    public function test_inactivate_failure_because_already_inactive(): void
    {
        $this->resetAfterTest(true);
        $this->setAdminUser();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $this->expectException(\moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:offer_already_inactive', 'local_offermanager'));
        $offer->inactivate();
    }


    public function test_status_change_from_inactive_to_active_triggers_offer_activated()
    {
        global $DB;

        $this->resetAfterTest(true);
        $this->setAdminUser();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $audienceid = $DB->insert_record(
            'local_audience_audiences',
            [
                'name' => 'Público-alvo 1'
            ]
        );

        $offer->add_audience($audienceid);

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offer_course->add_class($enrol_instance->id);

        $sink = $this->redirectEvents();
        $offer->activate();
        $events = $sink->get_events();
        $sink->close();

        $this->assertCount(2, $events);
        $activate_event = $events[0];
        $update_event = $events[1];
        $this->assertInstanceOf(offer_activated::class, $activate_event);
        $this->assertInstanceOf(offer_updated::class, $update_event);
    }

    public function test_status_change_from_active_to_inactive_triggers_offer_inactivated()
    {
        global $DB;

        $this->resetAfterTest(true);
        $this->setAdminUser();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);

        $offer->save();

        $audienceid = $DB->insert_record(
            'local_audience_audiences',
            [
                'name' => 'Público-alvo 1'
            ]
        );

        $offer->add_audience($audienceid);

        $course = $this->getDataGenerator()->create_course();

        $offer_course = $offer->add_course($course->id);

        $enrol_instance =  $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => 'manual'), '*', MUST_EXIST);

        $offer_course->add_class($enrol_instance->id);
        $offer->activate();

        $sink = $this->redirectEvents();
        $offer->inactivate();
        $events = $sink->get_events();
        $sink->close();

        $this->assertCount(2, $events);
        $activate_event = $events[0];
        $update_event = $events[1];
        $this->assertInstanceOf(offer_inactivated::class, $activate_event);
        $this->assertInstanceOf(offer_updated::class, $update_event);
    }

    public function test_no_status_change_does_not_trigger_status_events()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Test Offer'
        ]);
        $offer->save();

        $offer->set('description', 'Updated description');

        $sink = $this->redirectEvents();
        $offer->save();
        $events = $sink->get_events();
        $sink->close();

        $this->assertCount(1, $events);
        $this->assertInstanceOf(offer_updated::class, $events[0]);
    }

    /**
     * Testa o método fetch_types com uma string de busca.
     */
    public function test_fetch_types_with_search_string()
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer1 = new offer_model(0, (object) [
            'name' => 'Oferta 1',
            'type' => 'online',
            'status' => constants::OFFER_STATUS_INACTIVE,
        ]);

        $offer1->save();

        $offer2 = new offer_model(0, (object) [
            'name' => 'Oferta 11',
            'type' => 'online',
            'status' => constants::OFFER_STATUS_INACTIVE,
        ]);

        $offer2->save();

        $obj = new stdClass;
        $obj->id = $offer2->get('id');
        $obj->status = constants::OFFER_STATUS_ACTIVE;
        $DB->update_record(offer_model::TABLE, $obj);

        $offer3 = new offer_model(0, (object) [
            'name' => 'Oferta 2',
            'type' => 'presencial',
            'status' => constants::OFFER_STATUS_INACTIVE,
        ]);

        $offer3->save();
        $obj->id = $offer3->get('id');
        $DB->update_record(offer_model::TABLE, $obj);

        $offer4 = new offer_model(0, (object) [
            'name' => 'Oferta 22',
            'type' => 'online',
            'status' => constants::OFFER_STATUS_INACTIVE,
        ]);

        $offer4->save();

        $types = offer_model::fetch_types('Oferta 1');

        $this->assertCount(1, $types);
        $this->assertContains('online', $types);
        $this->assertNotContains('presencial', $types);

        $types = offer_model::fetch_types('Oferta 2');

        $this->assertCount(2, $types);
        $this->assertContains('online', $types);
        $this->assertContains('presencial', $types);
    }

    /**
     * Testa o método fetch_types com uma string de busca e só ativos.
     */
    public function test_fetch_types_with_search_string_only_actives()
    {
        global $DB;

        $this->resetAfterTest(true);

        $offer1 = new offer_model(0, (object) [
            'name' => 'Oferta 1',
            'type' => 'online',
            'status' => constants::OFFER_STATUS_INACTIVE,
        ]);

        $offer1->save();

        $offer2 = new offer_model(0, (object) [
            'name' => 'Oferta 11',
            'type' => 'online',
            'status' => constants::OFFER_STATUS_INACTIVE,
        ]);

        $offer2->save();

        $obj = new stdClass;
        $obj->id = $offer2->get('id');
        $obj->status = constants::OFFER_STATUS_ACTIVE;
        $DB->update_record(offer_model::TABLE, $obj);

        $offer3 = new offer_model(0, (object) [
            'name' => 'Oferta 2',
            'type' => 'presencial',
            'status' => constants::OFFER_STATUS_INACTIVE,
        ]);

        $offer3->save();

        $obj->id = $offer3->get('id');
        $DB->update_record(offer_model::TABLE, $obj);

        $offer4 = new offer_model(0, (object) [
            'name' => 'Oferta 22',
            'type' => 'online',
            'status' => constants::OFFER_STATUS_INACTIVE,
        ]);

        $offer4->save();

        $types = offer_model::fetch_types('Oferta 1', true);

        $this->assertCount(1, $types);
        $this->assertContains('online', $types);
        $this->assertNotContains('presencial', $types);

        $types = offer_model::fetch_types('Oferta 2', true);

        $this->assertCount(1, $types);
        $this->assertContains('presencial', $types);
        $this->assertNotContains('online', $types);
    }

    /**
     * Testa o método fetch_types quando não há ofertas cadastradas.
     */
    public function test_fetch_types_with_no_offers()
    {
        $this->resetAfterTest(true);

        $types = offer_model::fetch_types();

        $this->assertEmpty($types);
    }

    public function test_fetch_potential_courses()
    {
        $this->resetAfterTest(true);

        $category1 = $this->getDataGenerator()->create_category();
        $category2 = $this->getDataGenerator()->create_category();

        $course1 = $this->getDataGenerator()->create_course(['fullname' => 'Curso 1', 'category' => $category1->id]);
        $course2 = $this->getDataGenerator()->create_course(['fullname' => 'Curso 2', 'category' => $category2->id]);
        $course3 = $this->getDataGenerator()->create_course(['fullname' => 'Curso 3', 'category' => $category1->id]);

        $offer = new offer_model(0, (object) [
            'name' => 'Test Offer'
        ]);

        $offer->save();

        $offer->add_course($course1->id);

        $result = $offer->fetch_potential_courses();

        $this->assertCount(2, $result);
        $result0 = $result[0];
        $this->assertEquals($course2->id, $result0->id);
        $result1 = $result[1];
        $this->assertEquals($course3->id, $result1->id);

        $result = $offer->fetch_potential_courses($category1->id);
        $this->assertCount(1, $result);
        $result = $result[0];

        $this->assertEquals($course3->id, $result->id);

        $result = $offer->fetch_potential_courses(0, 'Curso 2');
        $this->assertCount(1, $result);
        $result = $result[0];
        $this->assertEquals($course2->id, $result->id);

        $result = $offer->fetch_potential_courses(0, '', [$course2->id]);
        $this->assertCount(1, $result);
        $result = $result[0];
        $this->assertEquals($course3->id, $result->id);

        $result = $offer->fetch_potential_courses(0, '', [], 0, 1);
        $this->assertCount(1, $result);
    }

    public function test_fetch_potential_courses_pagination()
    {
        $this->resetAfterTest(true);

        $size = 12;

        $offer = new offer_model(0, (object) [
            'name' => 'Test Offer'
        ]);
        $offer->save();

        for ($i = 1; $i <= $size + 3; $i++) {
            $course = $this->getDataGenerator()->create_course(
                [
                    'fullname' => "Curso $i"
                ]
            );

            if ($i > $size) {
                $offer->add_course($course->id);
            }
        }

        $result_page0 = $offer->fetch_potential_courses(0, '', [], 0, 5);
        $this->assertCount(5, $result_page0);

        $result_page1 = $offer->fetch_potential_courses(0, '', [], 1, 5);
        $this->assertCount(5, $result_page1);

        $result_page2 = $offer->fetch_potential_courses(0, '', [], 2, 5);
        $this->assertCount(2, $result_page2);

        $this->assertEmpty(array_intersect(array_column($result_page0, 'id'), array_column($result_page1, 'id')));
        $this->assertEmpty(array_intersect(array_column($result_page0, 'id'), array_column($result_page2, 'id')));
        $this->assertEmpty(array_intersect(array_column($result_page1, 'id'), array_column($result_page2, 'id')));
    }

    public function test_fetch_potential_courses_disregard_non_visible_courses()
    {
        $this->resetAfterTest(true);

        $category1 = $this->getDataGenerator()->create_category();

        $course1 = $this->getDataGenerator()->create_course(['fullname' => 'Curso 1', 'category' => $category1->id]);
        $course2 = $this->getDataGenerator()->create_course([
            'fullname' => 'Curso 2',
            'category' => $category1->id,
            'visible' => 0
        ]);

        $offer = new offer_model(0, (object) [
            'name' => 'Test Offer'
        ]);
        $offer->save();

        $result = $offer->fetch_potential_courses($category1->id);
        $this->assertCount(1, $result);
        $result = $result[0];
        $this->assertEquals($course1->id, $result->id);
    }

    public function test_fetch_potential_courses_with_category_filter()
    {
        $this->resetAfterTest(true);

        $category1 = $this->getDataGenerator()->create_category();
        $category2 = $this->getDataGenerator()->create_category();

        $course1 = $this->getDataGenerator()->create_course(['fullname' => 'Curso 1', 'category' => $category1->id]);
        $course2 = $this->getDataGenerator()->create_course(['fullname' => 'Curso 2', 'category' => $category2->id]);

        $offer = new offer_model(0, (object) [
            'name' => 'Test Offer'
        ]);
        $offer->save();

        $result = $offer->fetch_potential_courses($category1->id);
        $this->assertCount(1, $result);
        $result = $result[0];
        $this->assertEquals($course1->id, $result->id);
    }

    public function test_fetch_potential_courses_with_search_filter()
    {
        $this->resetAfterTest(true);

        $course1 = $this->getDataGenerator()->create_course(['fullname' => 'Curso de Matemática']);
        $course2 = $this->getDataGenerator()->create_course(['fullname' => 'Curso de Física']);

        $offer = new offer_model(0, (object) [
            'name' => 'Test Offer'
        ]);
        $offer->save();

        $result = $offer->fetch_potential_courses(0, 'Matemática');
        $this->assertCount(1, $result);
        $result = $result[0];
        $this->assertEquals($course1->id, $result->id);
    }

    public function test_fetch_potential_courses_with_exclude_courses()
    {
        $this->resetAfterTest(true);

        $course1 = $this->getDataGenerator()->create_course();
        $course2 = $this->getDataGenerator()->create_course();

        $offer = new offer_model(0, (object) [
            'name' => 'Test Offer'
        ]);
        $offer->save();

        $result = $offer->fetch_potential_courses(0, '', [$course1->id]);
        $this->assertCount(1, $result);
        $result = $result[0];
        $this->assertEquals($course2->id, $result->id);
    }

    public function test_fetch_potential_courses_with_case_insensitive_search()
    {
        $this->resetAfterTest(true);

        $course1 = $this->getDataGenerator()->create_course(['fullname' => 'Matemática Avançada']);
        $course2 = $this->getDataGenerator()->create_course(['fullname' => 'MATEMATICA BASICA']);

        $offer = new offer_model(0, (object) [
            'name' => 'Test Offer'
        ]);
        $offer->save();
        $result = $offer->fetch_potential_courses(0, 'matematica');
        $this->assertCount(2, $result);
    }

    public function test_fetch_potential_courses_with_accent_insensitive_search()
    {
        $this->resetAfterTest(true);

        $course1 = $this->getDataGenerator()->create_course(['fullname' => 'Matemática Avançada']);
        $course2 = $this->getDataGenerator()->create_course(['fullname' => 'Matematica Basica']);

        $offer = new offer_model(0, (object) [
            'name' => 'Test Offer'
        ]);
        $offer->save();

        $result = $offer->fetch_potential_courses(0, 'matematica');
        $this->assertCount(2, $result);
    }

    public function test_fetch_potential_courses_with_partial_search()
    {
        $this->resetAfterTest(true);

        // Cria cursos.
        $course1 = $this->getDataGenerator()->create_course(['fullname' => 'Matemática Avançada']);
        $course2 = $this->getDataGenerator()->create_course(['fullname' => 'Geometria Básica']);

        $offer = new offer_model(0, (object) [
            'name' => 'Test Offer'
        ]);
        $offer->save();

        $result = $offer->fetch_potential_courses(0, 'mat');
        $this->assertCount(1, $result);
        $result = $result[0];
        $this->assertEquals($course1->id, $result->id);
    }

    public function test_fetch_courses_with_all_parameters()
    {
        $this->resetAfterTest(true);

        $category = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 1']);
        $course2 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 2']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();
        $offer->add_course($course1->id);
        $offer->add_course($course2->id);

        $result = $offer->fetch_courses($category->id, 'Teste');

        $this->assertCount(2, $result);
        $this->assertEquals($course1->id, $result[0]->id);
        $this->assertEquals($course2->id, $result[1]->id);
    }

    public function test_fetch_courses_with_excluded_courses()
    {
        $this->resetAfterTest(true);

        $category = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 1']);
        $course2 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 2']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();
        $offer->add_course($course1->id);
        $offer->add_course($course2->id);

        $result = $offer->fetch_courses($category->id, 'Teste', [$course1->id]);

        $this->assertCount(1, $result);
        $this->assertEquals($course2->id, $result[0]->id);
    }

    public function test_fetch_courses_with_search_string()
    {
        $this->resetAfterTest(true);

        $category = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 1']);
        $course2 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Outro Curso']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();
        $offer->add_course($course1->id);
        $offer->add_course($course2->id);

        $result = $offer->fetch_courses($category->id, 'Teste');

        $this->assertCount(1, $result);
        $this->assertEquals($course1->id, $result[0]->id);
    }

    public function test_fetch_courses_with_category()
    {
        $this->resetAfterTest(true);

        $category1 = $this->getDataGenerator()->create_category();
        $category2 = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category1->id, 'fullname' => 'Curso de Teste 1']);
        $course2 = $this->getDataGenerator()->create_course(['category' => $category2->id, 'fullname' => 'Curso de Teste 2']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();
        $offer->add_course($course1->id);
        $offer->add_course($course2->id);

        $result = $offer->fetch_courses($category1->id);

        $this->assertCount(1, $result);
        $this->assertEquals($course1->id, $result[0]->id);
    }

    public function test_fetch_courses_with_no_matching_courses()
    {
        $this->resetAfterTest(true);

        $category = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 1']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();

        $result = $offer->fetch_courses($category->id, 'Teste');

        $this->assertEmpty($result);
    }

    public function test_fetch_courses_with_empty_parameters()
    {
        $this->resetAfterTest(true);

        $category = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 1']);
        $course2 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 2']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();
        $offer->add_course($course1->id);
        $offer->add_course($course2->id);

        $result = $offer->fetch_courses();

        $this->assertCount(2, $result);
    }

    public function test_fetch_courses_with_nonexistent_category()
    {
        $this->resetAfterTest(true);

        $category = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 1']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();
        $offer->add_course($course1->id);

        $result = $offer->fetch_courses(999);

        $this->assertEmpty($result);
    }

    public function test_fetch_courses_with_nonexistent_search_string()
    {
        $this->resetAfterTest(true);

        $category = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 1']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();
        $offer->add_course($course1->id);

        $result = $offer->fetch_courses($category->id, 'Nenhum Curso');

        $this->assertEmpty($result);
    }

    public function test_fetch_courses_with_all_courses_excluded()
    {
        $this->resetAfterTest(true);

        $category = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 1']);
        $course2 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 2']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();
        $offer->add_course($course1->id);
        $offer->add_course($course2->id);

        $result = $offer->fetch_courses($category->id, '', [$course1->id, $course2->id]);

        $this->assertEmpty($result);
    }

    public function test_fetch_courses_with_empty_exclude_list()
    {
        $this->resetAfterTest(true);

        $category = $this->getDataGenerator()->create_category();
        $course1 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 1']);
        $course2 = $this->getDataGenerator()->create_course(['category' => $category->id, 'fullname' => 'Curso de Teste 2']);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();
        $offer->add_course($course1->id);
        $offer->add_course($course2->id);

        $result = $offer->fetch_courses($category->id, '', []);

        $this->assertCount(2, $result);
    }

    public function test_get_courses_without_filters()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();

        $course1 = $this->getDataGenerator()->create_course();
        $course2 = $this->getDataGenerator()->create_course();

        $offer->add_course($course1->id);
        $offer->add_course($course2->id);

        $result = $offer->get_courses();

        $this->assertCount(2, $result);
        $this->assertEquals($course1->id, $result[0]->get('courseid'));
        $this->assertEquals($course2->id, $result[1]->get('courseid'));
    }

    public function test_get_courses_with_only_active()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();

        $course1 = $this->getDataGenerator()->create_course();
        $course2 = $this->getDataGenerator()->create_course();

        $offer->add_course($course1->id);
        $offer_course = $offer->add_course($course2->id);

        $offer_course->inactivate();

        $result = $offer->get_courses(true);

        $this->assertCount(1, $result);
        $this->assertEquals($course1->id, $result[0]->get('courseid'));
    }

    public function test_get_courses_with_specific_courseids()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();

        $course1 = $this->getDataGenerator()->create_course();
        $course2 = $this->getDataGenerator()->create_course();
        $course3 = $this->getDataGenerator()->create_course();

        $offer->add_course($course1->id);
        $offer->add_course($course2->id);
        $offer->add_course($course3->id);

        $result = $offer->get_courses(false, [$course1->id, $course3->id]);

        $this->assertCount(2, $result);
        $this->assertEquals($course1->id, $result[0]->get('courseid'));
        $this->assertEquals($course3->id, $result[1]->get('courseid'));
    }

    public function test_get_courses_with_pagination()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();

        $course1 = $this->getDataGenerator()->create_course();
        $course2 = $this->getDataGenerator()->create_course();
        $course3 = $this->getDataGenerator()->create_course();

        $offer->add_course($course1->id);
        $offer->add_course($course2->id);
        $offer->add_course($course3->id);

        $result_page1 = $offer->get_courses(false, [], 0, 2);
        $result_page2 = $offer->get_courses(false, [], 1, 2);

        $this->assertCount(2, $result_page1);
        $this->assertEquals($course1->id, $result_page1[0]->get('courseid'));
        $this->assertEquals($course2->id, $result_page1[1]->get('courseid'));

        $this->assertCount(1, $result_page2);
        $this->assertEquals($course3->id, $result_page2[0]->get('courseid'));
    }

    public function test_get_courses_with_invalid_courseids()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();

        $course1 = $this->getDataGenerator()->create_course();
        $offer->add_course($course1->id);

        $result = $offer->get_courses(false, [999, 1000]);

        $this->assertEmpty($result);
    }

    public function test_get_courses_with_pagination_and_insufficient_courses()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();

        $course1 = $this->getDataGenerator()->create_course();
        $offer->add_course($course1->id);

        $result = $offer->get_courses(false, [], 1, 2);

        $this->assertEmpty($result);
    }

    public function test_get_courses_with_per_page_zero()
    {
        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object)['name' => 'Oferta de Teste']);
        $offer->save();

        $course1 = $this->getDataGenerator()->create_course();
        $course2 = $this->getDataGenerator()->create_course();

        $offer->add_course($course1->id);
        $offer->add_course($course2->id);

        $result = $offer->get_courses();

        $this->assertCount(2, $result);
        $this->assertEquals($course1->id, $result[0]->get('courseid'));
        $this->assertEquals($course2->id, $result[1]->get('courseid'));

        $num = 50;

        for ($i = 0; $i < $num; $i++) {
            $course = $this->getDataGenerator()->create_course();

            $offer->add_course($course->id);
        }

        $result = $offer->get_courses();

        $this->assertCount($num + 2, $result);
    }
}
