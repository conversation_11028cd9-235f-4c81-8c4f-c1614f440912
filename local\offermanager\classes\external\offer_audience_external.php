<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\external;

use core_external\external_api;
use core_external\external_function_parameters;
use core_external\external_value;
use core_external\external_single_structure;
use core_external\external_multiple_structure;
use local_offermanager\persistent\offer_model;
use moodle_exception;

defined('MOODLE_INTERNAL') || die();

/**
 * Class offer_audience_external
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class offer_audience_external extends external_api
{

    /**
     * Define os parâmetros do método.
     *
     * @return external_function_parameters
     */
    public static function get_audiences_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'offerid' => new external_value(PARAM_INT, 'ID da oferta', VALUE_DEFAULT, 0),
        ]);
    }

    /**
     * Retorna a lista de todos os públicos-alvo e os IDs dos públicos-alvo atuais da oferta.
     *
     * @param int $offerid ID da oferta.
     * @return array
     * @throws moodle_exception
     */
    public static function get_audiences(int $offerid): array
    {
        global $DB;

        self::validate_parameters(
            self::get_audiences_parameters(),
            [
                'offerid' => $offerid
            ]
        );

        $all_audiences = $DB->get_records(
            'local_audience_audiences',
            null,
            '',
            'id, name'
        );

        if ($offerid === 0) {
            return [
                'all_audiences' => $all_audiences,
                'current_audience_ids' => [],
            ];
        }

        $offer = offer_model::get_record(['id' => $offerid]);

        if (!$offer) {
            throw new moodle_exception('error:offer_not_found', 'local_offermanager');
        }

        $current_audience_ids = $offer->get_audience_ids();

        return [
            'all_audiences' => $all_audiences,
            'current_audience_ids' => $current_audience_ids,
        ];
    }

    /**
     * Define a estrutura de retorno do método.
     *
     * @return external_single_structure
     */
    public static function get_audiences_returns(): external_single_structure
    {
        return new external_single_structure([
            'all_audiences' => new external_multiple_structure(
                new external_single_structure([
                    'id' => new external_value(PARAM_INT, 'ID do público-alvo'),
                    'name' => new external_value(PARAM_TEXT, 'Nome do público-alvo'),
                ]),
                'Lista de todos os públicos-alvo'
            ),
            'current_audience_ids' => new external_multiple_structure(
                new external_value(PARAM_INT, 'ID do público-alvo'),
                'IDs dos públicos-alvo atuais da oferta'
            ),
        ]);
    }

    /**
     * Define os parâmetros do método.
     *
     * @return external_function_parameters
     */
    public static function update_audiences_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'offerid' => new external_value(PARAM_INT, 'ID da oferta', VALUE_REQUIRED),
            'audienceids' => new external_multiple_structure(
                new external_value(PARAM_INT, 'ID do público-alvo'),
                'Lista de IDs dos públicos-alvo',
                VALUE_REQUIRED
            ),
        ]);
    }

    /**
     * Atualiza os públicos-alvo de uma oferta.
     *
     * @param int $offerid ID da oferta.
     * @param array $audienceids Lista de IDs dos públicos-alvo.
     * @return bool
     * @throws moodle_exception
     */
    public static function update_audiences(int $offerid, array $audienceids): bool
    {
        self::validate_parameters(self::update_audiences_parameters(), [
            'offerid' => $offerid,
            'audienceids' => $audienceids,
        ]);

        $offer = offer_model::get_record(['id' => $offerid]);

        if (!$offer) {
            throw new moodle_exception('error:offer_not_found', 'local_offermanager');
        }

        return $offer->update_audiences($audienceids);
    }

    /**
     * Define a estrutura de retorno do método.
     *
     * @return external_value
     */
    public static function update_audiences_returns(): external_value
    {
        return new external_value(PARAM_BOOL, 'Retorna true se houve algum tipo de atualização.');
    }
}
