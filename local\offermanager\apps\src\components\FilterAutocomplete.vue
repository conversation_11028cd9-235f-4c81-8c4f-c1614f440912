<template>
  <div class="filter-autocomplete-container">
    <label v-if="label" class="filter-label" :class="{ required }" :id="`${uniqueId}-label`">{{ label }}</label>

    <div class="autocomplete-wrapper">
      <div class="input-container" :style="{ maxWidth: inputMaxWidthStyle }">
        <div class="input-wrapper" :class="{ 'has-search-icon': hasSearchIcon }">
          <input
            type="text"
            class="form-control"
            :placeholder="placeholder"
            v-model="searchQuery"
            :disabled="disabled"
            :aria-expanded="isOpen"
            :aria-owns="`${uniqueId}-listbox`"
            :aria-labelledby="label ? `${uniqueId}-label` : undefined"
            :aria-autocomplete="'list'"
            :aria-controls="`${uniqueId}-listbox`"
            role="combobox"
            tabindex="0"
            @keydown="!disabled && handleKeydown"
            @focus="!disabled && handleFocus"
            @input="handleInput"
            @click="!disabled && openDropdown()"
            @blur="handleBlur"
            ref="inputElement"
          />
          <i v-if="hasSearchIcon" class="fas fa-search search-icon"></i>
        </div>

        <div
          v-if="isOpen"
          class="dropdown-menu show"
          :id="`${uniqueId}-listbox`"
          role="listbox"
          tabindex="-1"
        >
          <template v-if="displayItems.length > 0">
            <div
              v-for="(item, index) in displayItems"
              :key="item.value === '__ALL__' ? '__ALL__' : item.value"
              class="dropdown-item"
              :id="`${uniqueId}-option-${index}`"
              role="option"
              :aria-selected="selectedIndex === index"
              :tabindex="selectedIndex === index ? 0 : -1"
              :class="{
                active: selectedIndex === index,
                selected: item.value !== '__ALL__' && (Array.isArray(modelValue)
                  ? modelValue.some(i => i.value === item.value)
                  : modelValue === item.value)
              }"
              @click="selectItem(item)"
              @keydown="handleOptionKeydown($event, item, index)"
              ref="optionElements"
            >
              {{ item.label }}
              <i v-if="item.value !== '__ALL__' && Array.isArray(modelValue) && modelValue.some(i => i.value === item.value)" class="fas fa-check"></i>
            </div>
          </template>
          <div v-else class="dropdown-item no-results">
            {{ noResultsText || 'Nenhum item disponível' }}
          </div>
        </div>
      </div>

      <div class="tags-container" v-if="Array.isArray(modelValue) && modelValue.length > 0">
        <FilterTags>
          <FilterTag
            v-for="item in modelValue"
            :key="item.value"
            @remove="removeItem(item)"
          >
            {{ item.label }}
          </FilterTag>
        </FilterTags>
      </div>
    </div>
  </div>
</template>

<script>
import FilterTag from './FilterTag.vue';
import FilterTags from './FilterTags.vue';

export default {
  name: 'FilterAutocomplete',

  components: {
    FilterTag,
    FilterTags
  },

  props: {
    modelValue: {
      type: [Array, String, Number],
      default: () => []
    },
    items: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: ''
    },
    label: {
      type: String,
      default: ''
    },
    width: {
      type: [Number, String],
      default: 'auto'
    },
    required: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    minChars: {
      type: Number,
      default: 3
    },
    showAllOption: {
      type: Boolean,
      default: false
    },
    inputMaxWidth: {
      type: [String, Number],
      default: null
    },
    autoOpen: {
      type: Boolean,
      default: true
    },
    noResultsText: {
      type: String,
      default: 'Nenhum item disponível'
    },
    hasSearchIcon: {
      type: Boolean,
      default: false
    },
  },

  emits: ['update:modelValue', 'select', 'select-all'],

  data() {
    return {
      searchQuery: '',
      isOpen: false,
      selectedIndex: -1,
      internalItems: [],
      uniqueId: `autocomplete-${Math.random().toString(36).substring(2, 9)}`,
      focusedOptionIndex: -1,
      blurTimeout: null
    }
  },

  computed: {
    displayItems() {
      let itemsToShow = this.internalItems;

      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        itemsToShow = this.internalItems.filter(item =>
          item.label.toLowerCase().includes(query)
        );
      }

      if (this.showAllOption && Array.isArray(this.modelValue)) {
        return [{ label: 'Todos', value: '__ALL__' }, ...itemsToShow];
      }

      return itemsToShow;
    },
    inputMaxWidthStyle() {
      if (!this.inputMaxWidth) {
        return null;
      }
      return typeof this.inputMaxWidth === 'number'
        ? `${this.inputMaxWidth}px`
        : this.inputMaxWidth;
    }
  },

  watch: {
    items: {
      handler(newItems) {
        this.internalItems = Array.isArray(newItems) ? [...newItems] : [];

        if (this.autoOpen && !this.disabled && this.internalItems.length > 0 && this.$refs.inputElement === document.activeElement) {
          this.isOpen = true;
        }
      },
      immediate: true,
      deep: true
    },

    searchQuery() {
      this.isOpen = true;
      this.selectedIndex = -1;
    }
  },

  methods: {
    handleFocus() {
      if (this.autoOpen && !this.disabled) {
        this.isOpen = true;
        this.selectedIndex = -1;
      }

      if (this.blurTimeout) {
        clearTimeout(this.blurTimeout);
        this.blurTimeout = null;
      }
    },

    openDropdown() {
      if (!this.disabled) {
        this.isOpen = true;
      }
    },

    handleBlur() {
      this.blurTimeout = setTimeout(() => {
        if (!this.$el.contains(document.activeElement)) {
          this.isOpen = false;
          this.selectedIndex = -1;
        }
      }, 150);
    },

    handleInput() {
      if (!this.disabled) {
        this.isOpen = true;
      }
    },

    selectItem(item) {
      if (item.value === '__ALL__') {
        if (Array.isArray(this.modelValue)) {
          this.$emit('select-all');
        }
        this.searchQuery = '';
        this.isOpen = false;
        this.selectedIndex = -1;

        this.$nextTick(() => {
          this.focusInput();
        });

        return;
      }

      if (Array.isArray(this.modelValue)) {
        const newValue = [...this.modelValue];
        const index = newValue.findIndex(i => i.value === item.value);

        if (index === -1) {
          newValue.push(item);
        } else {
          newValue.splice(index, 1);
        }

        this.$emit('update:modelValue', newValue);
      }
      else {
        this.$emit('update:modelValue', item.value);
        this.$emit('select', item);
      }

      this.searchQuery = '';
      this.isOpen = false;
      this.selectedIndex = -1;

      this.$nextTick(() => {
        this.focusInput();
      });
    },

    removeItem(item) {
      if (Array.isArray(this.modelValue)) {
        const newValue = this.modelValue.filter(i => i.value !== item.value);
        this.$emit('update:modelValue', newValue);
      } else {
        this.$emit('update:modelValue', '');
      }

      if (Array.isArray(this.modelValue)) {
        this.searchQuery = '';
        this.isOpen = false;
        this.selectedIndex = -1;
      }
      else {
        this.selectedIndex = -1;
        this.$nextTick(() => {
          this.isOpen = false;
        });
      }

      this.$nextTick(() => {
        this.focusInput();
      });
    },

    handleKeydown(event) {
      if (!this.isOpen && event.key !== 'Tab') {
        this.isOpen = true;
        return;
      }

      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault();
          this.selectedIndex = Math.min(this.selectedIndex + 1, this.displayItems.length - 1);
          this.focusOption(this.selectedIndex);
          break;

        case 'ArrowUp':
          event.preventDefault();
          this.selectedIndex = Math.max(this.selectedIndex - 1, -1);
          if (this.selectedIndex === -1) {
            this.focusInput();
          } else {
            this.focusOption(this.selectedIndex);
          }
          break;

        case 'Enter':
          event.preventDefault();
          if (this.selectedIndex >= 0) {
            this.selectItem(this.displayItems[this.selectedIndex]);
          }
          break;

        case 'Escape':
          event.preventDefault();
          this.isOpen = false;
          this.selectedIndex = -1;
          break;

        case 'Tab':
          if (this.isOpen && !event.shiftKey && this.displayItems.length > 0) {
            event.preventDefault();
            this.selectedIndex = 0;
            this.focusOption(0);
          }
          break;
      }
    },

    handleOptionKeydown(event, item, index) {
      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault();
          if (index < this.displayItems.length - 1) {
            this.selectedIndex = index + 1;
            this.focusOption(this.selectedIndex);
          }
          break;

        case 'ArrowUp':
          event.preventDefault();
          if (index > 0) {
            this.selectedIndex = index - 1;
            this.focusOption(this.selectedIndex);
          } else {
            this.selectedIndex = -1;
            this.focusInput();
          }
          break;

        case 'Enter':
        case ' ':
          event.preventDefault();
          this.selectItem(item);
          break;

        case 'Escape':
          event.preventDefault();
          this.isOpen = false;
          this.selectedIndex = -1;
          this.focusInput();
          break;

        case 'Tab':
          if (index < this.displayItems.length - 1) {
            event.preventDefault();
            this.selectedIndex = index + 1;
            this.focusOption(this.selectedIndex);
          } else if (event.shiftKey) {
            event.preventDefault();
            if (index > 0) {
              this.selectedIndex = index - 1;
              this.focusOption(this.selectedIndex);
            } else {
              this.selectedIndex = -1;
              this.focusInput();
            }
          }
          break;
      }
    },

    focusInput() {
      if (this.$refs.inputElement) {
        this.$refs.inputElement.focus();
      }
    },

    focusOption(index) {
      this.$nextTick(() => {
        if (this.$refs.optionElements && this.$refs.optionElements[index]) {
          this.$refs.optionElements[index].focus();
        }
      });
    },

    handleClickOutside(event) {
      if (!this.$el.contains(event.target)) {
        this.isOpen = false;
        this.selectedIndex = -1;
      }
    }
  },

  mounted() {
    document.addEventListener('click', this.handleClickOutside);

    if (this.autoOpen && !this.disabled && this.internalItems.length > 0) {
      this.$nextTick(() => {
        this.isOpen = true;
      });
    }
  },

  beforeUnmount() {
    document.removeEventListener('click', this.handleClickOutside);
  }
}
</script>

<style lang="scss" scoped>
.filter-autocomplete-container {
  position: relative;
  width: 100%;
}

.filter-label {
  font-size: 14px;
  color: #fff;
  margin-bottom: 8px;
  display: block;

  &.required::after {
    content: "*";
    color: #dc3545;
    margin-left: 4px;
  }
}

.autocomplete-wrapper {
  position: relative;
}

.input-container {
  position: relative;
  width: 100%;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;

  &.has-search-icon {
    .form-control {
      padding-right: 30px;
    }

    .search-icon {
      position: absolute;
      right: 12px;
      color: #51585e;
      font-size: 16px;
      pointer-events: none;
    }
  }
}

.form-control {
  width: 100%;
  padding: 10px 12px;
  font-size: 15px;
  line-height: 1.5;
  color: #fff;
  background-color: #2c3237;
  border: 1px solid #51585e;
  border-radius: 4px;
  transition: all 0.15s ease-in-out;

  &:focus {
    outline: none;
    border-color: #51585e;
    box-shadow: none;
  }

  &::placeholder {
    color: #51585e;
    opacity: 0.7;
  }

  &:disabled {
    background-color: #343a40;
    border-color: #495057;
    opacity: 0.75;
    cursor: not-allowed;
  }
}

.dropdown-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 9999;
  margin-top: 4px;
  padding: 0.5rem 0;
  background-color: #2c3237;
  border: 1px solid #51585e;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
  min-width: 100%;
  width: auto;
  max-width: 350px;

  &.show {
    display: block;
  }
}

.dropdown-item {
  padding: 0.375rem 0.75rem;
  color: #fff;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  outline: none;

  &:hover {
    background-color: #343a40;
  }

  &:focus {
    background-color: var(--primary);
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  }

  &.active {
    background-color: var(--primary);
  }

  &.selected {
    background-color: #343a40;

    &:focus {
      background-color: var(--primary);
    }
  }

  &.no-results {
    color: #adb5bd;
    font-style: italic;
    cursor: default;
    white-space: normal;
    word-wrap: break-word;
    text-align: center;
    padding: 10px;

    &:hover {
      background-color: transparent;
    }
  }

  i {
    margin-left: 8px;
    font-size: 0.875rem;
    color: var(--primary);
  }
}

.tags-container {
  margin-top: 8px;
  width: 100%;

  :deep(.filter-tags) {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
}
</style>
