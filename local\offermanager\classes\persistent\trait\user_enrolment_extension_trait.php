<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON>le is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\persistent\trait;

use local_offermanager\persistent\offer_extension_reason_model;
use local_offermanager\event\offer_user_enrol_extended;
use moodle_exception;
use context_offer_class;

defined('MOODLE_INTERNAL') || die();

/**
 * Trait user_enrolment_extension_trait
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
trait user_enrolment_extension_trait {
        /**
     * Checks if the user can request an extension for this enrolment.
     *
     * @return true|string True if the user can request an extension, string with error message otherwise.
     */
    public function can_request_extension(): bool|string
    {
        $offerclass = $this->get_offer_class();

        if (!$offerclass) {
            return get_string('error:offer_class_not_found', 'local_offermanager');
        }

        $ue = $this->get_user_enrolment();

        $classenddate = $offerclass->get_mapped_field('enddate');
        if ($ue->timeend != 0 && $classenddate && $ue->timeend >= $offerclass->get_mapped_field('enddate')) {
            return get_string('error:extension_invalid_timeend', 'local_offermanager');
        }

        $extensiondata = $offerclass->get_extension_data();
        if (!$extensiondata || !isset($extensiondata->allowed_situations) || !isset($extensiondata->max_requests)) {
            return get_string('error:extension_invalid_config', 'local_offermanager');
        }

        $allowed_situations = $extensiondata->allowed_situations ?? [];

        if (empty($allowed_situations) || !in_array($this->get('situation'), $allowed_situations)) {
            return get_string('error:extension_invalid_situation', 'local_offermanager', $this->get_situation_name());
        }

        $maxrequests = (int)($extensiondata->max_requests ?? 0);

        if ($maxrequests && $this->get('extensioncount') >= $maxrequests) {
            return get_string('error:extension_limit_reached', 'local_offermanager');
        }

        return true;
    }

    /**
     * Processes the extension request for this user enrolment.
     *
     * @param string|null $reason Optional reason for the extension request
     * @return array An array containing the status, message, and warning flag.
     * @throws moodle_exception If the extension cannot be processed.
     */
    public function process_extension(?string $reason = null): array
    {
        global $DB;

        $can_request = $this->can_request_extension();
        if ($can_request !== true) {
            throw new moodle_exception('error:extension_cannot_process', 'local_offermanager', '', $can_request);
        }

        $offerclass = $this->get_offer_class();
        $ue = $this->get_user_enrolment();
        $extensiondata = $offerclass->get_extension_data();

        $original_ue_timeend = $ue->timeend;
        $extension_period_seconds = (int)($extensiondata->period ?? 0);

        if ($extension_period_seconds <= 0) {
            throw new moodle_exception('error:extension_invalid_period', 'local_offermanager');
        }

        $new_timeend = $ue->timeend + $extension_period_seconds;
        $warning_message = null;
        $is_warning = false;

        $class_enddate = (int)$offerclass->get_mapped_field('enddate');
        if ($class_enddate != 0 && $new_timeend > $class_enddate) {
            $new_timeend = $class_enddate;
            $warning_message = get_string('warning:extension_limited_by_class_enddate', 'local_offermanager', $extensiondata);
            $is_warning = true;
        }

        $ue->timemodified = time();
        $ue->timeend = $new_timeend;

        $DB->update_record('user_enrolments', $ue);

        $this->set('extensioncount', $this->get('extensioncount') + 1);
        $this->save();

        if($reason) {
            $extensionreason = new offer_extension_reason_model();
            $extensionreason->set('offeruserenrolid', $this->get('id'));
            $extensionreason->set('ueid', $ue->id);
            $extensionreason->set('reason', $reason ?: get_string('extension_reason_default', 'local_offermanager')); // Usar razão fornecida ou string padrão
            $extensionreason->save();
        }

        $event = offer_user_enrol_extended::instance($this);
        $event->trigger();

        $success_message = get_string('success:extension_granted', 'local_offermanager', $extensiondata);

        return [
            'success' => true,
            'message' => $warning_message ?? $success_message,
            'is_warning' => $is_warning
        ];
    }

    /**
     * Verifica se o usuário pode ver o modal de extensão.
     *
     * @return bool True se o usuário pode ver o modal, false caso contrário.
     */
    public function can_view_extension_modal()
    {
        $offerclass = $this->get_offer_class();

        if(!$offerclass) {
            return false;
        }

        $enabledenrolperiod = (bool) $offerclass->get_mapped_field('enableenrolperiod');

        if(!$enabledenrolperiod) {
            return false;
        }

        $enableextension = (bool) $offerclass->get_mapped_field('enableextension');

        if(!$enableextension) {
            return false;
        }

        $timeend = (int) $this->get_field_from_user_enrolment('timeend');
        $now = time();
        $extensiondata = $offerclass->get_extension_data();
        $thresholdtime = $extensiondata->days_available;

        $time_can_see_modal = $timeend ? $timeend - $thresholdtime : 0;

        // O usuário pode ver o modal se estiver dentro do período permitido para solicitar extensão
        return $time_can_see_modal < $now;
    }
}
