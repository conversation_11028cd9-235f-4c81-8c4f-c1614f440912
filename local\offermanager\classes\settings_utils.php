<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager;

defined('MOODLE_INTERNAL') || die();

/**
 * Class settings_utils
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class settings_utils
{
    public static function get_type_options()
    {
        $type_options = get_config('local_offermanager', 'typeoptions');

        $types = explode("\n", $type_options);
        $types = array_map('trim', $types);
        return array_filter($types);
    }

    public static function get_type_default()
    {
        global $CFG;
        $default = get_config('local_offermanager', 'defaulttypeoption');
        return $default ? $default : false;
    }
}
