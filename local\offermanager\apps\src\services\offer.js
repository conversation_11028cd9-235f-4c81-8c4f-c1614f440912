import { ajax } from "@/helpers/moodle";

/**
 * Busca ofertas.
 * @param {object} params
 * @returns {Promise<any>}
 */
export async function fetchOffers(params = {}) {
  try {
    const response = await ajax("local_offermanager_fetch", {
      search_string: params.search || "",
      type: params.type || null,
      only_active: params.onlyActive === true,
      page: params.page || 1,
      per_page: params.perPage || 25,
      sort_by: params.sortBy || "name",
      sort_direction: params.sortDesc ? "DESC" : "ASC",
    });

    // Retorna a resposta completa para que o componente possa tratar
    // adequadamente a estrutura retornada pelo Moodle
    return response;
  } catch (error) {
    throw new Error(error.message || "Erro ao buscar ofertas");
  }
}

/**
 * Busca uma oferta pelo ID.
 * @param {int} id
 * @returns {Promise<any>}
 */
export async function getOffer(id) {
  try {
    const response = await ajax("local_offermanager_get", {
      id,
    });
    return response;
  } catch (error) {
    throw new Error(error.message || "Erro ao buscar oferta");
  }
}

/**
 * Salva uma oferta.
 * @param {object} offerData
 * @returns {Promise<any>}
 */
export async function saveOffer(offerData) {
  try {
    const response = await ajax("local_offermanager_save", {
      id: offerData.id || 0,
      name: offerData.name,
      description: offerData.description || "",
      type: offerData.type || "",
      audienceids: offerData.audiences || [],
    });
    return response;
  } catch (error) {
    throw new Error(error.message || "Erro ao salvar oferta");
  }
}

/**
 * Deleta uma oferta.
 * @param {int} id
 * @returns {Promise<any>}
 */
export async function deleteOffer(id) {
  try {
    const response = await ajax("local_offermanager_delete", {
      id,
    });
    return response;
  } catch (error) {
    throw new Error(error.message || "Erro ao excluir oferta");
  }
}

/**
 * Busca tipos de oferta.
 * @param {object} params
 * @returns {Promise<any>}
 */
export async function getOfferTypes(params = {}) {
  try {
    const response = await ajax("local_offermanager_get_types", {
      search_string: params.search || "",
      only_active: params.onlyActive === true,
    });
    return response;
  } catch (error) {
    throw new Error(error.message || "Erro ao buscar tipos de oferta");
  }
}

/**
 * Busca opções de tipos de oferta.
 * @returns {Promise<any>}
 */
export async function getTypeOptions() {
  const response = await ajax("local_offermanager_get_type_options", {});

  if(response.error) {
    throw new Error(error.message || "Erro ao buscar opções de tipos");
  }

  return response;
}

/**
 * Adiciona um curso à oferta.
 * @param {int} offerId
 * @param {object} courseData
 * @returns {Promise<any>}
 */
export async function addCourseToOffer(offerId, courseData) {
  try {
    const response = await ajax("local_offermanager_add_course_to_offer", {
      offer_id: offerId,
      course: courseData,
    });
    return response;
  } catch (error) {
    throw new Error(error.message || "Erro ao adicionar curso à oferta");
  }
}

/**
 * Remove um curso da oferta.
 * @param {int} offerId
 * @param {int} courseId
 * @returns {Promise<any>}
 */
export async function removeCourseFromOffer(offerId, courseId) {
  try {
    const response = await ajax("local_offermanager_delete_course", {
      offercourseid: courseId,
    });
    return response;
  } catch (error) {
    throw new Error(error.message || "Erro ao remover curso da oferta");
  }
}

/**
 * Altera o status de um curso na oferta.
 * @param {int} offerId
 * @param {int} courseId
 * @param {boolean} active
 * @returns {Promise<any>}
 */
export async function toggleCourseStatus(offerId, courseId, active) {
  try {
    const response = await ajax("local_offermanager_set_course_status", {
      id: courseId,
      status: active ? 1 : 0, // Convertendo boolean para int
    });
    return response;
  } catch (error) {
    throw new Error(error.message || "Erro ao alterar status do curso");
  }
}

/**
 * Busca públicos-alvo.
 * @param {string} query
 * @returns {Promise<any>}
 */
export async function searchAudiences(query) {
  try {
    const response = await ajax("local_offermanager_get_audiences", {
      offerid: 0,
    });

    if (response?.data?.all_audiences) {
      // Filtra os públicos-alvo baseado na query
      const filteredAudiences = response.data.all_audiences.filter((audience) =>
        audience.name.toLowerCase().includes(query.toLowerCase())
      );

      return {
        items: filteredAudiences.map((audience) => ({
          id: audience.id,
          name: audience.name,
        })),
      };
    }

    return { items: [] };
  } catch (error) {
    throw new Error(error);
  }
}

/**
 * Atualiza públicos-alvo.
 * @param {int} offerId
 * @param {array} audienceIds
 * @returns {Promise<any>}
 */
export async function updateAudiences(offerId, audienceIds) {
  try {
    const response = await ajax("local_offermanager_update_audiences", {
      offerid: offerId,
      audienceids: audienceIds,
    });

    return response;
  } catch (error) {
    throw new Error(error);
  }
}

/**
 * Altera o status de uma oferta.
 * @param {int} offerId
 * @param {boolean} status
 * @returns {Promise<any>}
 */
export async function toggleOfferStatus(offerId, status) {
  try {
    const response = await ajax("local_offermanager_set_status", {
      id: offerId,
      status: !status, // inverte o status atual
    });
    return response;
  } catch (error) {
    throw new Error(error.message || "Erro ao alterar status da oferta");
  }
}

/**
 * Busca categorias.
 * @param {string} search - String para busca por nome de categoria
 * @param {int} offerId - ID da oferta (opcional)
 * @returns {Promise<any>}
 */
export async function getCategories(search = "", offerId = 0) {
  try {
    const response = await ajax("local_offermanager_get_categories", {
      search_string: search,
      offerid: offerId
    });
    return response;
  } catch (error) {
    throw new Error(error.message || "Erro ao buscar categorias");
  }
}

/**
 * Busca cursos por categoria (POTENCIAIS).
 * @param {int} offerId
 * @param {int} categoryId
 * @param {string} search
 * @param {int} page
 * @param {int} perPage
 * @returns {Promise<any>}
 */
export async function getCoursesByCategory(
  offerId,
  categoryId,
  search = "",
  page = 1,
  perPage = 20
) {
  try {
    console.log(
      `getCoursesByCategory - Parâmetros: offerId=${offerId}, categoryId=${categoryId}, search=${search}, page=${page}, perPage=${perPage}`
    );

    // Converte os parâmetros para números quando necessário
    const numericOfferId = parseInt(offerId, 10);
    const numericCategoryId = parseInt(categoryId, 10);
    const numericPage = parseInt(page, 10);
    const numericPerPage = parseInt(perPage, 10);

    // Verifica se os parâmetros são válidos
    if (
      isNaN(numericOfferId) ||
      isNaN(numericCategoryId) ||
      isNaN(numericPage) ||
      isNaN(numericPerPage)
    ) {
      console.error("Parâmetros inválidos para getCoursesByCategory");
    }

    const params = {
      offerid: numericOfferId,
      categoryid: numericCategoryId,
      search_string: search || "",
      page: numericPage,
      per_page: numericPerPage,
      exclude_courseids: [],
    };

    console.log("Chamando endpoint com parâmetros:", params);

    const response = await ajax(
      "local_offermanager_fetch_potential_courses",
      params
    );
    console.log("Resposta bruta do endpoint:", response);
    return response;
  } catch (error) {
    console.error("Erro em getCoursesByCategory:", error);
    throw new Error(error.message || "Erro ao buscar cursos");
  }
}

/**
 * Busca cursos da oferta por nome.
 * @param {int} offerId
 * @param {string} search
 * @returns {Promise<any>}
 */
export async function searchCurrentCoursesByName(offerId, search = "") {
  try {
    const response = await ajax("local_offermanager_fetch_current_courses", {
      offerid: offerId,
      categoryid: 0,
      search_string: search,
      exclude_courseids: [],
    });
    return response;
  } catch (error) {
    throw new Error(error.message || "Erro ao buscar cursos por nome");
  }
}

/**
 * Busca cursos da oferta por categoria.
 * @param {int} offerId
 * @param {int} categoryId
 * @returns {Promise<any>}
 */
export async function searchCurrentCoursesByCategory(offerId, categoryId) {
  try {
    const response = await ajax("local_offermanager_fetch_current_courses", {
      offerid: offerId,
      categoryid: categoryId,
      search_string: "",
      exclude_courseids: [],
    });
    return response;
  } catch (error) {
    throw new Error(error.message || "Erro ao buscar cursos por categoria");
  }
}

/**
 * Adiciona cursos à oferta.
 * @param {int} offerId
 * @param {array} courseIds
 * @returns {Promise<any>}
 */
export async function addCoursesToOffer(offerId, courseIds) {
  try {
    const response = await ajax("local_offermanager_add_courses", {
      offerid: offerId,
      courseids: courseIds,
    });
    return response;
  } catch (error) {
    throw new Error(error.message || "Erro ao adicionar cursos à oferta");
  }
}

/**
 * Busca cursos atuais da oferta.
 * @param {int} offerId
 * @param {object} options
 * @returns {Promise<any>}
 */
export async function getCurrentCourses(offerId, options = {}) {
  try {
    if (options.sortBy === "name") {
      options.sortBy = "fullname";
    }

    if (options.sortBy === "turmasCount") {
      options.sortBy = "class_counter";
    }

    const response = await ajax("local_offermanager_get_current_courses", {
      offerid: offerId,
      only_active: options.onlyActive || false,
      courseids: options.courseIds || [],
      page: options.page || 1,
      per_page: options.perPage || 100,
      sort_by: options.sortBy || "id",
      sort_direction: options.sortDesc ? "DESC" : "ASC",
      course_search: options.courseSearch || "",  // Adicionar parâmetro de busca por curso
      category_search: options.categorySearch || ""  // Adicionar parâmetro de busca por categoria
    });
    return response;
  } catch (error) {
    throw new Error(error.message || "Erro ao buscar cursos da oferta");
  }
}

/**
 * Cria uma nova turma
 * @param {object} params
 * @returns {Promise<any>}
 */
export async function addClass(params) {
  try {
    // Lista de campos aceitos no objeto optional_fields
    const acceptedOptionalFields = [
      "enableenddate",
      "enddate",
      "enablepreenrolment",
      "preenrolmentstartdate",
      "preenrolmentenddate",
      "description",
      "enableenrolperiod",
      "enrolperiod",
      "minusers",
      "maxusers",
      "roleid",
      "enablereenrol",
      "reenrolmentsituations",
      "enableextension",
      "extensionperiod",
      "extensiondaysavailable",
      "extensionmaxrequests",
      "extensionallowedsituations",
    ];

    // Cria um novo objeto com apenas os campos aceitos
    const cleanParams = {
      optional_fields: {},
    };

    // Copia os campos principais - garantindo que offercourseid seja um número
    if (params.offercourseid) {
      cleanParams.offercourseid = parseInt(params.offercourseid);
    } else {
      console.error("offercourseid não está definido nos parâmetros");
    }

    if (params.classname) {
      cleanParams.classname = params.classname;
    } else {
      console.error("classname não está definido nos parâmetros");
    }

    if (params.startdate) {
      cleanParams.startdate = params.startdate;
    } else {
      console.error("startdate não está definido nos parâmetros");
    }

    if (params.teachers && Array.isArray(params.teachers)) {
      cleanParams.teachers = [...params.teachers];
    } else {
      console.warn(
        "teachers não está definido nos parâmetros ou não é um array"
      );
      cleanParams.teachers = []; // Valor padrão
    }

    if (params.enrol) {
      cleanParams.enrol = params.enrol;
    } else {
      console.error("enrol não está definido nos parâmetros");
    }

    // Copia apenas os campos aceitos e remove valores undefined, null, 0 ou string vazia
    if (params.optional_fields) {
      acceptedOptionalFields.forEach((field) => {
        if (field in params.optional_fields) {
          // Verifica se o valor não é undefined, null, 0 ou string vazia
          const value = params.optional_fields[field];

          // Campos numéricos que devem ser tratados especialmente
          const numericFields = [
            "enrolperiod",
            "extensionperiod",
            "extensiondaysavailable",
            "extensionmaxrequests",
            "minusers",
            "maxusers",
          ];

          // Se for um campo numérico e o valor for 0, null, undefined ou string vazia, não inclui
          if (numericFields.includes(field)) {
            if (
              value !== 0 &&
              value !== null &&
              value !== undefined &&
              value !== ""
            ) {
              cleanParams.optional_fields[field] = value;
            }
          }
          // Para campos booleanos, sempre inclui
          else if (typeof value === "boolean") {
            cleanParams.optional_fields[field] = value;
          }
          // Para arrays, inclui apenas se não for vazio
          else if (Array.isArray(value)) {
            if (value.length > 0) {
              cleanParams.optional_fields[field] = value;
            }
          }
          // Para outros tipos, inclui apenas se não for null, undefined ou string vazia
          else if (value !== null && value !== undefined && value !== "") {
            cleanParams.optional_fields[field] = value;
          }
        }
      });
    }

    // Verificar se todos os campos obrigatórios estão presentes
    const requiredFields = ["offercourseid", "classname", "startdate", "enrol"];
    const missingFields = requiredFields.filter((field) => !cleanParams[field]);

    if (missingFields.length > 0) {
      console.error("Campos obrigatórios ausentes no serviço:", missingFields);
      throw new Error(
        `Campos obrigatórios ausentes: ${missingFields.join(", ")}`
      );
    }

    const response = await ajax("local_offermanager_add_class", cleanParams);

    return response;
  } catch (error) {
    console.error("Erro ao criar turma:", error);
    throw new Error(error.message || "Erro ao criar turma");
  }
}

/**
 * Busca dados de uma turma
 * @param {int} offerclassid
 * @returns {Promise<any>}
 */
export async function getClass(offerclassid) {
  try {
    const response = await ajax("local_offermanager_get_class", {
      offerclassid,
    });
    return response;
  } catch (error) {
    throw new Error(error.message || "Erro ao buscar turma");
  }
}

/**
 * Busca dados de um curso
 * @param {int} offercourseid
 * @returns {Promise<any>}
 */
export async function getCourse(offercourseid) {
  try {

    const response = await ajax("local_offermanager_get_course", {
      offercourseid,
    });

    if (response.error) {
      return [];
    }

    return response;
  } catch (error) {
    throw new Error(error.message || "Erro ao buscar curso da oferta");
  }
}

/**
 * Busca dados de um curso de oferta
 * @param {int} offercourseid
 * @returns {Promise<any>}
 */
export async function getClasses(offercourseid) {
  try {
    // Usamos o endpoint de buscar turmas para obter informações do curso
    const response = await ajax("local_offermanager_get_classes", {
      offercourseid,
    });

    // Verificar se a resposta é um array vazio ou um objeto com data vazio
    if (Array.isArray(response) && response.length === 0) {
      console.log(`Curso ${offercourseid} não tem turmas (array vazio)`);
      return [];
    }

    if (
      Array.isArray(response) &&
      response.length > 0 &&
      response[0].error === false &&
      Array.isArray(response[0].data) &&
      response[0].data.length === 0
    ) {
      console.log(`Curso ${offercourseid} não tem turmas (data vazio)`);
      return [];
    }

    // Se chegou aqui, retorna a resposta original para processamento
    return response;
  } catch (error) {
    console.error(`Erro ao buscar turmas do curso ${offercourseid}:`, error);
    throw new Error(error.message || "Erro ao buscar curso da oferta");
  }
}

/**
 * Atualiza uma turma
 * @param {object} params
 * @returns {Promise<any>}
 */
export async function updateClass(params) {
  try {
    // Lista de campos aceitos no objeto optional_fields
    const acceptedOptionalFields = [
      "enableenddate",
      "enddate",
      "enablepreenrolment",
      "preenrolmentstartdate",
      "preenrolmentenddate",
      "description",
      "enableenrolperiod",
      "enrolperiod",
      "minusers",
      "maxusers",
      "roleid",
      "enablereenrol",
      "reenrolmentsituations",
      "enableextension",
      "extensionperiod",
      "extensiondaysavailable",
      "extensionmaxrequests",
      "extensionallowedsituations",
    ];

    // Cria um novo objeto com apenas os campos aceitos para atualização
    const cleanParams = {
      offerclassid: params.offerclassid,
      classname: params.classname,
      startdate: params.startdate,
      teachers: params.teachers,
      optional_fields: {},
    };

    // O campo enrol não deve ser enviado na atualização, apenas na criação
    // A API não espera esse parâmetro e retorna erro se ele for enviado

    // Copia apenas os campos aceitos e remove valores undefined, null, 0 ou string vazia
    if (params.optional_fields) {
      acceptedOptionalFields.forEach((field) => {
        if (field in params.optional_fields) {
          // Verifica se o valor não é undefined, null, 0 ou string vazia
          const value = params.optional_fields[field];

          // Campos numéricos que devem ser tratados especialmente
          const numericFields = [
            "enrolperiod",
            "extensionperiod",
            "extensiondaysavailable",
            "extensionmaxrequests",
            "minusers",
            "maxusers",
          ];

          // Se for um campo numérico e o valor for 0, null, undefined ou string vazia, não inclui
          if (numericFields.includes(field)) {
            if (
              value !== 0 &&
              value !== null &&
              value !== undefined &&
              value !== ""
            ) {
              cleanParams.optional_fields[field] = value;
            }
          }
          // Para campos booleanos, sempre inclui
          else if (typeof value === "boolean") {
            cleanParams.optional_fields[field] = value;
          }
          // Para arrays, inclui apenas se não for vazio
          else if (Array.isArray(value)) {
            if (value.length > 0) {
              cleanParams.optional_fields[field] = value;
            }
          }
          // Para outros tipos, inclui apenas se não for null, undefined ou string vazia
          else if (value !== null && value !== undefined && value !== "") {
            cleanParams.optional_fields[field] = value;
          }
        }
      });
    }

    console.log(
      "Campos enviados para a API de atualização:",
      Object.keys(cleanParams.optional_fields)
    );
    console.log(
      "Objeto completo enviado para a API de atualização:",
      cleanParams
    );

    // Remover o campo enrol se existir, pois a API não espera esse parâmetro na atualização
    if ("enrol" in cleanParams) {
      delete cleanParams.enrol;
    }

    const response = await ajax("local_offermanager_update_class", cleanParams);
    return response;
  } catch (error) {
    throw new Error(error.message || "Erro ao atualizar turma");
  }
}

/**
 * Exclui uma turma
 * @param {int} offerclassid
 * @returns {Promise<any>}
 */
export async function deleteClass(offerclassid) {
  try {
    const response = await ajax("local_offermanager_delete_class", {
      offerclassid,
    });
    return response;
  } catch (error) {
    throw new Error(error.message || "Erro ao excluir turma");
  }
}

/**
 * Busca professores potenciais para turma
 * @param {int} offercourseid
 * @param {int} offercourseid
 * @returns {Promise<any>}
 */
export async function getPotentialTeachers(offercourseid, offerclassid = 0, searchString = '', excludedUserids = []) {
  try {
    const response = await ajax("local_offermanager_get_potential_teachers", {
      offercourseid: offercourseid,
      search_string: searchString,
      offerclassid: offerclassid,
      excluded_userids: excludedUserids
    });
    return response;
  } catch (error) {
    throw new Error(error.message || "Erro ao buscar professores");
  }
}

/**
 * Busca lista de situações de matrícula
 * @returns {Promise<any>}
 */
export async function getSituationList() {
  try {
    const response = await ajax("local_offermanager_get_situation_list", {});

    if(response.error) {
      throw new Error(response.exception.message || "Erro ao buscar situações de matrícula");
    }

    return response;
  } catch (error) {
    throw new Error(error.message || "Erro ao buscar situações de matrícula");
  }
}

/**
 * Duplica uma turma
 * @param {int} offerclassid ID da turma a ser duplicada
 * @param {int} targetoffercourseid ID do curso de destino (obrigatório)
 * @returns {Promise<any>}
 */
export async function duplicateClass(offerclassid, targetoffercourseid) {
  try {
    if (!targetoffercourseid) {
      throw new Error(
        "É necessário especificar um curso de destino para duplicar a turma"
      );
    }

    // Garantir que os IDs sejam números
    const numericOfferClassId = parseInt(offerclassid, 10);
    const numericTargetOfferCourseId = parseInt(targetoffercourseid, 10);

    if (isNaN(numericOfferClassId) || isNaN(numericTargetOfferCourseId)) {
      throw new Error("IDs inválidos para duplicação de turma");
    }

    const params = {
      offerclassid: numericOfferClassId,
      targetoffercourseid: numericTargetOfferCourseId,
    };

    const response = await ajax("local_offermanager_duplicate_class", params);
    return response;
  } catch (error) {
    throw new Error(error.message || "Erro ao duplicar turma");
  }
}

/**
 * Busca cursos potenciais para duplicação de turma
 * @param {int} offerclassid ID da turma a ser duplicada
 * @returns {Promise<any>}
 */
export async function getPotentialDuplicationCourses(offerclassid) {
  try {
    // Converter para número para garantir que seja enviado como número
    const numericOfferClassId = parseInt(offerclassid, 10);

    if (isNaN(numericOfferClassId)) {
      throw new Error("ID da turma inválido");
    }

    // Usar diretamente o endpoint específico para duplicação
    const response = await ajax("local_offermanager_get_duplication_courses", {
      offerclassid: numericOfferClassId,
    });

    // Processar a resposta para garantir o formato correto
    let processedResponse;
    if (response && response.data && Array.isArray(response.data)) {
      processedResponse = response.data;
    }
    // Caso padrão: usar a resposta como está
    else {
      processedResponse = response;
    }

    // Garantir que cada curso tenha o ID correto (offercourseid) e informações de categoria
    const validatedResponse = Array.isArray(processedResponse)
      ? processedResponse.map((course) => {
        // Garantir que o ID seja o offercourseid (ID da relação entre oferta e curso)
        // O backend agora retorna o offercourseid como id e também como offercourseid explicitamente
        return {
          id: course.id, // Este já deve ser o offercourseid
          name: course.name || course.fullname,
          courseid: course.courseid || null,
          offercourseid: course.offercourseid || course.id, // Usar offercourseid explícito se disponível
          categoryid: course.categoryid || null,
          category_name: course.category_name || "",
        };
      })
      : [];

    return validatedResponse;
  } catch (error) {
    // Tentar uma abordagem alternativa se o endpoint específico falhar
    try {
      // Buscar a turma para obter o ID da oferta
      const classResponse = await ajax("local_offermanager_get_class", {
        id: parseInt(offerclassid, 10),
      });

      // Extrair o ID da oferta e o ID do curso atual
      let offerid, currentCourseId;

      if (classResponse && classResponse.data) {
        offerid = classResponse.data.offerid;
        currentCourseId = classResponse.data.offercourseid;
      } else if (classResponse) {
        offerid = classResponse.offerid;
        currentCourseId = classResponse.offercourseid;
      } else {
        throw new Error("Não foi possível determinar a oferta da turma");
      }

      // Buscar todos os cursos da oferta
      const coursesResponse = await ajax(
        "local_offermanager_get_offer_courses",
        {
          offerid: parseInt(offerid, 10),
        }
      );

      // Extrair os cursos da resposta
      let courses = [];

      if (coursesResponse && Array.isArray(coursesResponse.data)) {
        courses = coursesResponse.data;
      } else if (
        coursesResponse &&
        coursesResponse.data &&
        Array.isArray(coursesResponse.data.courses)
      ) {
        courses = coursesResponse.data.courses;
      } else if (Array.isArray(coursesResponse)) {
        courses = coursesResponse;
      }

      // Filtrar o curso atual
      const filteredCourses = courses.filter((course) => {
        const courseId = course.id || course.offercourseid;
        return courseId != currentCourseId; // Usar != para comparar strings e números
      });

      // Mapear para o formato esperado, incluindo informações de categoria
      const mappedCourses = filteredCourses.map((course) => ({
        id: course.id, // Este é o offercourseid (ID da relação entre oferta e curso)
        name: course.fullname || course.name,
        courseid: course.courseid || null,
        offercourseid: course.id, // Garantir que offercourseid esteja presente
        categoryid: course.categoryid || null,
        category_name: course.category_name || "",
      }));

      return mappedCourses;
    } catch (alternativeError) {
      throw new Error(error.message || "Erro ao buscar cursos para duplicação");
    }
  }
}

/**
 * Busca papéis disponíveis para o curso
 * @param {int} offercourseid
 * @returns {Promise<any>}
 */
export async function getCourseRoles(offercourseid) {
  try {
    const response = await ajax("local_offermanager_get_course_roles", {
      offercourseid,
    });

    if(response.error){
      throw new Error(error.message || "Erro ao buscar papéis do curso");
    }

    return response;
  } catch (error) {
    throw new Error(error.message || "Erro ao buscar papéis do curso");
  }
}

/**
 * Busca métodos de inscrição disponíveis
 * @param {boolean} enabled Retornar apenas plugins habilitados
 * @returns {Promise<any>}
 */
export async function getClassMethods(enabled = true) {
  try {
    const response = await ajax("local_offermanager_get_class_methods", {
      enabled,
    });
    return response;
  } catch (error) {
    throw new Error(error.message || "Erro ao buscar métodos de inscrição");
  }
}

/**
 * Altera o status de uma turma.
 * @param {int} classId
 * @param {boolean} active
 * @returns {Promise<any>}
 */
export async function toggleClassStatus(classId, active) {
  try {
    const response = await ajax("local_offermanager_set_class_status", {
      id: classId,
      status: active ? 1 : 0, // Convertendo boolean para int
    });
    return response;
  } catch (error) {
    throw new Error(error.message || "Erro ao alterar status da turma");
  }
}