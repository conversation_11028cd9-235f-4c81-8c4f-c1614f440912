<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Install script for Offer Manager
 *
 * Documentation: {@link https://moodledev.io/docs/guides/upgrade}
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

/**
 * Executed on installation of Offer Manager
 *
 * @return bool
 */
function xmldb_local_offermanager_install()
{
    global $DB;

    $dbman = $DB->get_manager();

    $table = new xmldb_table('enrol');

    $field = new xmldb_field('customint9', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'customint8');

    if (!$dbman->field_exists($table, $field)) {
        $dbman->add_field($table, $field);
    }

    // Criar os papéis personalizados
    xmldb_local_offermanager_create_custom_roles();

    return true;
}

/**
 * Cria os papéis personalizados para o plugin Offer Manager
 */
function xmldb_local_offermanager_create_custom_roles() {
    global $DB;

    // Verificar se o papel de Coordenador já existe
    $coordinator_role = $DB->get_record('role', array('shortname' => 'coordenador'));
    if (!$coordinator_role) {
        // Obter o papel de Gerente para basear o papel de Coordenador
        $manager_role = $DB->get_record('role', array('shortname' => 'manager'));
        if ($manager_role) {
            // Criar o papel de Coordenador baseado no Gerente
            $coordinator_role = create_role(
                'Coordenador',
                'coordenador',
                'Papel de coordenação com as mesmas permissões do Gerente',
                'manager'
            );

            // Copiar todas as capacidades do papel de Gerente para o papel de Coordenador
            role_cap_duplicate($manager_role->id, $coordinator_role);
        }
    }

    // Verificar se o papel de Editor de Cursos já existe
    $courseeditor_role = $DB->get_record('role', array('shortname' => 'editorcursos'));
    if (!$courseeditor_role) {
        // Obter o papel de Criador de Cursos para basear o papel de Editor de Cursos
        $coursecreator_role = $DB->get_record('role', array('shortname' => 'coursecreator'));
        if ($coursecreator_role) {
            // Criar o papel de Editor de Cursos baseado no Criador de Cursos
            $courseeditor_role = create_role(
                'Editor de Cursos',
                'editorcursos',
                'Pode criar e editar cursos, adicionar métodos de inscrição, mas não pode inscrever professores, estudantes ou criar turmas',
                'coursecreator'
            );

            // Copiar todas as capacidades do papel de Criador de Cursos para o papel de Editor de Cursos
            role_cap_duplicate($coursecreator_role->id, $courseeditor_role);

            // Remover capacidades específicas do papel de Editor de Cursos
            $capabilities_to_prohibit = array(
                'enrol/manual:enrol',      // Impedir a inscrição manual de usuários
                'enrol/manual:manage',     // Impedir o gerenciamento de inscrições
                'moodle/course:managegroups', // Impedir a criação de turmas
                'moodle/course:viewparticipants', // Impedir a visualização de participantes
                'moodle/role:assign'       // Impedir a atribuição de papéis como professor
            );

            foreach ($capabilities_to_prohibit as $capability) {
                // Verificar se a capacidade existe
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    // Proibir a capacidade para o papel de Editor de Cursos
                    assign_capability($capability, CAP_PROHIBIT, $courseeditor_role, context_system::instance());
                }
            }
        }
    }
}
